package com.cgg.dataentry.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.cgg.dataentry.model.LocDetailsEntryForm;
import com.cgg.dataentry.service.LocDetailsEntryService;

@Controller
@RequestMapping(value = "/locDetailsEntry")
public class LocDetailsEntryController {
	@Autowired
	private LocDetailsEntryService locEntryService;
	
	@RequestMapping(method = RequestMethod.GET)
    public String locDetails(Map<String, Object> model,HttpSession session)throws Exception {
		String userId=null;
		if(session!=null && session.getAttribute("userid")!=null) 
			userId=(String)session.getAttribute("userid");
	
	 if(userId==null || userId.equals("null") ) {
		 return  "redirect:/locDetails";
	  }
		List<LocDetailsEntryForm> recommendedList = new ArrayList<LocDetailsEntryForm>();
		List<LocDetailsEntryForm> hospitalList = new ArrayList<LocDetailsEntryForm>();
		List<LocDetailsEntryForm> districts = new ArrayList<LocDetailsEntryForm>();
		LocDetailsEntryForm locEntryForm = new LocDetailsEntryForm();  
		recommendedList = locEntryService.getRecommendedDetails();
		hospitalList = locEntryService.getHospitalList();
		districts = locEntryService.getDistricts();
		model.put("recommendedList",recommendedList);
		model.put("hospitalList",hospitalList);
		model.put("districts",districts);
        model.put("locEntryForm", locEntryForm);
         
        return "locDetailsEntry";
    }
	@RequestMapping(method = RequestMethod.POST)
    public String saveLocDetails(@ModelAttribute("locEntryForm") LocDetailsEntryForm locEntryForm,
            Map<String, Object> model,HttpServletRequest request) throws Exception {
		//System.out.println("ttttttttttttt");
		HttpSession session=null;
		String userId=null;
		session=request.getSession();
		if(session!=null && session.getAttribute("userid")!=null) 
			userId=(String)session.getAttribute("userid");
	
//	System.out.println("ttttttttttttt"+userId);
	 if(userId==null || userId.equals("null") ) {
		 return  "redirect:/locDetails";
	  }
	locEntryForm.setUserId(userId);
	locEntryForm.setIpAddress(request.getRemoteAddr());
	    String locNo=locEntryService.saveCmrfDetails(locEntryForm, model);
		  List<LocDetailsEntryForm> recommendedList = new ArrayList<LocDetailsEntryForm>();
		  List<LocDetailsEntryForm> hospitalList = new ArrayList<LocDetailsEntryForm>();
			//	String userId="cmrf1";	
			
		  recommendedList = locEntryService.getRecommendedDetails();
			hospitalList = locEntryService.getHospitalList();
		  model.put("recommendedList",recommendedList);
		  model.put("hospitalList",hospitalList);
		  locEntryForm=locEntryService.getLocLetterData(locNo);
		 // model.put("msg", msg); 
		  model.put("locEntryForm", locEntryForm);
		
		  
        return "/dataentry/locLetter";
        
    }
	@RequestMapping(value = "getLocData", method = RequestMethod.POST)
    public @ResponseBody Map<String, Object> getLocData(HttpServletRequest request,@RequestParam("locTokenVal")String locTokenVal,HttpServletResponse response) {
		System.out.println("getLocData");
		String locData=null;
		Map<String, Object> responseData = new HashMap<>();
		List<Map<String, Object>> cmrfDetailsByAadhar=null;
		
        try{
        	if(locTokenVal!=null) {
            	locData=locEntryService.getLocData(locTokenVal);
            	if(locData!=null) {
 		            String[] dataParts = locData.split(":#");
            	    if (dataParts.length > 2) {
		                responseData.put("patientName", dataParts[0]);
		                responseData.put("fatherName", dataParts[1]);
		                responseData.put("aadharNo", dataParts[2]);
		                responseData.put("mobileNo", dataParts[3]);
		                responseData.put("patDistrict", dataParts[4]);
		                responseData.put("address", dataParts[5]);
		                responseData.put("purpose", dataParts[6]);
		                responseData.put("assuredAmt", dataParts[7]);
		                responseData.put("recommendedBy", dataParts[8]);
		                responseData.put("vipLetterDate", dataParts[9]);
		                responseData.put("hospCode", dataParts[10]);
		                responseData.put("locNo", dataParts[11]);
		                responseData.put("status", dataParts[12]);
		               		   
		                String recommBy = dataParts.length > 8 ? dataParts[8] : "";		              
		                if ("999".equals(recommBy)) {
		                    if (dataParts.length > 13) {
		                        responseData.put("cmcoReferredBy", dataParts[13]);		                       
		                    }
		                }
		               		                
		                String aadhar = dataParts[2];
		                String locNo = dataParts[11];
		             
		                if (aadhar != null && !aadhar.trim().isEmpty() && !aadhar.equals("0") ) {		                  		             		                  
		                    cmrfDetailsByAadhar = locEntryService.getCmrfLocDetailsByAadhar(aadhar, locNo);
		                    responseData.put("totalCount", cmrfDetailsByAadhar.size());
		                    responseData.put("entries", cmrfDetailsByAadhar);		            
		                } else {
		                	 responseData.put("totalCount", 0);
			                 responseData.put("entries", new ArrayList<>()); 
		                }
		            }
		       
		        }else {
		           responseData=null;
		        }
           }
       }catch (Exception exception) {
            exception.printStackTrace();
		    responseData.put("error", "An error occurred while fetching the data.");
       }
      return responseData;
    }
	
}

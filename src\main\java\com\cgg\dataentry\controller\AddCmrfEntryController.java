package com.cgg.dataentry.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
 import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.cgg.common.CommonFunctions;
import com.cgg.common.UIDVerfication;
import com.cgg.dataentry.model.AddCmrfEntryForm;
import com.cgg.dataentry.service.AddCmrfEntryService;
import com.cgg.hospital.service.InwardCmrfService;


@Controller
@RequestMapping(value = "/addCMRFEntry")
public class AddCmrfEntryController {
	@Autowired
	private AddCmrfEntryService addCmrfEntryService;

	@Autowired
	private InwardCmrfService inwardService;
	
	@RequestMapping(method = RequestMethod.GET)
    public String addCmrfEntryForm(Map<String, Object> model,HttpServletRequest request,RedirectAttributes redirectAttributes)throws Exception {
		String userid=null;
		String roleId=null;
		HttpSession session=request.getSession();
		userid=(String)session.getAttribute("userid");
		roleId=(String)session.getAttribute("rolesStr");
		 if(userid==null || userid.equals("null") ) {
			 redirectAttributes.addFlashAttribute("msg", "Please try again");
			 return  "redirect:/addCmrfEntry";
		  }
		AddCmrfEntryForm addCmrfEntryForm = new AddCmrfEntryForm();  
		System.out.println("userid"+userid);
		if(userid.equals("cmrf_aps")) {
			//addCmrfEntryForm.setCmrfUser("true");	
			model.put("cmrfUser", true);
		}else {
			model.put("cmrfUser", false);
		}
		if(roleId!=null && roleId.equals("25")) {
			model.put("specialCaseToken", true);
		}else {
			model.put("specialCaseToken", false);
		}
				
		List<AddCmrfEntryForm> recommendedList = new ArrayList<AddCmrfEntryForm>();
		List<AddCmrfEntryForm> hospitalList = new ArrayList<AddCmrfEntryForm>();
		List<AddCmrfEntryForm> districts = new ArrayList<AddCmrfEntryForm>();
		recommendedList = addCmrfEntryService.getRecommendedDetails();
		hospitalList = addCmrfEntryService.getHospitalList();
		districts=addCmrfEntryService.getDistricts();
		model.put("recommendedList",recommendedList);
		model.put("hospitalList",hospitalList);
		model.put("districts",districts);
        model.put("addCmrfEntryForm", addCmrfEntryForm);
        addCmrfEntryForm.setPurpose("UNDERGONE TREATMENT FOR ILL HEALTH AT  DURING  /201");
        return "addCmrfEntry";
    }
	
	@RequestMapping(method = RequestMethod.POST)
    public String saveCmrfDetails(@ModelAttribute("addCmrfEntryForm") AddCmrfEntryForm addCmrfEntryForm,
            Map<String, Object> model,HttpServletRequest request,RedirectAttributes redirectAttributes) throws Exception {
		System.out.println("getAadharNo"+addCmrfEntryForm.getAadharNo());
		  addCmrfEntryForm.setIpAddress(request.getRemoteAddr());
		  HttpSession session=request.getSession();
		  String aadharNo=null;
		  Integer hospCode=null;
		  String userId=(String)session.getAttribute("userid");
		  if(userId!=null && !userId.equals("null") ) {
			  addCmrfEntryForm.setUserId(userId);
			  aadharNo=addCmrfEntryForm.getAadharNo().trim();
			  hospCode = Integer.parseInt(addCmrfEntryForm.getHospCode());
			  int cmrfType = addCmrfEntryForm.getCmrfType();
				if(CommonFunctions.validateData(addCmrfEntryForm.getAadharNo())) {
					if (!UIDVerfication.validateVerhoeff(aadharNo)) {
						String message = "Aadhaar is not valid.please enter valid aadhaar";
						redirectAttributes.addFlashAttribute("msg", message);
						 return "redirect:/addCMRFEntry";
					}
				}	
				if (cmrfType == 2) {
					if(CommonFunctions.validateData(addCmrfEntryForm.getAadharNo()) && CommonFunctions.validateData(addCmrfEntryForm.getHospCode())) {
						if (isAadhaarEnteredToday(aadharNo, hospCode, 2)) {
							String message = "Failed to insert. A record with this Aadhaar number and hospital already entered today.";
							redirectAttributes.addFlashAttribute("msg", message);
							return "redirect:/addCMRFEntry";
						}
					}	
				}
			  String msg=addCmrfEntryService.saveCmrfDetails(addCmrfEntryForm, model);
			/*
			 * List<AddCmrfEntryForm> recommendedList = new ArrayList<AddCmrfEntryForm>();
			 * List<AddCmrfEntryForm> hospitalList = new ArrayList<AddCmrfEntryForm>();
			 * List<AddCmrfEntryForm> districts = new ArrayList<AddCmrfEntryForm>();
			 * 
			 * recommendedList = addCmrfEntryService.getRecommendedDetails(); hospitalList =
			 * addCmrfEntryService.getHospitalList();
			 * districts=addCmrfEntryService.getDistricts();
			 * model.put("recommendedList",recommendedList);
			 * model.put("hospitalList",hospitalList); model.put("districts",districts);
			 */
			 List<AddCmrfEntryForm> OtherConsts = new ArrayList<AddCmrfEntryForm>();
		  if(addCmrfEntryForm.getRecommendedBy()!=null && addCmrfEntryForm.getRecommendedBy().equals("998")) {
		    			     OtherConsts=addCmrfEntryService.getOtherConsList();
		    		  }
		   model.put("otherConstList",OtherConsts);
		    // model.put("msg", msg);
			  redirectAttributes.addFlashAttribute("msg", msg);
	         model.put("addCmrfEntryForm", addCmrfEntryForm);
	      //   addCmrfEntryForm.setCmrfDate("");
	      //   addCmrfEntryForm.setYear("");;
	       
	         addCmrfEntryForm.setCmrfLoc("");
	         addCmrfEntryForm.setPatientIpNo("");
	         addCmrfEntryForm.setAadharNo("");
	         addCmrfEntryForm.setPatientName("");
	         addCmrfEntryForm.setHospCode("0");
	         addCmrfEntryForm.setAge("");
	         addCmrfEntryForm.setRecommendedBy("0");
	         addCmrfEntryForm.setCo("0");
	         addCmrfEntryForm.setFatherName("");
	         addCmrfEntryForm.setPatAddress("");
	         addCmrfEntryForm.setPatDistrict("0");
	         addCmrfEntryForm.setPatMandal("0");
	         addCmrfEntryForm.setRange("0");
	         addCmrfEntryForm.setReqAmt("");
	         addCmrfEntryForm.setPurpose("");
	         addCmrfEntryForm.setSancAmt("");
	         addCmrfEntryForm.setPaymentTo("0");
	         addCmrfEntryForm.setMobileNo("");
		  }else {
			  redirectAttributes.addFlashAttribute("msg", "Please try again");
		  }
       // return "addCmrfEntry";
         return "redirect:/addCMRFEntry";
    }
	
	@RequestMapping(value = "getMandals", method = RequestMethod.POST)
    public @ResponseBody String getMandals(HttpServletRequest request,@RequestParam("district_code")String districtCode,HttpServletResponse response) {
		System.out.println("getMandal");
        StringBuilder mainData = new StringBuilder();
        List<AddCmrfEntryForm> mandalList = new ArrayList<AddCmrfEntryForm>();
        try{
            mainData.append("<option value=''>--Select--</option>");
            if(districtCode!=null) {
            	mandalList=addCmrfEntryService.getMandals(districtCode);
                for(AddCmrfEntryForm tempDTO : mandalList) {
                    mainData.append("<option value='" + tempDTO.getMandalNo() + "'>" + tempDTO.getMandalName() + "</option>");     
                }
            }

        } catch (Exception exception) {
            exception.printStackTrace();
        }

        return mainData.toString();
    }
	@RequestMapping(value = "getInwardData", method = RequestMethod.POST)
	public @ResponseBody Map<String, Object> getInwardData(
		    HttpServletRequest request,
		    @RequestParam("mlaCmrfNo") String mlaCmrfNo,
		    HttpServletResponse response
		) {
		    Map<String, Object> responseData = new HashMap<>();

		    try {
		        if (mlaCmrfNo != null) {
		            String mlaCmrfData = addCmrfEntryService.getInwardData(mlaCmrfNo);
		            //System.out.println("data"+mlaCmrfData);
		            if(mlaCmrfData!=null) {
		            String[] dataParts = mlaCmrfData.split(":#");

		            if (dataParts.length > 3) {
		                responseData.put("recommendedBy", dataParts[0]);
		                responseData.put("patientName", dataParts[1]);
		                responseData.put("co", dataParts[2]);
		                responseData.put("aadharNo", dataParts[3]);
		                responseData.put("hospCode", dataParts[5]);
		                responseData.put("patientIpNo", dataParts[6]);
		                responseData.put("mobileNo", dataParts[7]);
		                responseData.put("patAddress", dataParts[8]);
		                responseData.put("patDistrict", dataParts[9]);
		                responseData.put("patMandal", dataParts[10]);
		                responseData.put("purpose", dataParts[11]);
		                responseData.put("age", dataParts[12]);
		                responseData.put("status", dataParts[13]);
		                responseData.put("oldFscNo", dataParts[14]);
		                responseData.put("newFscNo", dataParts[15]);
		                responseData.put("docVerAmt", dataParts[16]);
		                responseData.put("bankAccNo", dataParts[17]);
		                responseData.put("bankAccHolderName", dataParts[18]);
		                responseData.put("userId", dataParts[19]);
		                responseData.put("isSpecialFlg", dataParts[20]);
		                responseData.put("specialFlgRefBy", dataParts[21]);
		                responseData.put("tokenEnteredDate", dataParts[22]);

		                System.out.println("dataParts[22]--"+dataParts[22]);
		               
		                // Fetch additional entries if available
		                String aadhar = dataParts[3];
		                List<Map<String, Object>> cmrfDetailsByAadhar;

		                if (aadhar != null && !aadhar.trim().isEmpty() && !aadhar.equals("0")) {
		                    // Fetch details only if aadhar is valid
		                    cmrfDetailsByAadhar = addCmrfEntryService.getCmrfDetailsByAadhar(aadhar, mlaCmrfNo);
		                    responseData.put("totalCount", cmrfDetailsByAadhar.size());
		                    responseData.put("entries", cmrfDetailsByAadhar);
		                 //  System.out.println(cmrfDetailsByAadhar);
		                } else {
		                    responseData.put("totalCount", 0);
		                    responseData.put("entries", new ArrayList<>());  // Empty list
		                }

		            }
		           // System.out.println(responseData.toString());
		        }
		            else
		            	responseData=null;}
		    } catch (Exception exception) {
		        exception.printStackTrace();
		        responseData.put("error", "An error occurred while fetching the data.");
		    }
 		    return responseData;
		}

   
	@RequestMapping(value = "getCmrfLocData", method = RequestMethod.POST)
    public @ResponseBody String getCmrfLocData(HttpServletRequest request,@RequestParam("cmrfLoc")String cmrfLocNo,HttpServletResponse response) {
		System.out.println("getCmrfLocData");
		String cmrfLocData=null;
        try{
          //  mainData.append("<option value=''>--Select--</option>");
            if(cmrfLocNo!=null) {
            	cmrfLocData=addCmrfEntryService.getCmrfLocData(cmrfLocNo);
                
            }

        } catch (Exception exception) {
            exception.printStackTrace();
        }

        return cmrfLocData;
    }

	 @RequestMapping(value = "getOtherConsList", method = RequestMethod.POST)
    public @ResponseBody String getOtherConsList(HttpServletRequest request,HttpServletResponse response) {
		System.out.println("getOtherConsList");
        StringBuilder mainData = new StringBuilder();
        List<AddCmrfEntryForm> consList = new ArrayList<AddCmrfEntryForm>();
        try{
           mainData.append("<option value='0'>--Select--</option>");
            	consList=addCmrfEntryService.getOtherConsList();
        		System.out.println("getOtherConsList"+consList.size());

                for(AddCmrfEntryForm tempDTO : consList) {
                    mainData.append("<option value='" + tempDTO.getConstNo() + "'>" + tempDTO.getConstName() + "</option>");     
                
            }

        } catch (Exception exception) {
            exception.printStackTrace();
        }
		System.out.println("getOtherConsList"+mainData);

        return mainData.toString();

    }

	@PostMapping("getCmrfData")
    public @ResponseBody Map<String, Object> getCmrfData(
            HttpServletRequest request,
            @RequestParam("cmrfNo") String cmrfNo,
            HttpServletResponse response
    ) {
        Map<String, Object> cmrfData = new HashMap<>();

        try {
            if (!(cmrfNo.trim().isEmpty())) { 
                cmrfData = addCmrfEntryService.getCmrfData(cmrfNo);

				String aadhar = (String)cmrfData.get("aadharNo");
				String mlaCmrfNo = (String)cmrfData.get("inwardId");
				List<Map<String, Object>> cmrfDetailsByAadhar;

		        if (aadhar != null && !aadhar.trim().isEmpty() && !aadhar.equals("0")) {
		            // Fetch details only if aadhar is valid
		            cmrfDetailsByAadhar = addCmrfEntryService.getCmrfDetailsByAadhar(aadhar.trim(), mlaCmrfNo.trim());
		            cmrfData.put("totalCount", cmrfDetailsByAadhar.size());
		            cmrfData.put("entries", cmrfDetailsByAadhar);
		            //  System.out.println(cmrfDetailsByAadhar);
		        } else {
		            cmrfData.put("totalCount", 0);
		            cmrfData.put("entries", new ArrayList<>());  // Empty list
		        }
            } else {
                cmrfData.put("error", "CMRF Number cannot be empty.");
            }
        } catch (Exception e) {
            e.printStackTrace();
            cmrfData.put("error", "An error occurred while fetching the data.");
        }
        return cmrfData;
    }

	public @ResponseBody Boolean isAadhaarEnteredToday(String aadhaarNo, Integer hospCode, Integer type) throws Exception {
		// System.out.println("Type : " + type);
		// System.out.println("Aadhar : " + aadhaarNo);
		// System.out.println("hospCode : " + hospCode);
		return inwardService.isAadhaarEnteredToday(aadhaarNo, hospCode, type);
    }
}

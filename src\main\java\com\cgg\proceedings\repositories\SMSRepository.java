package com.cgg.proceedings.repositories;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.cgg.dataentry.entities.MlaCmrf;

@Repository
public interface SMSRepository extends JpaRepository<MlaCmrf, String> {
    
    @Modifying
    @Transactional
    @Query(value = "Insert into cmrf_chq_sms(mobile_no, msg, sent_date, cmrf_no, sms_response) values (?1, ?2, CURRENT_TIMESTAMP, ?3, ?4)", nativeQuery = true)
    void insertIntoCmrfChqSms(Long mobile_no, String message, String cmrf_no, String sms_response);
}

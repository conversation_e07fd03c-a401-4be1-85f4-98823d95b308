package com.cgg.dataentry.entities;

import java.time.LocalDateTime;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "mla_cmrf")
public class MlaCmrfM {

    @Id
    @Column(name = "mla_cmrf_no")
    private String mlaCmrfNo;
    
    @Column(name = "income_no")
    private String incomeNo;

    @Column(name = "patient_name")
    private String patientName;

    @Column(name = "time_stamp")
    private Date timeStamp;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "recommended_by")
    private String recommendedBy;

    @Column(name = "status")
    private String status;

	@Column(name = "aadhar_no")
    private String aadharNo;

    @Column(name = "father_son_of")
    private String fatherSonOf;

    @Column(name = "cmrf_no")
    private String cmrfNo;

    @Column(name = "cmrf_priority")
    private String cmrfPriority;

    @Column(name = "entered_on")
    private LocalDateTime enteredOn;

    @Column(name = "delete_flag")
    private boolean deleteFlag ;

    @Column(name = "updated_on")
    private LocalDateTime updatedOn;

    @Column(name = "patient_ip")
    private String patientIp;

    @Column(name = "hosp_code")
    private Integer hospCode;

    @Column(name = "patient_ip_status")
    private String patientIpStatus;

    @Column(name = "rej_reasons")
    private String rejReasons;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "verified_by_deo")
    private String verifiedByDeo;

    @Column(name = "deo_verified_date")
    private LocalDateTime deoVerifiedDate;

    @Column(name = "deo_rej_reasons")
    private String deoRejReasons;

    @Column(name = "admission_no")
    private String admissionNo;

    @Column(name = "hos_verified_date")
    private LocalDateTime hosVerifiedDate;

    @Column(name = "patient_ip_upd_date")
    private LocalDateTime patientIpUpdDate;

    @Column(name = "pat_address")
    private String patAddress;

    @Column(name = "pat_district")
    private Integer patDistrict;

    @Column(name = "pat_mandal")
    private Integer patMandal;

    @Column(name = "mobile_no")
    private Long mobileNo;

    @Column(name = "age")
    private String age;

    @Column(name = "purpose")
    private String purpose;

    @Column(name = "pincode")
    private Integer pincode;

    @Column(name = "ip_address")
    private String ipAddress;

    @Column(name = "patient_ip_updated_by")
    private String patientIpUpdatedBy;

    @Column(name = "deo_verified_by")
    private String deoVerifiedBy;

    @Column(name = "old_fsc_no")
    private String oldFscNo;

    @Column(name = "new_fsc_no")
    private String newFscNo;

    @Column(name = "gender")
    private String gender;

    @Column(name = "upload_path")
    private String uploadPath;

    @Column(name = "hard_copy_received")
    private Boolean hardCopyReceived;
    
    @Column(name="bank_dist")
    private String bankDistrict;
    
    @Column(name = "bank_name")
    private String bankName;
    
    @Column(name = "bank_acc_no")
    private String bankAccNo;
    
    @Column(name="bank_ifsc")
    private String bankIfsc;
    
    @Column(name = "bank_branch")
    private String bankBranch;
 
 
    
    
    @Column(name = "token_hash")
    private String tokenHash;
    
       
    @Column(name = "deo_upd_dt")
    private LocalDateTime deoUpdDt;
      
    @Column(name = "cheque_dt")
    private LocalDateTime chequeDt;
    
    
    
    
    
    
    
    
    public String getTokenHash() {
		return tokenHash;
	}

	public void setTokenHash(String tokenHash) {
		this.tokenHash = tokenHash;
	}

	public LocalDateTime getDeoUpdDt() {
		return deoUpdDt;
	}

	public void setDeoUpdDt(LocalDateTime deoUpdDt) {
		this.deoUpdDt = deoUpdDt;
	}

	public LocalDateTime getChequeDt() {
		return chequeDt;
	}

	public void setChequeDt(LocalDateTime chequeDt) {
		this.chequeDt = chequeDt;
	}
 
 

	public String getBankDistrict() {
		return bankDistrict;
	}

	public void setBankDistrict(String bankDistrict) {
		this.bankDistrict = bankDistrict;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBankAccNo() {
		return bankAccNo;
	}

	public void setBankAccNo(String bankAccNo) {
		this.bankAccNo = bankAccNo;
	}

	public String getBankIfsc() {
		return bankIfsc;
	}

	public void setBankIfsc(String bankIfsc) {
		this.bankIfsc = bankIfsc;
	}

	public String getBankBranch() {
		return bankBranch;
	}

	public void setBankBranch(String bankBranch) {
		this.bankBranch = bankBranch;
	}

	public String getIncomeNo() {
		return incomeNo;
	}

	public void setIncomeNo(String incomeNo) {
		this.incomeNo = incomeNo;
	}

	public String getMlaCmrfNo() {
		return mlaCmrfNo;
	}

	public void setMlaCmrfNo(String mlaCmrfNo) {
		this.mlaCmrfNo = mlaCmrfNo;
	}

	public String getPatientName() {
		return patientName;
	}

	public void setPatientName(String patientName) {
		this.patientName = patientName;
	}

	public Date getTimeStamp() {
		return timeStamp;
	}

	public void setTimeStamp(Date timeStamp) {
		this.timeStamp = timeStamp;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getRecommendedBy() {
		return recommendedBy;
	}

	public void setRecommendedBy(String recommendedBy) {
		this.recommendedBy = recommendedBy;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getAadharNo() {
		return aadharNo;
	}

	public void setAadharNo(String aadharNo) {
		this.aadharNo = aadharNo;
	}

	public String getFatherSonOf() {
		return fatherSonOf;
	}

	public void setFatherSonOf(String fatherSonOf) {
		this.fatherSonOf = fatherSonOf;
	}

	public String getCmrfNo() {
		return cmrfNo;
	}

	public void setCmrfNo(String cmrfNo) {
		this.cmrfNo = cmrfNo;
	}

	public String getCmrfPriority() {
		return cmrfPriority;
	}

	public void setCmrfPriority(String cmrfPriority) {
		this.cmrfPriority = cmrfPriority;
	}

	public LocalDateTime getEnteredOn() {
		return enteredOn;
	}

	public void setEnteredOn(LocalDateTime enteredOn) {
		this.enteredOn = enteredOn;
	}

	public boolean isDeleteFlag() {
		return deleteFlag;
	}

	public void setDeleteFlag(boolean deleteFlag) {
		this.deleteFlag = deleteFlag;
	}

	public LocalDateTime getUpdatedOn() {
		return updatedOn;
	}

	public void setUpdatedOn(LocalDateTime updatedOn) {
		this.updatedOn = updatedOn;
	}

	public String getPatientIp() {
		return patientIp;
	}

	public void setPatientIp(String patientIp) {
		this.patientIp = patientIp;
	}

	public Integer getHospCode() {
		return hospCode;
	}

	public void setHospCode(Integer hospCode) {
		this.hospCode = hospCode;
	}

	public String getPatientIpStatus() {
		return patientIpStatus;
	}

	public void setPatientIpStatus(String patientIpStatus) {
		this.patientIpStatus = patientIpStatus;
	}

	public String getRejReasons() {
		return rejReasons;
	}

	public void setRejReasons(String rejReasons) {
		this.rejReasons = rejReasons;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public String getVerifiedByDeo() {
		return verifiedByDeo;
	}

	public void setVerifiedByDeo(String verifiedByDeo) {
		this.verifiedByDeo = verifiedByDeo;
	}

	public LocalDateTime getDeoVerifiedDate() {
		return deoVerifiedDate;
	}

	public void setDeoVerifiedDate(LocalDateTime deoVerifiedDate) {
		this.deoVerifiedDate = deoVerifiedDate;
	}

	public String getDeoRejReasons() {
		return deoRejReasons;
	}

	public void setDeoRejReasons(String deoRejReasons) {
		this.deoRejReasons = deoRejReasons;
	}

	public String getAdmissionNo() {
		return admissionNo;
	}

	public void setAdmissionNo(String admissionNo) {
		this.admissionNo = admissionNo;
	}

	public LocalDateTime getHosVerifiedDate() {
		return hosVerifiedDate;
	}

	public void setHosVerifiedDate(LocalDateTime hosVerifiedDate) {
		this.hosVerifiedDate = hosVerifiedDate;
	}

	public LocalDateTime getPatientIpUpdDate() {
		return patientIpUpdDate;
	}

	public void setPatientIpUpdDate(LocalDateTime patientIpUpdDate) {
		this.patientIpUpdDate = patientIpUpdDate;
	}

	public String getPatAddress() {
		return patAddress;
	}

	public void setPatAddress(String patAddress) {
		this.patAddress = patAddress;
	}

	public Integer getPatDistrict() {
		return patDistrict;
	}

	public void setPatDistrict(Integer patDistrict) {
		this.patDistrict = patDistrict;
	}

	public Integer getPatMandal() {
		return patMandal;
	}

	public void setPatMandal(Integer patMandal) {
		this.patMandal = patMandal;
	}

	

	public Long getMobileNo() {
		return mobileNo;
	}

	public void setMobileNo(Long mobileNo) {
		this.mobileNo = mobileNo;
	}

	public String getAge() {
		return age;
	}

	public void setAge(String age) {
		this.age = age;
	}

	public String getPurpose() {
		return purpose;
	}

	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}

	

	public Integer getPincode() {
		return pincode;
	}

	public void setPincode(Integer pincode) {
		this.pincode = pincode;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	public String getPatientIpUpdatedBy() {
		return patientIpUpdatedBy;
	}

	public void setPatientIpUpdatedBy(String patientIpUpdatedBy) {
		this.patientIpUpdatedBy = patientIpUpdatedBy;
	}

	public String getDeoVerifiedBy() {
		return deoVerifiedBy;
	}

	public void setDeoVerifiedBy(String deoVerifiedBy) {
		this.deoVerifiedBy = deoVerifiedBy;
	}

	public String getOldFscNo() {
		return oldFscNo;
	}

	public void setOldFscNo(String oldFscNo) {
		this.oldFscNo = oldFscNo;
	}

	public String getNewFscNo() {
		return newFscNo;
	}

	public void setNewFscNo(String newFscNo) {
		this.newFscNo = newFscNo;
	}

	public String getGender() {
		return gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getUploadPath() {
		return uploadPath;
	}

	public void setUploadPath(String uploadPath) {
		this.uploadPath = uploadPath;
	}

	
	public Boolean getHardCopyReceived() {
		return hardCopyReceived;
	}

	public void setHardCopyReceived(Boolean hardCopyReceived) {
		this.hardCopyReceived = hardCopyReceived;
	}

	@Override
	public String toString() {
		return "MlaCmrf [mlaCmrfNo=" + mlaCmrfNo + ", incomeNo=" + incomeNo + ", patientName=" + patientName
				+ ", timeStamp=" + timeStamp + ", userId=" + userId + ", recommendedBy=" + recommendedBy + ", status="
				+ status + ", aadharNo=" + aadharNo + ", fatherSonOf=" + fatherSonOf + ", cmrfNo=" + cmrfNo
				+ ", cmrfPriority=" + cmrfPriority + ", enteredOn=" + enteredOn + ", deleteFlag=" + deleteFlag
				+ ", updatedOn=" + updatedOn + ", patientIp=" + patientIp + ", hospCode=" + hospCode
				+ ", patientIpStatus=" + patientIpStatus + ", rejReasons=" + rejReasons + ", updatedBy=" + updatedBy
				+ ", verifiedByDeo=" + verifiedByDeo + ", deoVerifiedDate=" + deoVerifiedDate + ", deoRejReasons="
				+ deoRejReasons + ", admissionNo=" + admissionNo + ", hosVerifiedDate=" + hosVerifiedDate
				+ ", patientIpUpdDate=" + patientIpUpdDate + ", patAddress=" + patAddress + ", patDistrict="
				+ patDistrict + ", patMandal=" + patMandal + ", mobileNo=" + mobileNo + ", age=" + age + ", purpose="
				+ purpose + ", pincode=" + pincode + ", ipAddress=" + ipAddress + ", patientIpUpdatedBy="
				+ patientIpUpdatedBy + ", deoVerifiedBy=" + deoVerifiedBy + ", oldFscNo=" + oldFscNo + ", newFscNo="
				+ newFscNo + ", gender=" + gender + ", uploadPath=" + uploadPath + ", hardCopyReceived="
				+ hardCopyReceived + ", bankDistrict=" + bankDistrict + ", bankName=" + bankName + ", bankAccNo="
				+ bankAccNo + ", bankIfsc=" + bankIfsc + ", bankBranch=" + bankBranch ;
	}
    
}


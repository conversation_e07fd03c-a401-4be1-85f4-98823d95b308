<%@ page language="java" import="java.util.*" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<%
String path = request.getContextPath();
//String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
String basePath = path+"/";
%>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Status of Application</title>
    <script src="${basePath}js/highcharts/highcharts.js"></script>
    <script src="${basePath}js/highcharts/treemap.js"></script>
    <script src="${basePath}js/highcharts/treegraph.js"></script>
    <script src="${basePath}js/highcharts/exporting.js"></script>
    <script src="${basePath}js/highcharts/accessibility.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">

    <style>
        .custom-label {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-size: 13px;
            min-width: 120px;
            justify-content: flex-start;
            word-wrap: break-word;
            white-space: normal; /* allow normal wrapping */
        }
        .custom-label i {
            font-size: 24px;
            margin-bottom: 5px;
            color: #563d7c; /* nice icon color */
        }
        .custom-label span {
            margin-top: 2px;
            word-break: break-word;
            white-space: normal;
        }
        .blurred {
            opacity: 0.4;
            filter: grayscale(70%);
        }
    </style>
</head>
<body>
    <div class="container-fluid complete_wrap">
        <div class="row" style="display: none;">
            Status of Application
        </div>
        <div style="width: 90%;" class="container" id="figure"></div>
    </div>

    <script>
        var activeStepId = 'Cheque printed';

        var treeData = [
            { id: 'Online Application Submitted', parent: '', icon: 'fa-file', step: 0 },
            { id: 'File Received By CMO', parent: 'Online Application Submitted', icon: 'fa-inbox', step: 1 },
            { id: 'Auto cancelled due non submission of documents to CMRF with in 90 days', parent: 'Online Application Submitted', icon: 'fa-ban', step: 2 },
            { id: 'Documents pending for verification at CMRF', parent: 'File Received By CMO', icon: 'fa-clock-o', step: 3 },
            { id: 'Retuned To People Representative due to inadequate details', parent: 'File Received By CMO', icon: 'fa-undo', step: 4 },
            { id: 'Received documents and sent for hospital verification', parent: 'File Received By CMO', icon: 'fa-hospital-o', step: 5 },
            { id: 'Rejected By CMRF', parent: 'File Received By CMO', icon: 'fa-times-circle', step: 6 },
            { id: 'Hospital blocked', parent: 'File Received By CMO', icon: 'fa-lock', step: 7 },
            { id: 'Approved by hospital and pending for Doctors verification at CMRF', parent: 'Received documents and sent for hospital verification', icon: 'fa-check-circle', step: 8 },
            { id: 'Pending At Hospital', parent: 'Received documents and sent for hospital verification', icon: 'fa-hourglass-half', step: 9 },
            { id: 'Rejected by hospital', parent: 'Received documents and sent for hospital verification', icon: 'fa-times', step: 10 },
            { id: 'Approved by Doctors at CMRF', parent: 'Approved by hospital and pending for Doctors verification at CMRF', icon: 'fa-user-md', step: 11 },
            { id: 'Pending At CMRF Doctors', parent: 'Approved by hospital and pending for Doctors verification at CMRF', icon: 'fa-stethoscope', step: 12 },
            { id: 'Rejected By CMRF Doctors', parent: 'Approved by hospital and pending for Doctors verification at CMRF', icon: 'fa-thumbs-down', step: 13 },
            { id: 'CMRF Number Issued', parent: 'Approved by Doctors at CMRF', icon: 'fa-hashtag', step: 14 },
            { id: 'Cheque printed', parent: 'CMRF Number Issued', icon: 'fa-money', step: 15 }
        ];

        var nodeMap = {};
        treeData.forEach(function(node) {
            nodeMap[node.id] = node;
        });

        // Build the active path from activeStepId up to root
        var activeNodeIds = new Set();
        var currentId = activeStepId;
        while (currentId) {
            activeNodeIds.add(currentId);
            var parentNode = nodeMap[currentId];
            currentId = parentNode ? parentNode.parent : null;
        }

        Highcharts.chart('figure', {
        chart: {
            spacingBottom: 30,
            marginRight: 120,
            height: 1300,
            inverted: true
        },
        title: {
            text: 'Application Status Tree'
        },
        series: [
            {
            type: 'treegraph',
            data: treeData,
            marker: {
                fillColor: '#ffffff',
            },
            dataLabels: {
                    align: 'center',
                    useHTML: true,
                    formatter: function() {
                        var isActive = activeNodeIds.has(this.point.id);

                        return '<div class="custom-label ' + (isActive ? '' : 'blurred') + '">' +
                                    '<i class="fa ' + this.point.icon + '"></i>' +
                                    '<span>' + this.point.id + '</span>' +
                               '</div>';
                    },
                    style: {
                        color: '#000000',
                        textOutline: '3px #ffffff',
                        whiteSpace: 'normal',
                        fontSize: '13px'
                    },
                    y: 4,
                    crop: false,
                    overflow: 'none',
                },
            }
        ]
        });
    </script>
</body>
</html>
package com.cgg.dataentry.service;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.ModelAttribute;
import com.cgg.dataentry.model.LocDetailsEntryForm;

public interface LocDetailsEntryService {
	public List<LocDetailsEntryForm> getRecommendedDetails() throws Exception;
	public List<LocDetailsEntryForm> getHospitalList() throws Exception;
	public String saveCmrfDetails(@ModelAttribute("locEntryForm") LocDetailsEntryForm locEntryForm,
            Map<String, Object> model) throws Exception;
	public String getLocData(String locTokenNo) throws Exception;
	public List<Map<String, Object>> getCmrfLocDetailsByAadhar(String uidNo,String locTokenNo) throws Exception;
	public LocDetailsEntryForm getLocLetterData(String locNo) throws Exception;
	public List<LocDetailsEntryForm> getDistricts() throws Exception;
	


}

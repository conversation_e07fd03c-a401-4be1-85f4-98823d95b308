<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>  
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>  
 
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<title>Update CMRF Entry</title>
<script type="text/javascript" src="js/common/commonValidations.js"></script>
<!-- Include jQuery -->
<script type="text/javascript" src="js/jquery-1.12.4.js"></script> 
 <script type="text/javascript" src="js/jquery-ui.js"></script>
 <link rel="stylesheet" href="css/jquery-ui.css">

  <link rel="stylesheet" href="resources/demos/style.css">
  
 
 <script type="text/javascript">
 $(document).ready(function() { 	
 if($("#recommendedBy").val()=="998"){
          var url = "updateCmrfEntry/getOtherConsList";
               setOtherConstValue($("#otherConHidval").val());
              $("#otherConsDiv").show();
         }
	    else{
	                 	 $("#otherConsDiv").hide();
	                  $("#otherConst").val("0");
	                  }
	              });
 var tsan=0;
 var flag=1;
 function amountcheck(){
  
    if(parseInt($("#sancAmt").val())>parseInt($("#reqAmt").val())){
         alert('Sorry santioned amount cannot be greater then requested amount');
         $("#sancAmt").val(tsan);
         return false;
    }
   // valuecheck();
 
}
function convertInWords() {
                var reqAmt=$("#reqAmt").val();
                reqAmt=Math.floor(reqAmt);
                var obStr=new String(reqAmt);
                numReversed=obStr.split("");
                actnumber=numReversed.reverse();

                if(Number(reqAmt) >=0){
                    //do nothing
                }
                else{
                    alert('wrong Number cannot be converted');
                    return false;
                }
                if(Number(reqAmt)==0){
                    document.getElementById('container').innerHTML=obStr+''+'Rupees Zero Only';
                    return false;
                }
                if(actnumber.length>9){
                    alert('Oops!!!! the Number is too big to covertes');
                    return false;
                }

                var iWords=["Zero", " One", " Two", " Three", " Four", " Five", " Six", " Seven", " Eight", " Nine"];
                var ePlace=[' Ten', ' Eleven', ' Twelve', ' Thirteen', ' Fourteen', ' Fifteen', ' Sixteen', ' Seventeen', ' Eighteen', ' Nineteen'];
                var tensPlace=['dummy', ' Ten', ' Twenty', ' Thirty', ' Forty', ' Fifty', ' Sixty', ' Seventy', ' Eighty', ' Ninety' ];

                var iWordsLength=numReversed.length;
                var totalWords="";
                var inWords=new Array();
                var finalWord="";
                j=0;
                for(i=0; i<iWordsLength; i++){
                    switch(i)
                    {
                        case 0:
                            if(actnumber[i]==0 || actnumber[i+1]==1 ) {
                                inWords[j]='';
                            }
                            else {
                                inWords[j]=iWords[actnumber[i]];
                            }
                            inWords[j]=inWords[j]+' Only';
                            break;
                        case 1:
                            tensConvertion();
                            break;
                        case 2:
                            if(actnumber[i]==0) {
                                inWords[j]='';
                            }
                            else if(actnumber[i-1]!=0 && actnumber[i-2]!=0) {
                                inWords[j]=iWords[actnumber[i]]+' Hundred and';
                            }
                            else {
                                inWords[j]=iWords[actnumber[i]]+' Hundred';
                            }
                            break;
                        case 3:
                            if(actnumber[i]==0 || actnumber[i+1]==1) {
                                inWords[j]='';
                            }
                            else {
                                inWords[j]=iWords[actnumber[i]];
                            }
                            if(actnumber[i+1] != 0 || actnumber[i] > 0){
                                inWords[j]=inWords[j]+" Thousand";
                            }
                            break;
                        case 4:
                            tensConvertion();
                            break;
                        case 5:
                            if(actnumber[i]==0 || actnumber[i+1]==1) {
                                inWords[j]='';
                            }
                            else {
                                inWords[j]=iWords[actnumber[i]];
                            }
                            if(actnumber[i+1] != 0 || actnumber[i] > 0){
                                inWords[j]=inWords[j]+" Lakh";
                            }
                            break;
                        case 6:
                            tensConvertion();
                            break;
                        case 7:
                            if(actnumber[i]==0 || actnumber[i+1]==1 ){
                                inWords[j]='';
                            }
                            else {
                                inWords[j]=iWords[actnumber[i]];
                            }
                            inWords[j]=inWords[j]+" Crore";
                            break;
                        case 8:
                            tensConvertion();
                            break;
                        default:
                            break;
                    }
                    j++;
                }

                function tensConvertion() {
                    if(actnumber[i]==0) {
                        inWords[j]='';
                    }
                    else if(actnumber[i]==1) {
                        inWords[j]=ePlace[actnumber[i-1]];
                    }
                    else {
                        inWords[j]=tensPlace[actnumber[i]];
                    }
                }
                inWords.reverse();
                for(i=0; i<inWords.length; i++) {
                    finalWord+=inWords[i];
                }
                var x =obStr;
                x=x.toString();
                var lastThree = x.substring(x.length-3);
                var otherNumbers = x.substring(0,x.length-3);
                if(otherNumbers != '')
                    lastThree = ',' + lastThree;
                var res = otherNumbers.replace(/\B(?=(\d{2})+(?!\d))/g, ",") + lastThree;
                document.getElementById('container').innerHTML=res+'<br>  '+finalWord;

                //alert("2");

            }

function getamount(){
   if($("#cmrfLoc").val()=="" || $("#cmrfLoc").val()=="0"){
        var req_amt=$("#reqAmt").val();
        var r_val=$("#range").val();
        if($("#recommendedBy").val()!="")
              {                
             
        
                    if(!isNaN(req_amt)){
                            var str=$( "#recommendedBy option:selected" ).text()
                            var res =str.substring(str.indexOf("(")+1, str.indexOf(")"))
                           
                            if ($("#recommendedBy").val() !== "" && 
                                ($("#recommendedBy").val() === "2" || $("#recommendedBy").val() === "77")) {
                            
                                // Convert date string to Date object for proper comparison
                                const tokenDateStr = $("#tokenEnteredDate").val();
                                const tokenDate = new Date(tokenDateStr.split("/").reverse().join("/")); // converts 'DD/MM/YYYY' -> 'YYYY/MM/DD'
                            
                                const comparisonDate = new Date("2025-06-12");
                                if (tokenDate > comparisonDate) {
                                    $("#sancAmt").val(parseInt(req_amt) * 0.5);
                                } else {
                                    $("#sancAmt").val(parseInt(req_amt) * 0.4);
                                }
                            }
                            else if($("#recommendedBy").val()!="" && res.toUpperCase()=="MINISTER")
                                {
                                    //alert("50%");
                                   $("#sancAmt").val(parseInt(req_amt)*(.5));
                                } 
                            //else if($("#recommendedBy").val()!="" && res.toUpperCase()=="TRS")
                              else if($("#recommendedBy").val()!="" && $("#recommendedBy").val()!="194" && res.toUpperCase()=="INC")
                                {
                                    //alert("40%");
                                  $("#sancAmt").val(parseInt(req_amt)*(.4));
                                   
                                } 
                              else if($("#recommendedBy").val()!="" && $("#recommendedBy").val()=="117" && res.toUpperCase()=="CPI")
                              {
                                  //alert("40%");
                                $("#sancAmt").val(parseInt(req_amt)*(.4));
                                 
                              }
                            //  else if($("#recommendedBy").val()=="999" || $("#recommendedBy").val()=="194")
                            else if($("#recommendedBy").val()=="999")
                              {
                                  //alert("60%");
                                 //changed cmco from 60% to %0% on 14/06/2024
                                $("#sancAmt").val(parseInt(req_amt)*(.5));
                                 
                              } 
                              else if($("#recommendedBy").val()=="194")
                              {
                                  //alert("60%");
                            	//alert("60%");
                                  //$("#sancAmt").val(parseInt(req_amt)*(.6));
                                  if(req_amt <50000){
                                  	  $("#sancAmt").val(req_amt);
  	
                                  }
                                  
                                  else if((req_amt>=50000) && (req_amt<=100000)){
                                  	
                                  	var sancAmt=parseInt(req_amt)*(.6);
                                  	//alert(sancAmt);
                                  	if(sancAmt<50000){
                                  	  $("#sancAmt").val(50000);
                                  	}else{
                                  		$("#sancAmt").val(sancAmt);
                                  	}
                                  }
                                else if((req_amt>100000) && (req_amt<=200000)){
                                      	
                                      	var sancAmt=parseInt(req_amt)*(.5);
                                      //	alert(sancAmt);
                                      	if(sancAmt<60000){
                                      	  $("#sancAmt").val(60000);
                                      	}else{
                                      		$("#sancAmt").val(sancAmt);
                                      	}
  	
                                  }
                                  else if(req_amt> 200000){
                                	  $("#sancAmt").val(100000);
  	
                                }
                                 
                              } 
                            else
                                {
                                    //alert("50%");
                                    $("#sancAmt").val(parseInt(req_amt)*(.3));
                                } 
                            if($("#recommendedBy").val()!="194"){
                                 if(parseInt($("#sancAmt").val())>60000)
                                        {
                                            $("#sancAmt").val("60000");
                                        }
                                         if(parseInt($("#sancAmt").val())<5000)
                                        {
                                           $("#sancAmt").val("5000");
                                        }
                                         if(parseInt(req_amt)<5000)
                                        {
                                             $("#sancAmt").val(req_amt);
                                        }
                            }
                       var modvalue=$("#sancAmt").val()%1000;
                        if(modvalue>750){	
                            $("#sancAmt").val(parseInt($("#sancAmt").val())-(parseInt($("#sancAmt").val())%1000));
                             $("#sancAmt").val(parseInt($("#sancAmt").val())+parseInt("1000"));
                        }
                        else if(modvalue>300){
                           $("#sancAmt").val(parseInt($("#sancAmt").val())-(parseInt($("#sancAmt").val())%1000));
                             $("#sancAmt").val(parseInt($("#sancAmt").val())+parseInt("500"));
                        }
                        else{
                              $("#sancAmt").val(parseInt($("#sancAmt").val())-(parseInt($("#sancAmt").val())%1000));
                           
                        }



                    }
                  
               
                tsan=$("#sancAmt").val();
                   }
                    else
                        {
                            alert("Please select Recommended By");
                             $("#reqAmt").val(0);
                             $("#recommendedBy").focus();
  
                        }
                    }
            }
function checkamount(){
	var sancAmt=$("#sancAmt").val();
	                 if($("#cmrfLoc").val()=="" || $("#cmrfLoc").val()=="0"){
	                 
	            
	                 if(isNaN(sancAmt)){
		                    $("#sancAmt").val("0");
		                    alert("Please Enter Numeric");
		                    $("#sancAmt").focus();
		                    alert("Please Enter Numeric");
		                    return false;
		                }
	                if(parseFloat(sancAmt)!= parseFloat(tsan)){
	                	 alert("Sorry Sanctioned Amount you have given is more then the norms");
	                	$("#sancAmt").val(tsan);
	                	 return false;
	                }
	               
	                                
	                
	        }
	                 return true;  
	            }
	            
	            
		function inittsan(){
		if(flag==1){
		tsan=$("#sancAmt").val();
		flag=0;
		}
		}
            /*function valuecheck(){
               var newwindowurl="/passVerify?mode1=verify";
                var newWindow;
                if(checkamount()){

                    newWindow = window.open(newwindowurl,"","z-lock=yes,width=500,height=300,scrollbars=no");	
                }else{
               // alert("1");
                return false;
                }
            }

*/
            

function getMadalsByDistId(distCode, mandalVal) {
        var dist = $("#patDistrict").val();
       
        $("#" + mandalVal).html('<option value="">Loading...</option>');
        var url = "addCMRFEntry/getMandals?district_code=" + dist;
        $.post(url, function(data, status) {
            $("#" + mandalVal).html(data);
        });
    }
function setMadalsByDistId(distCode, patMandal,mandalval) {
        var dist = $("#patDistrict").val();
       
        $("#" + patMandal).html('<option value="">Loading...</option>');
        var url = "addCMRFEntry/getMandals?district_code=" + dist;
        $.post(url, function(data, status) {
            $("#" + patMandal).html(data);
            $("#patMandal").val(mandalval);
            
        });
    }
function setOtherConstValue(value) {
 //   $("#otherConst").html('<option value="">Loading...</option>');
    var url = "updateCmrfEntry/getOtherConsList";
    $.post(url, function(data, status) {
        $("#otherConst").html(data);
        $("#otherConst").val(value);
        
    });
}
    function UpdateValue(newVal){
     $("#valueck").val(newVal); 
   
} 
    function getOtherConsList(recommendedVal) {
          if(recommendedVal=='998'){
           var url = "updateCmrfEntry/getOtherConsList";
           $.post(url, function(data, status) {
               $("#otherConst").html(data);
               $("#otherConsDiv").show();

           });
          }else{
        	  $("#otherConsDiv").hide();
          }
       }

</script>

 <script>
  $( function() {
    $( "#cmrfDate" ).datepicker({
			changeMonth: true,
			changeYear: true,
			//minDate:"01/06/2014",
			minDate:"01/06/2009",
			maxDate:new Date(),
			dateFormat:"dd/mm/yy",
			//yearRange: "-0:+0"
		});
		
  } );
  </script>
 
    <script>
  function getDetails()
{
   var cmrfVal=$("#Cmrfno").val()+$("#CmrfYr").val();
         //      var locTokenVal = $("#locToken").val();
      
        var url = "updateCmrfEntry/getCmrfData?cmrfVal=" + cmrfVal;
        $.post(url, function(data, status) {
          if(data!=null){
          var updateStr=data.split(":#");
          
         if(updateStr.length>1){
       
           if(updateStr[0]=='1' || updateStr[0]=='Y' || updateStr[0]=='y'){
         $('input:radio[id=exgratia1]').checked = true;
           document.getElementById('exgratia1').checked = true;
         
         }
        else {
         $('input:radio[id=exgratia2]').checked = true; 
           document.getElementById('exgratia2').checked = true;
         }
         
          if(updateStr[20]=='1' || updateStr[20]=='Y' || updateStr[20]=='y'){
         $('input:radio[id=Signed1]').checked = true;
         document.getElementById('Signed1').checked = true;

         }
        else  {
         $('input:radio[id=Signed2]').checked = true;
         document.getElementById('Signed2').checked = true;
         }
         
    
         $("#cmrfDate").val(updateStr[1]);
          $("#year").val(updateStr[2]);
      //    $("#cmrfLoc").val(updateStr[3]);
           $("#inwardId").val(updateStr[19]);
        //   alert(updateStr[19]);
           getBankDetails(updateStr[19]);
            // $("#patientIpNo").val(updateStr[3]);
            if(updateStr[3] == '0'){
              $("#aadharNo").val("");
            }else{
              $("#aadharNo").val(updateStr[3]);
            }
            $("#patientName").val(updateStr[4]);
           $("#hospCode").val(updateStr[5]);
           $("#age").val(updateStr[6]);
             $("#recommendedBy").val(updateStr[7]);
             if($("#recommendedBy").val()=="998"){
            	 getOtherConsList($("#recommendedBy").val());
             }
             $("#fatherName").val(updateStr[8]);
           $("#patAddress").val(updateStr[9]);
         $("#patDistrict").val(updateStr[10]);
//     	setMadalsByDistId(updateStr[10],'patMandal',updateStr[11]);
         $("#reqAmt").val(updateStr[12]);
         //  $("#recFrom").val(updateStr[14]);
            $("#purpose").val(updateStr[13]);
            tsan=updateStr[14];
             $("#sancAmt").val(updateStr[14]);
              $("#paymentTo").val(updateStr[15]);
             // alert(updateStr[17]);
             if(updateStr[17]!='0'){
                $("#cmpNo").val(updateStr[17]);
                }else{
                $("#cmpNo").val("");
                }
				//alert($("#recommendedBy").val());
             if($("#recommendedBy").val()=="998"){
           // 	 alert(updateStr[18]);
                 $("#otherConst").val(updateStr[18]);
                 setOtherConstValue(updateStr[18]);
                 $("#otherConsDiv").show();

                 }else{
                	 $("#otherConsDiv").hide();
                 $("#otherConst").val("0");
                 }
          $("#cmrfLoc").val(updateStr[20]);
          if(updateStr[20]!='0' && updateStr[20]!=''){
              $("#inwardId").val("");
              $("#sancAmt").prop('readonly', false);
              $("#impMark").hide();
              }
          if(updateStr[19]!='0' && updateStr[19]!=''){
              $("#cmrfLoc").val("");
              $("#sancAmt").prop('readonly', true);
              $("#impMark").show();
              }
          $("#bankAccNo").val(updateStr[21]);
          $("#bankAccHolName").val(updateStr[22] === "null" ? "" : updateStr[22]);
          if(updateStr[23] == 0){
            $("#mobileNo").val("");
          }else{
            $("#mobileNo").val(updateStr[23]);
          }
        
          alert(" please wait while loading");
                   }
          }
          if(data == ""){
		 alert("Invalid CMRF No");
		 return false;
		 }
        });
}
     function retrieveTokenData(tokenNo)
        {
                   //alert(document.forms[1].mla_cmrf_year.value);
                mlaCmrfNo=tokenNo+$("#inwardId").val();
                $("#mlaCmrfNo").val(mlaCmrfNo);
               // var dist = $("#patDistrict").val();
       
        var url = "updateCmrfEntry/getInwardData?mlaCmrfNo=" + mlaCmrfNo;
        $.post(url, function(data, status) {
        if(data != ''){
							//var returnelements = textToSplit.split("#");
						//alert("text to split - "+textToSplit);
                                                if(data=="Invalid MlA Inward Id.")
                                                    {
                                                     $("#mlaCmrfNo").val("");
                                                     $("#co").val("S/O");

                                                    }
						var rasf_string=data.split(":#");
                                                 
                                                 if(rasf_string.length>1)           
                                             {
                                            $("#recommendedBy").val(rasf_string[0]);
                                            $("#patientName").val(rasf_string[1]);

                                  var val="";
                                  if(rasf_string[2]!=null){
                                      
                                   val=rasf_string[2].split(" ");
                                      }
                                      var stringArray = new Array();
                                    for(var i =0; i < val.length; i++){
                                    stringArray.push(val[i]);
                                    if(i != val.length-1){
                                        stringArray.push(" ");
                                    }
                                }
                                var name="";
                                for(i=0;i<val.length;i++){
                                   if(i!==0) {
                                       
                                      name=name+val[i] ;
                                       if(i!==val.length-1){
                                          
                                       name=name+" ";
                                   }
                                   }
                                  
                                }
                                name=name.trim();
                                   //   $("#co").val(val[1]);
                                      $("#fatherName").val(name);               
                                      
                     $("#aadharNo").val(rasf_string[3]);               
			 		$("#hospCode").val(rasf_string[5]);
                        
                                         }
		 }
				
		 
        
        
        });
              
              }
                function retrieveCmrfLocData(cmrfLocNo)
        {
     
        var url = "updateCmrfEntry/getCmrfLocData?cmrfLoc=" + cmrfLocNo;
        $.post(url, function(data, status) {
        if(data != ''){
                 if(data=="Invalid LOC No.")
                          {
                        $("#cmrfLoc").val("");
                        alert("Invalid LOC No")
						return false;
                           }
						var rasf_string=data.split(":#");
                                                 
                  if(rasf_string.length>1)           
                                             {
                       $("#recommendedBy").val(rasf_string[0]);
						$("#patientName").val(rasf_string[1]);
						$("#co").val(rasf_string[2]);
						$("#fatherName").val(rasf_string[2]);
						$("#paymentTo").val("H");
						$("#patAddress").val(rasf_string[3]);
						$("#reqAmt").val(rasf_string[4]);
						$("#sancAmt").val(rasf_string[4]);
						$("#hospCode").val(rasf_string[5]);
						$("#purpose").val("UNDERGONE TREATMENT FOR "+rasf_string[6]);
                                      
                                   
                        
                                         }
		 }  
        });
              
           }
  	function updateDetails(){
  		
  		if($("#cmrfDate").val()==""){
			alert("Enter Date Received");
			$("#cmrfDate").focus();
			return false;
		}	
		if($("#year").val()==""){
			alert("Enter Year");
			$("#year").focus();
			return false;
		}	
		if($('input[id=exgratia1]:checked').val()==undefined && $('input[id=exgratia2]:checked').val()==undefined){
			alert("Select Exgratia[Y/N]");
			return false;
		}
		if($('input[id=Signed1]:checked').val()==undefined && $('input[id=Signed2]:checked').val()==undefined){
			alert("Select Signed by CM[Y/N]");
			return false;
		}
		if ($("#cmrfLoc").val()=="0" || ($("#cmrfLoc").val()=="")){
		if ($("#inwardId").val() == "" || $("#inwardId").val() == "0" || typeof $("#inwardId").val() == 'undefined') {
		    alert("Enter MLA Inward Id");
		    $("#inwardId").focus();
		    return false;
		}
		}

		/* if($("#patientIpNo").val()==""){
			alert("Enter IP(In Patient ID)");
			$("#patientIpNo").focus();
			return false;
		} */
    if($("#cmrfLoc").val()=="0" || ($("#cmrfLoc").val()=="")){
      if($("#aadharNo").val()==""){
        alert("Enter Aadhaar No");
        $("#aadharNo").focus();
        return false;
      }	
      if($("#mobileNo").val()==""){
        alert("Enter Mobile Number");
        $("#mobileNo").focus();
        return false;
      }
      if (!/^\d{10}$/.test($("#mobileNo").val().trim())) {
        alert("Please enter a valid 10-digit mobile number.");
        $("#mobileNo").focus();
				return false;
			}
    }
		if($("#patientName").val()==""){
			alert("Enter Patient Name");
			$("#patientName").focus();
			return false;
		}
		 if($("#hospCode").val()=="0"){
			alert("Select Hospital");
			$("#hospCode").focus();
			return false;
		} 
		if($("#age").val()==""){
			alert("Enter Age");
			$("#age").focus();
			return false;
		}
		if($("#recommendedBy").val()=="0"){
			alert("Select Recommended By");
			$("#recommendedBy").focus();
			return false;
		}
		if($("#recommendedBy").val()=="998"){
			if($("#otherConst").val()=="0" || $("#otherConst").val()=="" ||$("#otherConst").val()==null){
				alert("Select other Constituency");
				$("#otherConst").focus();
				return false;
			}
			
		}

		
		if($("#fatherName").val()==""){
			alert("Enter Guardian Name");
			$("#fatherName").focus();
			return false;
		}
		if($("#patAddress").val()==""){
			alert("EnterPatient Address");
			$("#patAddress").focus();
			return false;
		}
		if($("#patDistrict").val()=="0"){
			alert("Select District");
			$("#patDistrict").focus();
			return false;
		}
    // let bankAccNo = $("#bankAccNo").val().trim();
    // if (bankAccNo === "" || bankAccNo === "0") {
    //     alert("Enter Bank Account Number");
    //     $("#bankAccNo").focus();
    //     return false;
    // }

    // let bankAccHolName = $("#bankAccHolName").val().trim();
    // if (bankAccHolName === "" || bankAccHolName === "0") {
    //     alert("Enter Bank Account Holder Name");
    //     $("#bankAccHolName").focus();
    //     return false;
    // }
		
		/*if($("#patMandal").val()=="undefined" || $("#patMandal").val()=="0" ||$("#patMandal").val()==""){
			alert("Select Mandal");
			$("#patMandal").focus();
			return false;
		}*/
	/* 	if($("#range").val()=="0"){
			alert("Select Range");
			$("#range").focus();
			return false;
		} */
		if($("#reqAmt").val()==""){
			alert("Enter Amount Requested");
			$("#reqAmt").focus();
			return false;
		}
		/* if($("#recFrom").val()=="0"){
			alert("Select Received From");
			$("#recFrom").focus();
			return false;
		} */
		if($("#purpose").val()==""){
			alert("Enter Purpose");
			$("#purpose").focus();
			return false;
		}
		if($("#sancAmt").val()==""){
			alert("Enter Amount Sanctioned");
			$("#sancAmt").focus();
			return false;
		}else if(!checkamount()){
                    alert("Sorry Sanctioned Amount you have given is more then the norms");
			$("#sancAmt").focus();
                    return false;
                }
		
		
  		var obj= confirm("Are you sure want to update the details now?");
  		if(obj)
  			return true;
  	    else
  	    	return false;
  	//alert(obj);
  	}
  	 /* function getamount(){
  	 
  	  if($("#cmrfLoc").val()==""){
              if($("#recommendedBy").val()!="0"){
  			alert($("#reqAmt").val());
  			alert($("#reqAmt").val());
  			}
            }
            } */

  function getBankDetails(tokenNo) {
  /*  if (event) {
        event.preventDefault();
    }*/

    fetch("updateCmrfEntry/getTokenBankDetails?tokenNo=" + encodeURIComponent(tokenNo))
      .then(response => {
          if (response.ok) {
              return response.text().then(data => {
                  console.log("Response Data:", data);

                  // Split the response string based on ':#'
                  var data_string = data.split(':#');

                  // Set values in the respective fields
                  if (data_string.length >= 1) {
                      $("#bankAccNo").val(data_string[0]);
                      $("#bankAccHolName").val(data_string[1]);
                      $("#tokenEnteredDate").val(data_string[2]);
                  } else {
                      alert("Unexpected response format: " + data);
                  }
              });
          } else {
              return response.text().then(data => {
                  console.error("Error Response:", data);
                  alert("Something went wrong. Please try again.\n" + data);
              });
          }
      })
      .catch(error => {
          console.error("Network Error:", error);
          alert("Failed to communicate with the server. Please try again.\n" + error);
      });
    }
  </script>
</head>
<body>
	<div class="container complete_wrap">
						<div class="container text-center">
				<h3>Update CMRF Entry</h3></div>      
					<div class="container" style="margin-top:2px;margin-bottom: 20px;width:1050px;border:2px solid green">
			
				 <p style="color:red;font-size: 20px;font-weight: bold;">${msg}</p> 
				 <form:form action="updateCmrfEntry" method="POST" modelAttribute="updateCmrfEntryForm">
				
				<%--  <input type="hidden" id="valueck" value="n" name="valueck"/> 
							 <form:hidden path="cmrfUser" Value="${cmrfUser}" /> --%>
				<form:hidden path="cmrfUpdNo" Value="${cmrfUpdNo}" />
				 <form:hidden path="otherConHidval" Value="${otherConHidval}" />
         <input type="hidden" name="enteredOn" id="tokenEnteredDate">
				 <div class="row" style="margin-top:15px;">
                  <div class="col-sm-10">
                        <div class="form-group col-sm-2">
                        <label for="textfile-id">Sl No:</label>
                        </div>
                          <div class="form-group col-sm-3">
                        <form:input path="Cmrfno" class="form-control" autocomplete="off" id="Cmrfno"/>
                        </div>
                          <div class="form-group col-sm-5">                       
                        <form:input path="CmrfYr" class="form-control" autocomplete="off" id="CmrfYr" value="/CMRF/202"/>
                        </div>
                        <div class="form-group col-sm-2">
                         <input type="button" value="Get Details"  name="get" class="btn btn-success"  onclick="getDetails();">
                         </div>
                        
                      
                      </div>
                    </div>
                  <div class="row">
                  
                    
                    <div class="col-sm-6">
                      <div class="form-group radio_check_buttons">
                        <label for="radio-id">Exgratia[Y/N]:</label>
                        <input id="exgratia1" name="exgratia" type="radio" value="Y" class="form-control"  > <span>Yes</span>
						<input id="exgratia2" name="exgratia" type="radio" value="N" class="form-control" ><span>No</span>
                      </div>
                      </div>
                      <div class="col-sm-6">
                      <div class="form-group radio_check_buttons">
                        <label for="radio-id">Signed by CM[Y/N]:</label>
                        <input id="Signed1" name="Signed" type="radio" value="Y" class="form-control" > <span>Yes</span>
						<input id="Signed2" name="Signed" type="radio" value="N" class="form-control"><span>No</span>
                      </div>
                      </div>
                      <div class="col-sm-6">
                      <div class="form-group">
                        <label for="date-id">Date Received</label>
                        <form:input path="cmrfDate" id="cmrfDate" placeholder="DD/MM/YYYY" autocomplete="off" class="form-control" />
                      </div>
                    </div>
                    
                     <div class="col-sm-6">
                      <div class="form-group">
                        <label for="year-id">Year</label>
                        <form:input path="year" id="year"  class="form-control" readonly="true"/>
                      </div>
                    </div>
                  
                       <div class="col-sm-6">
                      <div class="form-group">
                        <label for="textfile-id">CMRF LOC:</label>
                        <form:input path="cmrfLoc" id="cmrfLoc"  class="form-control" onkeyup="uppercaseID(this);" onchange="retrieveCmrfLocData(this.value)"/>
                      </div>
                    </div>
                      <div class="col-sm-6">
                      <div class="form-group">
                        <label for="date-id">MLA Inward Id:</label>
                        
                       <%--  <form:input path="tokenNo" id="tokenNo"    onchange="retrieveTokenData(this.value)" onkeyup="intOnly(this);"/> --%>
                        <form:input path="inwardId" id="inwardId" onkeyup="uppercaseID(this);" onchange="getBankDetails(this.value)" />
                        <%-- <form:hidden path="mlaCmrfNo" id="mlaCmrfNo"/> --%>
                      </div>
                    </div></div>
                    <div class="row">
                   <div class="col-sm-6">
                      <div class="form-group">
                        <label for="date-id">IP(In Patient ID):</label>
                        <form:input path="patientIpNo" id="patientIpNo"  class="form-control" />
                      </div>
                    </div>
                    <div class="col-sm-6">
                      <div class="form-group">
                        <label for="date-id">Aadhaar No:<span style="color:red">*</span></label>
                        <form:input path="aadharNo" id="aadharNo"  class="form-control" onkeyup="intNumOnly(this);"/>
                      </div>
                    </div>
                    <div class="row">
                    <div class="col-sm-6">
                      <div class="form-group">
                        <label for="date-id">Patient Name:<span style="color:red">*</span></label>
                        <form:input path="patientName" id="patientName"  class="form-control" />
                      </div>
                    </div>
                    <div class="col-sm-6">
                      <div class="form-group">
                        <label for="select-id">Hospital:</label>
                         <form:select path="hospCode" class="form-control" autocomplete="off" id="hospCode">
						<form:option value="0">Select</form:option>
             				<form:options items="${hospitalList}" itemLabel="hospName" itemValue="hospCode"/>
						
						</form:select>
						</div>
                     </div>
                     </div>
                    <div class="row">
                    <div class="col-sm-6">
                      <div class="form-group">
                        <label for="date-id">Mobile Number:<span style="color:red; display: none;" id="impMark">*</span></label>
                        <form:input path="mobileNo" id="mobileNo"  class="form-control" onkeyup="intNumOnly(this);" maxlength="10"/>
                      </div>
                    </div>
                     </div>
                     <div class="row">
                      <div class="col-sm-6">
                      <div class="form-group">
                        <label for="date-id">Age:<span style="color:red">*</span></label>
                        <form:input path="age" id="age"  class="form-control" onkeyup="intNumOnly(this);"/>
                      </div>
                    </div>
                    <div class="col-sm-6">
                      <div class="form-group"  class="col-sm-2">
                        <label for="select-id">Guardian:<span style="color:red">*</span></label>
                       	<form:input path="fatherName" class="form-control" autocomplete="off" id="fatherName"/>
						
                      </div>
                      
                      </div>
                  
                    </div>
                       <div class="col-sm-6">
                      <div class="form-group">
                        <label for="select-id">Recommended By:<span style="color:red">*</span></label>
                        <form:select path="recommendedBy" class="form-control" autocomplete="off" id="recommendedBy" onchange="getamount();getOtherConsList(this.value);">
             				<form:option value="0">Select</form:option>
             				<form:options items="${recommendedList}" itemLabel="constName" itemValue="constNo"/>
             			</form:select>	
						
                      </div>
                    </div>
                    <div class="col-sm-6" style="display:none" id="otherConsDiv">
                      <div class="form-group">
                        <label for="select-id">Others:<span style="color:red">*</span></label>
                        <form:select path="otherConst" class="form-control" autocomplete="off" id="otherConst" >
             				<form:option value="0">Select</form:option>
             				<form:options items="${otherConstList}" itemLabel="constName" itemValue="constNo"/>
             			</form:select>	
						
                      </div>
                    </div>
                      <div class="col-sm-6">
                      <div class="form-group">
                        <label for="date-id">Patient Address:<span style="color:red">*</span></label>
                        <form:textarea path="patAddress" id="patAddress"  class="form-control"  maxlength="45"/>
                      </div>
                    </div>
                  </div>
                    <div class="col-sm-6" style="align:right">
                      <div class="form-group">
                        <label  for="select-id">Patient District:<span style="color:red">*</span></label>
                         <form:select path="patDistrict" class="form-control" autocomplete="off" id="patDistrict" onchange="getMadalsByDistId(this,'patMandal')">
             				<form:option value="0">Select</form:option>
             				<form:options items="${districts}" itemLabel="distName" itemValue="distNo"/>
             			</form:select>	
                      
                      </div>
                 </div>
                  <div class="row">
                   <div class="col-sm-6">
                      <div class="form-group">
                        <label for="select-id">Select Mandal:<span style="color:red">*</span></label>
                       	<form:select path="patMandal" autocomplete="off" id="patMandal" class="form-control" >
                			<form:option value="0">Select</form:option>
                			<%-- <form:option value="1">mandals</form:option> --%>
                			<form:options items="${mandals}" itemLabel="mandalName" itemValue="mandalNo"/>
                			
                		</form:select>
                      </div>
                    </div> 

                    <div class="col-sm-6">
                      <div class="form-group">
                        <label for="select-id">Bank Account Number:</label>
                       	<form:input path="bankAccNo" class="form-control" autocomplete="off" id="bankAccNo" onkeyup="intNumOnly(this);" maxlength="18" readonly="${userId != 'cmrf_aps' && userId != 'cmrf_aps2' && userId != 'cmrf_cgg'}" />
                      </div>
                    </div>
                    <div class="col-sm-6">
                      <div class="form-group">
                        <label for="select-id">Bank Account Holder Name:</label>
                         <form:input path="bankAccHolName" class="form-control" autocomplete="off" id="bankAccHolName" onkeyup="uppercaseWithSymbols(this);" maxlength="50" readonly="${userId != 'cmrf_aps' && userId != 'cmrf_aps2' && userId != 'cmrf_cgg'}" />
                      </div>
                    </div> 
                  
                     <div class="col-sm-6" class="col-md-3">
                      <div class="form-group">
                        <label for="textfile-id">Amount Requested:<span style="color:red">*</span></label>
                        <%-- <form:input path="reqAmt" class="form-control" autocomplete="off" id="reqAmt" onchange="getamount()"
                         onkeyup="intOnly(this);"/> --%>
                         <form:input path="reqAmt" class="form-control" autocomplete="off"
							onmouseover="showToolTip(event,document.getElementById('container').innerHTML);return false;"
							 onmouseout="hideToolTip()"   
							onkeyup="convertInWords();intOnly(this);"  maxlength="7"
								id="reqAmt" onchange="getamount()"  />
							<!-- <div class="form-group col-sm-2" style="width:1000px"> -->
						<span id="container" style="color:blue;font-size:14px;font-weight:bold;width:895px "></span>

					<!-- </div> -->	
                      </div>
                  </div>
                  
              
                    <div class="col-sm-6">
                      <div class="form-group">
                        <label for="textfile-id">Purpose:<span style="color:red">*</span></label>
                      <form:textarea path="purpose" class="form-control" autocomplete="off" id="purpose"/>
                      </div>
                    </div>
                  
                 <div class="col-sm-6">
                      <div class="form-group">
                      <label for="textfile-id">Amount Sanctioned:<span style="color:red">*</span></label>
                   
							<form:input path="sancAmt" class="form-control" readonly="true"
								autocomplete="off" id="sancAmt" onkeyup="intOnly(this);" onfocus="inittsan()" onchange="checkamount()"
								 maxlength="7"  />
                      </div>
                    </div>
				
                    
 			 <div class="col-sm-5">
                      <div class="form-group">
                        <label for="select-id">Payment To Hospital/Beneficiary:<span style="color:red">*</span></label>
                       	<form:select path="paymentTo" autocomplete="off" id="paymentTo" class="form-control">
                			<form:option value="B">Beneficiary</form:option>
                			<form:option value="H">Hospital</form:option>
                			
                		</form:select>
                      </div>
                    </div>
                   <div></div>
                  <div class="col-sm-5" style="margin-right:100px" >
                      <div class="form-group">
                        <label for="select-id">Cmp No:<span style="color:red">*</span></label>
                        <form:input path="cmpNo" id="cmpNo" readonly="true" class="form-control" />

                      </div>
                    </div> 
                	</div>
                	<div>
                    <div align="center"><input type="submit" value="Update" name="Update" class="btn btn-primary" onclick="return updateDetails()"/></div>
					<div class="empty-space-30" style="padding:15px;display: flex;"></div>
                
               	</div>
               	 <script type="text/javascript"> 
 
    var exg = '${updateCmrfEntryForm.exgratia}';
    var sig = '${updsign}';
    
   
if(exg=='Y'){
   $('input:radio[id=exgratia1]').checked = true; 
   document.getElementById('exgratia1').checked = true;
   }else{
   $('input:radio[id=exgratia2]').checked = true; 
 document.getElementById('exgratia2').checked = true;
    
   $("#exgratia2").val("N");
   }
   if(sig=='Y'){
   $('input:radio[id=Signed1]').checked = true; 
   
   document.getElementById('Signed1').checked = true;
   }else{
   $('input:radio[id=Signed2]').checked = true; 
   document.getElementById('Signed2').checked = true;
   }
  </script>
                </form:form> 
		</div>
			</div>
	
</body>
</html>

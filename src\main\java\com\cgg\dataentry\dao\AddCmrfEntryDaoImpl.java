package com.cgg.dataentry.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.cgg.common.CommonFunctions;
import com.cgg.common.CommonUtils;
import com.cgg.dataentry.model.AddCmrfEntryForm;
import com.cgg.dataentry.repositories.MlaInwardCmrfEditRepository;

@Repository
public class AddCmrfEntryDaoImpl implements AddCmrfEntryDao {
	
	@Autowired
	private DataSource dataSource;
	@Autowired
	private MlaInwardCmrfEditRepository mlaInwardCmrfEditRepository;
	
	
	public List<AddCmrfEntryForm> getRecommendedDetails() throws Exception{
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<AddCmrfEntryForm> addCmrfDetails = new ArrayList<AddCmrfEntryForm>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
		//	sql="select cno, cno||','||mlamp||coalesce(design,'')||coalesce('('||case when minister='Y' then 'Minister' else  party end||')','')|| ', '||cname as cname from  constituency  order by cno";
			sql="select cno, cno||','||coalesce(mlamp,'')||coalesce(design,'')||coalesce('('||case when minister='Y' then 'Minister' else  party end||')','')|| ', '||coalesce(cname,'') as cname from  constituency  where active_con=true order by cno";
			rs=st.executeQuery(sql);
			while(rs.next()) {
				AddCmrfEntryForm addCmrfEntry = new AddCmrfEntryForm();
				addCmrfEntry.setConstNo(rs.getString("cno"));
				addCmrfEntry.setConstName(rs.getString("cname"));
				addCmrfDetails.add(addCmrfEntry);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return addCmrfDetails;
	}

	public List<AddCmrfEntryForm> getHospitalList() throws Exception{
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<AddCmrfEntryForm> hospDetails = new ArrayList<AddCmrfEntryForm>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			sql="select hospcode, hospname||'('||recog||')' as hospname from  hospital where delete_flag='false' order by hospname";
			rs=st.executeQuery(sql);
			while(rs.next()) {
				AddCmrfEntryForm addCmrfEntry = new AddCmrfEntryForm();
				addCmrfEntry.setHospCode(rs.getString("hospcode"));
				addCmrfEntry.setHospName(rs.getString("hospname"));
				hospDetails.add(addCmrfEntry);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return hospDetails;
	}
	public List<AddCmrfEntryForm> getDistricts() throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<AddCmrfEntryForm> addCmrfDetails = new ArrayList<AddCmrfEntryForm>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			sql="select distno, distname from  district  order by distname";
			System.out.println("sql---"+sql);
			rs=st.executeQuery(sql);
			while(rs.next()) {
				AddCmrfEntryForm addCmrfEntry = new AddCmrfEntryForm();
				addCmrfEntry.setDistNo(rs.getString("distno"));
				addCmrfEntry.setDistName(rs.getString("distname"));
				addCmrfDetails.add(addCmrfEntry);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return addCmrfDetails;
	}

	@Override
	public String saveCmrfDetails(AddCmrfEntryForm formbean, Map<String, Object> model)throws Exception {
		// TODO Auto-generated method stub
		Connection	con=null;
		Statement	st=null;
		boolean finalFlag=false;
		boolean insFlag=false;
		boolean sancFlag=false;
		String mesg=null;
		ResultSet rs=null;
		String sql=null;
		String mlaupd=null;
		int seqNo = 0;
		try {
		con = dataSource.getConnection();
		st=con.createStatement();
		String userId=formbean.getUserId();	
		String qry=null;
		Statement stmt=null;
		stmt=con.createStatement();	
		con.setAutoCommit(false);
		int year = 0;
		String insquery=null;
		String cmrf_no=null;
		String mlaCmrfTokenNo=null;
		int count=0;
		String existsCmrfNo=null;
		synchronized (this) {
		Map<String,Object> insertMap = new HashMap<String,Object>();	
		//qry = "select max(int4(split_part(cmrf_no,'/',1))),EXTRACT(YEAR FROM now()) from cmrelief where   cmrf_dt>to_date('01/01/2019','dd/mm/yyyy');";
	//	qry = "select max(int4(split_part(cmrf_no,'/',1))),EXTRACT(YEAR FROM now()) from cmrelief where   cmrf_dt>to_date('07/12/2023','dd/mm/yyyy');";
		qry = "select max(int4(split_part(cmrf_no,'/',1))),EXTRACT(YEAR FROM now()) from cmrelief where   cmrf_dt>to_date('31/01/2024','dd/mm/yyyy');";

        rs = stmt.executeQuery(qry);          
         while (rs.next()) {
        	 seqNo = rs.getInt(1);
             year = rs.getInt(2);
         }
         seqNo = seqNo + 1;
         String currYear = new SimpleDateFormat("yyyy").format(new Date());
         cmrf_no=seqNo+"/CMRF/"+currYear;
         System.out.println("sign==="+formbean.getSigned());
         System.out.println("ex==="+formbean.getExgratia());

 		insertMap.put("cmrf_no","'"+cmrf_no+"'");
		
		insertMap.put("cmrf_dt","to_date('"+formbean.getCmrfDate()+"','dd/mm/yyyy')");
		insertMap.put("sanc_date","to_date('"+formbean.getCmrfDate()+"','dd/mm/yyyy')");

		
		if(CommonFunctions.validateData(formbean.getYear()))
			insertMap.put("year001","'"+formbean.getYear()+"'");
		
			insertMap.put("ex_gratia","'"+formbean.getExgratia()+"'");
		
			/*
			 * if(CommonFunctions.validateSelectBox(formbean.getIsReimbursement()))
			 * insertMap.put("is_reimbursement","'"+formbean.getIsReimbursement()+"'");
			 */
			insertMap.put("userid","'"+userId+"'");
			insertMap.put("sined","'"+formbean.getSigned()+"'");
		
			insertMap.put("cmrf_type","'"+formbean.getCmrfType()+"'");

			if (formbean.getCmrfType() == 3) {
				if(CommonFunctions.validateData(formbean.getOldCmrfNo()))
				insertMap.put("old_cmrf_no","'"+formbean.getOldCmrfNo().trim()+"'");
			}
		
			if (formbean.getCmrfType() == 1) {
				if(CommonFunctions.validateData(formbean.getCmrfLoc()))
					insertMap.put("cmrf_loc","'"+formbean.getCmrfLoc()+"'");
			}

			if (formbean.getCmrfType() == 2) {
				if(CommonFunctions.validateData(formbean.getMlaCmrfNo()))
					insertMap.put("inward_id","'"+formbean.getMlaCmrfNo()+"'");	
			}
		
		if(CommonFunctions.validateData(formbean.getPatientIpNo()))
			insertMap.put("patient_ip_no","'"+formbean.getPatientIpNo()+"'");
		
		if(CommonFunctions.validateData(formbean.getAadharNo()))
			insertMap.put("aadhar_no","'"+formbean.getAadharNo()+"'");
		
		if(CommonFunctions.validateData(formbean.getPatientName()))
			insertMap.put("pat_name","'"+formbean.getPatientName().trim()+"'");
		
		
			insertMap.put("hosp_code","'"+formbean.getHospCode()+"'");
		
		if(CommonFunctions.validateSelectBox(formbean.getRecommendedBy()))
			insertMap.put("recommended_by","'"+formbean.getRecommendedBy()+"'");

		if (formbean.getRecommendedBy().equals("998")) {
			insertMap.put("other_const", "'"+formbean.getOtherConst()+"'");
        }		
		if(CommonFunctions.validateData(formbean.getOldFscNo()))
			insertMap.put("old_fsc_no","'"+formbean.getOldFscNo() +"'");
		
		if(CommonFunctions.validateData(formbean.getNewFscNo()))	
			insertMap.put("new_fsc_no","'"+formbean.getNewFscNo() +"'");
		
		if(CommonFunctions.validateData(formbean.getAge()))
			insertMap.put("age","'"+formbean.getAge()+"'");
		
		if(CommonFunctions.validateData(formbean.getFatherName()))
			insertMap.put("father_son_of","'"+formbean.getCo()+" "+formbean.getFatherName()+"'");
		
		if(CommonFunctions.validateData(formbean.getPatAddress()))
			insertMap.put("pat_address","'"+formbean.getPatAddress()+"'");
		
		if(CommonFunctions.validateSelectBox(formbean.getPatDistrict()))
			insertMap.put("pat_district","'"+formbean.getPatDistrict()+"'");
		
		if(CommonFunctions.validateSelectBox(formbean.getPatMandal()))
			insertMap.put("pat_mandal","'"+formbean.getPatMandal()+"'");
		
		if(CommonFunctions.validate(formbean.getBankAccNo()))
			insertMap.put("bank_account_no","'"+formbean.getBankAccNo().trim()+"'");
		
		if(CommonFunctions.validate(formbean.getBankAccHolderName()))
			insertMap.put("bank_acc_hol_name","'"+formbean.getBankAccHolderName().trim()+"'");
		
		if(CommonFunctions.validateData(formbean.getReqAmt()))
			insertMap.put("req_amt","'"+formbean.getReqAmt()+"'");
		
		
	
	/*	if(CommonFunctions.validateSelectBox(formbean.getRecFrom()))
			insertMap.put("rec_from","'"+formbean.getRecFrom()+"'");*/
		
		if(CommonFunctions.validateData(formbean.getPurpose()))
			insertMap.put("purpose","'"+formbean.getPurpose()+"'");
		
		if(CommonFunctions.validateData(formbean.getSancAmt()))
			insertMap.put("sanc_amt","'"+formbean.getSancAmt()+"'");
		
		if(CommonFunctions.validateSelectBox(formbean.getPaymentTo()))
			insertMap.put("payment_to","'"+formbean.getPaymentTo()+"'");
		
		if(CommonFunctions.validateData(formbean.getIpAddress()))
			insertMap.put("ip_address","'"+formbean.getIpAddress()+"'");
		if(CommonFunctions.validateData(formbean.getMobileNo()))
			insertMap.put("mobile_no","'"+formbean.getMobileNo()+"'");
			insertMap.put("economy","1");
		
		//String insquery=null;
		//synchronized (this) {
		mlaCmrfTokenNo=formbean.getMlaCmrfNo();
		if (mlaCmrfTokenNo != null && !mlaCmrfTokenNo.isEmpty()) 
			count=mlaInwardCmrfEditRepository.mlaCMRFTokenCount(mlaCmrfTokenNo);
		if(count>0) {
			existsCmrfNo=mlaInwardCmrfEditRepository.mlaCMRFTokenNo(mlaCmrfTokenNo);
			mesg="Entered token number already exists with CMRF No - "+existsCmrfNo;
		}else {
			Map<String,Object> tmap = CommonFunctions.insertQuery(insertMap);
			if(tmap!=null && !tmap.isEmpty())
				 insquery = "insert into cmrelief ("+(tmap.get("colNames"))+") values ("+tmap.get("colValues")+")";
			System.out.println("insquery---"+insquery);
		//	String ipAddress="0.0.0.0";
			String ipAddress=formbean.getIpAddress();

			 insFlag = CommonUtils.insertQuery(con, insquery);
			 System.out.println("insFlag---"+insFlag);
			// System.out.println("getValueck---"+formbean.getValueck());
			/* if(formbean.getValueck().equals("y")) {
	               sql = "insert into sanc_amt_verification(userid,ip,proxy,appid) "
	              		+ "values('" + userId+ "','" +
	              		ipAddress + "','" + ipAddress + "','" + cmrf_no + "')";
	             sancFlag = CommonUtils.insertQuery(con, sql);

				}*/
		
	    if(insFlag) {
	    	if(formbean.getMlaCmrfNo()!=null && !formbean.getMlaCmrfNo().equals("0")&& !formbean.getMlaCmrfNo().equals("")){
	    		//    String mlaqry = "insert into mla_cmrf_log (mla_cmrf_no, patient_name, time_stamp, user_id, recommended_by, old_fsc_no, new_fsc_no, status, aadhar_no, father_son_of, cmrf_no,  cmrf_priority, entered_on, delete_flag,updated_on,logged_timestamp,logged_ipaddress,logged_by,logged_remarks)"
	            //      		+ " (select  mla_cmrf_no, patient_name, time_stamp, user_id, recommended_by, old_fsc_no, new_fsc_no, status, aadhar_no, father_son_of, cmrf_no,  cmrf_priority, entered_on, delete_flag,updated_on,now(),'" + formbean.getIpAddress() + "','" + userId + "','CMRF Entry' from mla_cmrf where mla_cmrf_no ='" + formbean.getMlaCmrfNo() + "')";
	            //      System.out.println("mla qry--" + mlaqry);
	            //      boolean insQuery = CommonUtils.insertQuery(con, mlaqry);
					 int cnt = mlaInwardCmrfEditRepository.insertMlaCmrfLog(formbean.getMlaCmrfNo(), "Updating the data in MLA_CMRF during Add CMRF Entry", userId, formbean.getIpAddress());
	                 if(cnt == 1) {
	                //	  sql="update mla_cmrf set status='3',patient_ip_status='true',verified_by_deo='true',cmrf_no='" +cmrf_no+"',updated_on=now(),updated_by='" + userId + "',ip_address='" + formbean.getIpAddress() + "' where mla_cmrf_no='" + formbean.getMlaCmrfNo()+ "' and status='1'";
						mlaupd="update mla_cmrf set status='3',cmrf_no='" +cmrf_no+"',cmrf_ent_dt=now(),cmrf_ent_by='" + userId + "', ";

						if (CommonFunctions.validate(formbean.getBankAccNo())) {
                            mlaupd = mlaupd + "bank_acc_no='" + formbean.getBankAccNo() + "', "; 
                        }
                        if (CommonFunctions.validate(formbean.getBankAccHolderName())) {
                            mlaupd = mlaupd + "bank_acc_holder_name='" + formbean.getBankAccHolderName() + "', "; 
                        }

						mlaupd = mlaupd + "cmrf_ent_ipaddr='" + formbean.getIpAddress() + "' where mla_cmrf_no='" + formbean.getMlaCmrfNo()+ "' ";
	                    System.out.println("MLA_CMRF Update Query : " + mlaupd);
	                    finalFlag=CommonUtils.updateQuery(con, mlaupd);
	                    con.commit();
	                 }
                }
	    	else {
	    		 con.commit();
		    	 mesg="Inserted Successfully.Cmrf No is:"+cmrf_no;
	    	}
	    	if(finalFlag) {
	    	 mesg="Inserted Successfully.Cmrf No is:"+cmrf_no;
	    	}
	    }else {
	    	 con.rollback();
	    	mesg="Insertion Failed";
	    }
		//System.out.println("qry---"+qry);
		}}
		}catch(Exception e) {
			 con.rollback();
			e.printStackTrace();
			mesg="Insertion Failed";
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		
		return mesg;
	}
	public List<AddCmrfEntryForm> getMandals(String distCode) throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<AddCmrfEntryForm> addCmrfDetails = new ArrayList<AddCmrfEntryForm>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			sql="select mcode, mname from  mandal where distcode='"+distCode + "' order by mname";
			System.out.println("sql---"+sql);
			rs=st.executeQuery(sql);
			while(rs.next()) {
				AddCmrfEntryForm addCmrfEntry = new AddCmrfEntryForm();
				addCmrfEntry.setMandalNo(rs.getString("mcode"));
				addCmrfEntry.setMandalName(rs.getString("mname"));
				addCmrfDetails.add(addCmrfEntry);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return addCmrfDetails;

}

	public String getInwardData(String mlaCmrfNo) throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		String statusMesg=null;
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
          // sql = "select status||':#'||recommended_by||':#'||patient_name||':#'||father_son_of"
           // 		//+ "||':#'||aadhar_no"
            //	    + "||':#'||hosp_code"
            //		+ "  as inwardStr from mla_cmrf  where mla_cmrf_no='" + mlaCmrfNo + "' and delete_flag=false";
         /*   sql = "select recommended_by||':#'||patient_name||':#'||father_son_of||':#'||COALESCE(aadhar_no,'0')||':#'||mla_cmrf_no||':#'||COALESCE(hosp_code,'0')||':#'||COALESCE(patient_ip,'0')||" +
           		"':#'||COALESCE(mobile_no,'0')||':#'||COALESCE(pat_address,'0')||':#'||COALESCE(pat_district,'0')||':#'||" +
           		" COALESCE(pat_mandal,'0')||':#'||COALESCE(purpose,'UNDERGONE TREATMENT FOR DURING  /202')||':#'||COALESCE(age,'0')||':#'||status || ':#' || COALESCE(old_fsc_no, '0') || ':#' || COALESCE(new_fsc_no, '0') as inwardStr from mla_cmrf  where mla_cmrf_no='" + mlaCmrfNo + "' and status='1' and delete_flag=false";*/
            
            sql = "select recommended_by||':#'||patient_name||':#'||father_son_of||':#'||COALESCE(aadhar_no,'0')||':#'||mla_cmrf_no||':#'||COALESCE(hosp_code,'0')||':#'||COALESCE(patient_ip,'0')||" +
               		"':#'||COALESCE(mobile_no,'0')||':#'||COALESCE(pat_address,'0')||':#'||COALESCE(pat_district,'0')||':#'||" +
               		" COALESCE(pat_mandal,'0')||':#'||COALESCE(purpose,'UNDERGONE TREATMENT FOR DURING  /202')||':#'||COALESCE(age,'0')||':#'||status || ':#' || COALESCE(old_fsc_no, '0') || ':#' || COALESCE(new_fsc_no, '0') || ':#' || COALESCE(doc_verif_amt , '0') || ':#' || COALESCE(bank_acc_no , '') || ':#' || COALESCE(bank_acc_holder_name , '') || ':#' || case when user_id ilike '%cmrf%' then 'null' else user_id end || ':#' || "
               		+ "  COALESCE(is_special_flg, 'false') || ':#' || "
               		+ "  COALESCE(special_flg_ref_by, 'null') || ':#' || to_char(entered_on,'dd/mm/yyyy') as inwardStr "
               		+ " from mla_cmrf "
               		+ " where mla_cmrf_no='" + mlaCmrfNo + "' and status in('1','10')  and verified_by_deo = 'true' and delete_flag=false";
			//sql="select status from mla_cmrf where mla_cmrf_no='" + mlaCmrfNo + "' and delete_flag=false";
			System.out.println("sql---"+sql);
			rs=st.executeQuery(sql);
			if(rs.next()) {
				statusMesg=rs.getString("inwardStr");				
			}
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			 CommonUtils.closeCon(con, st, rs);
		}
		return statusMesg;
}
	public List<Map<String, Object>> getCmrfDetailsByAadhar(String aadharNo, String mlaCmrfNo) throws Exception {
	    List<Map<String, Object>> entries = new ArrayList<>();
	    
	    String countQuery = "SELECT COUNT(*) FROM mla_cmrf WHERE aadhar_no = ? AND mla_cmrf_no <> ? AND entered_on >= CURRENT_DATE - INTERVAL '6 months' ";
	    try (Connection con = dataSource.getConnection();
	         PreparedStatement countStmt = con.prepareStatement(countQuery)) {
	        countStmt.setString(1, aadharNo);
	        countStmt.setString(2, mlaCmrfNo);

	        // Execute count query
	        try (ResultSet rs = countStmt.executeQuery()) {
	            if (rs.next() && rs.getInt(1) == 0) {
	                return entries; // Return empty list if no entries found
	            }
	        }

	        // If entries exist, fetch data
	        String dataQuery = "SELECT a.mla_cmrf_no, a.patient_name, a.father_son_of, " +
	                           "CASE WHEN s.status_id IN ('3') THEN s.status_name || ' - ' || COALESCE(a.cmrf_no, 'NA-') " +
	                           "WHEN s.status_id IN ('11') THEN s.status_name || ' - ' || COALESCE(a.cmrf_no, 'NA-') || ' Cheque No:' || r.cheque_no || ' Cheque Date:' || TO_CHAR(r.billdate, 'dd/mm/yyyy') " +
	                           "ELSE s.status_name END AS status " +
	                           "FROM mla_cmrf a " +
	                           "LEFT JOIN rev_san r ON r.cmrf_no = a.cmrf_no " +
	                           "INNER JOIN status_mst s ON CAST(a.status AS INTEGER) = s.status_id " +
	                           "WHERE a.aadhar_no = ? AND mla_cmrf_no <> ? AND a.entered_on >= CURRENT_DATE - INTERVAL '6 months' ORDER BY a.entered_on ";

	        // Execute data query
	        try (PreparedStatement dataStmt = con.prepareStatement(dataQuery)) {
	            dataStmt.setString(1, aadharNo);
	            dataStmt.setString(2, mlaCmrfNo);
	            try (ResultSet dataRs = dataStmt.executeQuery()) {
	                while (dataRs.next()) {
	                    Map<String, Object> entry = new HashMap<>();
	                    entry.put("mlaCmrfNo", dataRs.getString("mla_cmrf_no"));
	                    entry.put("patientName", dataRs.getString("patient_name"));
	                    entry.put("fatherSonOf", dataRs.getString("father_son_of"));
	                    entry.put("status", dataRs.getString("status"));
	                    entries.add(entry);
	                }
	            }
	        }
	    } catch (SQLException e) {
	        e.printStackTrace(); // Handle exceptions appropriately
	    }

	    return entries; // Return the list of entries (could be empty)
	}



	public String getCmrfLocData(String cmrfLocNo) throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		String cmrfLocStr=null;
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
           // sql = "select recommended_by||':#'||patient_name||':#'||father_name||':#'||address||':#'||assured_amt||':#'||hosp_code||':#'||purpose||':#'||loc_no as cmrfLocStr from loc_cmrf  where loc_no='" + cmrfLocNo + "'";
            sql = "select coalesce(recommended_by,'0')||':#'||patient_name||':#'||father_name||':#'||address||':#'||assured_amt||':#'||hosp_code||':#'||purpose||':#'||loc_no as cmrfLocStr from loc_cmrf  where loc_no='" + cmrfLocNo + "'"; 
			System.out.println("sql---"+sql);
			rs=st.executeQuery(sql);
			while(rs.next()) {
				cmrfLocStr=rs.getString("cmrfLocStr");
				
			}
			if(cmrfLocStr==null ||cmrfLocStr.equals("")){
				cmrfLocStr="Invalid LOC No.";
            }
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return cmrfLocStr;

	}
	
	 public List<AddCmrfEntryForm> getOtherConsList() throws Exception {
        Connection con = null;
        Statement st = null;
        ResultSet rs = null;
        String sql = null;
        final List<AddCmrfEntryForm> updateCmrfDetails = new ArrayList<AddCmrfEntryForm>();
        try {
            con = this.dataSource.getConnection();
            st = con.createStatement();
            sql = "select cname||'-'||cno as cnocode, cname||'-'||cno  as cname from  other_constituency_list order by cno";
            System.out.println("sql---" + sql);
            rs = st.executeQuery(sql);
            while (rs.next()) {
                final AddCmrfEntryForm updateCmrfEntry = new AddCmrfEntryForm();
                updateCmrfEntry.setConstNo(rs.getString("cnocode"));
                updateCmrfEntry.setConstName(rs.getString("cname"));
                updateCmrfDetails.add(updateCmrfEntry);
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            if (rs != null) {
                rs.close();
                rs = null;
            }
            if (st != null) {
                st.close();
                st = null;
            }
            if (con != null && !con.isClosed()) {
                con.close();
                con = null;
            }
        }
        return updateCmrfDetails;
    }

	public Map<String, Object> getCmrfData(String cmrfNo) throws Exception {
		Connection con = null;
		PreparedStatement stmt = null;
		ResultSet dataRs = null;
		Map<String, Object> cmrfRecord = new HashMap<>();

		try {
			con = dataSource.getConnection();

			String sql = "SELECT c.recommended_by, c.pat_name, c.father_son_of, COALESCE(c.aadhar_no,'0') AS aadhar_no, "
					+ "c.inward_id, COALESCE(c.hosp_code, 0) AS hosp_code, COALESCE(c.patient_ip_no, '') AS patient_ip, "
					+ "COALESCE(c.mobile_no, 0) AS mobile_no, COALESCE(c.pat_address, '') AS pat_address, "
					+ "COALESCE(c.pat_district, 0) AS pat_district, COALESCE(c.pat_mandal, 0) AS pat_mandal, "
					+ "COALESCE(c.purpose, 'UNDERGONE TREATMENT FOR DURING /202') AS purpose, "
					+ "COALESCE(c.age, '0') AS age, COALESCE(c.new_fsc_no, '0') AS new_fsc_no, "
					+ "COALESCE(c.bank_account_no, '0') AS bank_account_no, COALESCE(c.bank_acc_hol_name, '') AS bank_acc_hol_name "
					+ "FROM cmrelief c WHERE cmrf_no = ?";

			stmt = con.prepareStatement(sql);
			stmt.setString(1, cmrfNo);

			dataRs = stmt.executeQuery();
			System.out.println("CMRF DATA : " + sql);
			if (dataRs.next()) {
				cmrfRecord.put("recommendedBy", dataRs.getString("recommended_by"));
				cmrfRecord.put("patientName", dataRs.getString("pat_name"));
				cmrfRecord.put("fatherSonOf", dataRs.getString("father_son_of"));
				cmrfRecord.put("aadharNo", dataRs.getString("aadhar_no"));
				cmrfRecord.put("inwardId", dataRs.getString("inward_id"));
				cmrfRecord.put("hospCode", dataRs.getInt("hosp_code"));
				cmrfRecord.put("patientIP", dataRs.getString("patient_ip"));
				cmrfRecord.put("mobileNo", dataRs.getString("mobile_no"));
				cmrfRecord.put("patAddress", dataRs.getString("pat_address"));
				cmrfRecord.put("patDistrict", dataRs.getInt("pat_district"));
				cmrfRecord.put("patMandal", dataRs.getInt("pat_mandal"));
				cmrfRecord.put("purpose", dataRs.getString("purpose"));
				cmrfRecord.put("age", dataRs.getString("age"));
				cmrfRecord.put("newFscNo", dataRs.getString("new_fsc_no"));
				cmrfRecord.put("bankAccountNo", dataRs.getString("bank_account_no"));
				cmrfRecord.put("bankAccHolderName", dataRs.getString("bank_acc_hol_name"));
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception("Error fetching CMRF data", e); 
		} finally {
			CommonUtils.closeCon(con, stmt, dataRs);
		}
		
		return cmrfRecord;
	}

}
	
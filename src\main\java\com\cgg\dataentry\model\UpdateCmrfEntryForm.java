package com.cgg.dataentry.model;

public class UpdateCmrfEntryForm {
	private String cmrfDate;
	private String userId;
	private String cmrfUpdNo;
	private String cmpNo;
	private String otherConst;
	private String otherConHidval;
	private String enteredOn;
	
	
	public String getOtherConHidval() {
		return otherConHidval;
	}

	public void setOtherConHidval(String otherConHidval) {
		this.otherConHidval = otherConHidval;
	}

	public String getOtherConst() {
		return otherConst;
	}

	public void setOtherConst(String otherConst) {
		this.otherConst = otherConst;
	}

	public String getCmpNo() {
		return cmpNo;
	}

	public void setCmpNo(String cmpNo) {
		this.cmpNo = cmpNo;
	}

	public String getCmrfUpdNo() {
		return cmrfUpdNo;
	}

	public void setCmrfUpdNo(String cmrfUpdNo) {
		this.cmrfUpdNo = cmrfUpdNo;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}
	private String valueck;
	private String cmrfUser;
	private String ipAddress;
	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
	private String year=String.valueOf(new java.util.Date().getYear() + 1900);
	private String isReimbursement,exgratia;
	public String getValueck() {
		return valueck;
	}

	public void setValueck(String valueck) {
		this.valueck = valueck;
	}

	public String getCmrfUser() {
		return cmrfUser;
	}

	public void setCmrfUser(String cmrfUser) {
		this.cmrfUser = cmrfUser;
	}
	private String Cmrfno,CmrfYr;
	private String isSigned;
	
	public String getIsSigned() {
		return isSigned;
	}

	public void setIsSigned(String isSigned) {
		this.isSigned = isSigned;
	}

	public String getExgratia() {
		return exgratia;
	}

	public void setExgratia(String exgratia) {
		this.exgratia = exgratia;
	}
	public String getCo() {
		return co;
	}

	public void setCo(String co) {
		this.co = co;
	}
	private String co;
	private String Signed;
	private String cmrfLoc,patientIpNo,tokenNo;
	public String getTokenNo() {
		return tokenNo;
	}

	public void setTokenNo(String tokenNo) {
		this.tokenNo = tokenNo;
	}

	protected String mlaCmrfNo,inwardId;
	private String aadharNo,mobileNo,hospCode,age,fatherName,patAddress,hospName,bankAccNo,bankAccHolName;
	private String constNo;
	private String constName,recommendedBy,purpose,sancDate; 
	public String getSancDate() {
		return sancDate;
	}

	public void setSancDate(String sancDate) {
		this.sancDate = sancDate;
	}

	private String patDistrict,reqAmt,patientName;
	public String getCmrfno() {
		return Cmrfno;
	}

	public void setCmrfno(String cmrfno) {
		Cmrfno = cmrfno;
	}

	public String getCmrfYr() {
		return CmrfYr;
	}

	public void setCmrfYr(String cmrfYr) {
		CmrfYr = cmrfYr;
	}

	public String getPurpose() {
		return purpose;
	}

	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}
	public String getPatientName() {
		return patientName;
	}

	public void setPatientName(String patientName) {
		this.patientName = patientName;
	}

	private String patMandal,recFrom,pancAmt,paymentTo,diseaseType,detDiseases,sancAmt,range,endorsment;
	public String getEndorsment() {
		return endorsment;
	}

	public void setEndorsment(String endorsment) {
		this.endorsment = endorsment;
	}

	public String getRange() {
		return range;
	}

	public void setRange(String range) {
		this.range = range;
	}

	public String getSancAmt() {
		return sancAmt;
	}

	public void setSancAmt(String sancAmt) {
		this.sancAmt = sancAmt;
	}

	private String prevSanc,prevCmpno,prevDate,pndorsment,prevApplnos;
	private String distNo,distName;
	private String mandalNo,mandalName;
	public String getHospName() {
		return hospName;
	}

	public void setHospName(String hospName) {
		this.hospName = hospName;
	}

	public String getDistNo() {
		return distNo;
	}

	public void setDistNo(String distNo) {
		this.distNo = distNo;
	}

	public String getDistName() {
		return distName;
	}

	public void setDistName(String distName) {
		this.distName = distName;
	}

	public String getMandalNo() {
		return mandalNo;
	}

	public void setMandalNo(String mandalNo) {
		this.mandalNo = mandalNo;
	}

	public String getMandalName() {
		return mandalName;
	}

	public void setMandalName(String mandalName) {
		this.mandalName = mandalName;
	}

	public String getRecFrom() {
		return recFrom;
	}

	public void setRecFrom(String recFrom) {
		this.recFrom = recFrom;
	}

	

	public String getPatMandal() {
		return patMandal;
	}

	public void setPatMandal(String patMandal) {
		this.patMandal = patMandal;
	}


	public String getPatDistrict() {
		return patDistrict;
	}

	public void setPatDistrict(String patDistrict) {
		this.patDistrict = patDistrict;
	}

	public String getReqAmt() {
		return reqAmt;
	}

	public void setReqAmt(String reqAmt) {
		this.reqAmt = reqAmt;
	}

	public String getPancAmt() {
		return pancAmt;
	}

	public void setPancAmt(String pancAmt) {
		this.pancAmt = pancAmt;
	}

	public String getPaymentTo() {
		return paymentTo;
	}

	public void setPaymentTo(String paymentTo) {
		this.paymentTo = paymentTo;
	}

	public String getDiseaseType() {
		return diseaseType;
	}

	public void setDiseaseType(String diseaseType) {
		this.diseaseType = diseaseType;
	}

	public String getDetDiseases() {
		return detDiseases;
	}

	public void setDetDiseases(String detDiseases) {
		this.detDiseases = detDiseases;
	}

	public String getPrevSanc() {
		return prevSanc;
	}

	public void setPrevSanc(String prevSanc) {
		this.prevSanc = prevSanc;
	}

	public String getPrevCmpno() {
		return prevCmpno;
	}

	public void setPrevCmpno(String prevCmpno) {
		this.prevCmpno = prevCmpno;
	}

	public String getPrevDate() {
		return prevDate;
	}

	public void setPrevDate(String prevDate) {
		this.prevDate = prevDate;
	}

	public String getPndorsment() {
		return pndorsment;
	}

	public void setPndorsment(String pndorsment) {
		this.pndorsment = pndorsment;
	}

	public String getPrevApplnos() {
		return prevApplnos;
	}

	public void setPrevApplnos(String prevApplnos) {
		this.prevApplnos = prevApplnos;
	}

	public String getRecommendedBy() {
		return recommendedBy;
	}

	public void setRecommendedBy(String recommendedBy) {
		this.recommendedBy = recommendedBy;
	}

	public String getConstNo() {
		return constNo;
	}

	public void setConstNo(String constNo) {
		this.constNo = constNo;
	}

	public String getConstName() {
		return constName;
	}

	public void setConstName(String constName) {
		this.constName = constName;
	}

	public String getPatAddress() {
		return patAddress;
	}

	public void setPatAddress(String patAddress) {
		this.patAddress = patAddress;
	}

	public String getFatherName() {
		return fatherName;
	}

	public void setFatherName(String fatherName) {
		this.fatherName = fatherName;
	}

	public String getAge() {
		return age;
	}

	public void setAge(String age) {
		this.age = age;
	}

	public String getAadharNo() {
		return aadharNo;
	}

	public String getHospCode() {
		return hospCode;
	}

	public void setHospCode(String hospCode) {
		this.hospCode = hospCode;
	}

	public void setAadharNo(String aadharNo) {
		this.aadharNo = aadharNo;
	}

	public String getMlaCmrfNo() {
		return mlaCmrfNo;
	}

	public void setMlaCmrfNo(String mlaCmrfNo) {
		this.mlaCmrfNo = mlaCmrfNo;
	}

	public String getInwardId() {
		return inwardId;
	}

	public void setInwardId(String inwardId) {
		this.inwardId = inwardId;
	}

	public String getPatientIpNo() {
		return patientIpNo;
	}

	public void setPatientIpNo(String patientIpNo) {
		this.patientIpNo = patientIpNo;
	}

	public String getCmrfLoc() {
		return cmrfLoc;
	}

	public void setCmrfLoc(String cmrfLoc) {
		this.cmrfLoc = cmrfLoc;
	}

	public String getIsReimbursement() {
		return isReimbursement;
	}

	public String getSigned() {
		return Signed;
	}

	public void setSigned(String signed) {
		this.Signed = signed;
	}

	public void setIsReimbursement(String isReimbursement) {
		this.isReimbursement = isReimbursement;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getCmrfDate() {
		return cmrfDate;
	}

	public void setCmrfDate(String cmrfDate) {
		this.cmrfDate = cmrfDate;
	}

	public String getBankAccNo() {
		return bankAccNo;
	}

	public void setBankAccNo(String bankAccNo) {
		this.bankAccNo = bankAccNo;
	}

	public String getBankAccHolName() {
		return bankAccHolName;
	}

	public void setBankAccHolName(String bankAccHolName) {
		this.bankAccHolName = bankAccHolName;
	}

	public String getMobileNo() {
		return mobileNo;
	}

	public void setMobileNo(String mobileNo) {
		this.mobileNo = mobileNo;
	}

	public String getEnteredOn() {
		return enteredOn;
	}

	public void setEnteredOn(String enteredOn) {
		this.enteredOn = enteredOn;
	}
}

package com.cgg.dataentry.service;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.dataentry.dao.ConstituencyDetailsDao;

import com.cgg.dataentry.model.ConstituencyDetailsForm;
@Service
public class ConstituencyDetailsServiceImpl implements ConstituencyDetailsService{
	@Autowired
	private ConstituencyDetailsDao constDtlsDao;
	
public List<ConstituencyDetailsForm> getDistricts()throws Exception{
		
		return constDtlsDao.getDistricts();
	}

@Override
public List<ConstituencyDetailsForm> getConstDtls() throws Exception {
	// TODO Auto-generated method stub
	return constDtlsDao.getConstDtls();
}

@Override
public int insertConstDetails(ConstituencyDetailsForm constDtlsForm, Map<String, Object> model)throws Exception {
	int cnt=constDtlsDao.insertConstDetails(constDtlsForm, model);
	return cnt;
}

@Override
public int updateConstDetails(ConstituencyDetailsForm constDtlsForm, Map<String, Object> model)throws Exception {
	// TODO Auto-generated method stub
	int cnt=constDtlsDao.updateConstDetails(constDtlsForm, model);
	return cnt;
}

@Override
public String getConstData(String constituencyVal) throws Exception {
	// TODO Auto-generated method stub
	return constDtlsDao.getConstData(constituencyVal);
}


}

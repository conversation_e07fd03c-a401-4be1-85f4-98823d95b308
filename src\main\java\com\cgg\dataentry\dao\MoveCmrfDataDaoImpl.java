package com.cgg.dataentry.dao;

import java.time.LocalDateTime;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.cgg.dataentry.model.MoveDataModel;

@Repository
public class MoveCmrfDataDaoImpl implements MoveCmrfDataDao{

	@Autowired
	JdbcTemplate jdbcTemplate;
	
	@Override
	public boolean updateData(MoveDataModel moveData,HttpServletRequest request) throws Exception {
		String ipAddress = request.getRemoteAddr();
		HttpSession session=request.getSession();
		String userId=(String)session.getAttribute("userid");
		boolean flag=false;
		String sql="";
		int count=0;
		String fromDate = Integer.parseInt(moveData.getCMRFFrom().substring(8, 10))+"-"+Integer.parseInt(moveData.getCMRFFrom().substring(5, 7))+"-"+Integer.parseInt(moveData.getCMRFFrom().substring(0, 4));
		String toDate = Integer.parseInt(moveData.getCMRFTo().substring(8, 10))+"-"+Integer.parseInt(moveData.getCMRFTo().substring(5, 7))+"-"+Integer.parseInt(moveData.getCMRFTo().substring(0, 4));
		sql="update cmrelief set cmrf_dt=to_date('" + toDate + "','dd-mm-yyyy'),sanc_date=to_date('" + toDate + "','dd-mm-yyyy'),updated_by = '" + userId
				+ "', updated_on = '" + LocalDateTime.now() + "', " + "ip_address = '" + ipAddress + "' "
				+ " where cmp_date is null and proceedings is null and cmrf_dt=to_date('" + fromDate + "','dd-mm-yyyy')";
		count=jdbcTemplate.update(sql);
		if(count>=1) {
			flag=true;
		}else {
			flag=false;
		}
		return flag;
	}

}

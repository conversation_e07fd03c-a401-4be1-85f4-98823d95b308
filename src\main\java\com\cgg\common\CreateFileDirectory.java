package com.cgg.common;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.net.URLConnection;
import org.springframework.web.multipart.MultipartFile;

public class CreateFileDirectory {

    public boolean createDiretoriesMultiPartFile(String filePath, String fileName, MultipartFile formFile) throws Exception {
        boolean flag = false;
        FileOutputStream outputStream = null;
        InputStream inputStream = null;

        try {
            // Check file size before proceeding
            if (formFile.getSize() > 100 * 1024 * 1024) { // 100 MB limit
                throw new IOException("File size exceeds the 100MB limit.");
            }

            File folder = new File(filePath);
            if (!folder.exists()) {
                folder.mkdirs(); // Create directories if not exist
            }

            File newFile = new File(filePath, fileName);
            outputStream = new FileOutputStream(newFile);
            inputStream = formFile.getInputStream();
            
            byte[] buffer = new byte[8192]; // Use buffer to prevent memory overload
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            flag = true; // Indicate successful upload
        } catch (IOException e) {
            e.printStackTrace(); // Log exception for better tracking
            throw new Exception("Error uploading file: " + e.getMessage(), e); // Rethrow as custom exception
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return flag; // Return true if the file is successfully written
    }
    public boolean createDirectoriesAndEmptyFile(String filePath, String fileName) throws Exception {
        boolean flag = false;

        try {
            File folder = new File(filePath);
            if (!folder.exists()) {
                boolean dirsCreated = folder.mkdirs();  
                if (!dirsCreated) {
                    throw new IOException("Failed to create necessary directories.");
                }
            }

            // Create an empty file
            File newFile = new File(filePath, fileName);
            if (!newFile.exists()) {
                boolean fileCreated = newFile.createNewFile();
                if (!fileCreated) {
                    throw new IOException("Failed to create the file: " + newFile.getAbsolutePath());
                }
            }

            flag = true;  
        } catch (IOException e) {
            e.printStackTrace();  
            throw new Exception("Error creating directories or file: " + e.getMessage(), e);
        }

        return flag;
    }
    public static String getContentType(Path path) throws IOException {
        String contentType = null;
        try {
            contentType = URLConnection.guessContentTypeFromName(path.toString());
            if (contentType == null) {
                contentType = Files.probeContentType(path);
            }
        } catch (IOException e) {
            e.printStackTrace(); // Log for debugging
        }
        return contentType;
    }
}

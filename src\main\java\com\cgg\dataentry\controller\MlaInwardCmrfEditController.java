package com.cgg.dataentry.controller;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.cgg.common.CommonFunctions;
import com.cgg.common.Response;
import com.cgg.common.UIDVerfication;
import com.cgg.dataentry.entities.MlaCmrf;
import com.cgg.dataentry.service.MlaInwardCmrfEditService;
import com.cgg.hospital.model.InwardCmrf;
import com.cgg.hospital.service.AarogyasriService;
import com.cgg.hospital.service.InwardCmrfService;

import meesevaService.MeeseevaData;

//import cgg.gov.in.model.PetitionForm;

@Controller
public class MlaInwardCmrfEditController {

	@Autowired
	MlaInwardCmrfEditService mlaInwardCmrfEditService;

	@Autowired
	private InwardCmrfService inwardService;

	@Autowired
	private AarogyasriService aarogyasriService;

	@GetMapping("/mlaInwardCmrfEdit")
	public String mlaInwardCmrfEdit(HttpServletRequest request) {
		HttpSession session = request.getSession();
		String userId = null;
		userId = (String) session.getAttribute("userid");
		if (userId == null || userId.equals("null")) {
			return "redirect:/";
		}
		return "mlaInwardCmrfEdit";
	}

	@PostMapping("/getTokenDetails")
	public String getTokenDetails(@ModelAttribute("inwardCmrf") InwardCmrf inwardCmrf, HttpServletRequest request,
			RedirectAttributes redirect, Model model, HttpServletRequest req) {

		Response res = new Response();
		List<Map<String, String>> patientDetails = new ArrayList<Map<String, String>>();
		Map<String, String> incomeDetails = new HashMap<>();
		String retriveData = "";
		String result = "";

		try {
			HttpSession session = request.getSession();
			String cmrfTokenNo = inwardCmrf.getCmrfTokenNo();
			LocalDateTime qrStartDate = LocalDateTime.of(2025, 2, 1, 0, 0, 0);

			Optional<MlaCmrf> tokenDetails = mlaInwardCmrfEditService.getTokenDetails(cmrfTokenNo);
			if (!tokenDetails.isPresent()) {
				redirect.addFlashAttribute("error", "No Data Found");
				return "redirect:/mlaInwardCmrfEdit";
			}

			LocalDateTime enteredOn = tokenDetails.get().getEnteredOn();
			LocalDateTime qrScannedTimestamp = tokenDetails.get().getQrScannedTimestamp();

			if (enteredOn.isAfter(qrStartDate) || enteredOn.isEqual(qrStartDate)) {
				if (qrScannedTimestamp == null) {
					redirect.addFlashAttribute("error", "QR code must be scanned for application to update.");
					return "redirect:/mlaInwardCmrfEdit";
				}
			}

			MlaCmrf mlaCmrf = tokenDetails.get();
			String aadharNo = mlaCmrf.getAadharNo();
			String fscNo = mlaCmrf.getNewFscNo();
			String incomeNo = mlaCmrf.getIncomeNo();

			if (aadharNo != null && !aadharNo.equals("")) {
				res = aarogyasriService.getPatientDetails(aadharNo, null, req);
			} else if (fscNo != null && !fscNo.equals("")) {
				res = aarogyasriService.getPatientDetails(null,fscNo, req);
			}
			if (res.getStatus() == HttpStatus.OK) {
				patientDetails = (List<Map<String, String>>) res.getData();
			}
			model.addAttribute("patientDetails", patientDetails);
			if (incomeNo != null && !incomeNo.equals("")) {
				retriveData = MeeseevaData.MeesevaDAta(incomeNo);
			}
			if (retriveData == null || retriveData.equals("") || retriveData.length() < 10
					|| retriveData.equals("Application Number not found")) {
				model.addAttribute("msg", "Income Details Not Found");
			} else if (retriveData.substring(0, 2).equals("SD")) {
				String val[] = retriveData.split("\\$");
				if (val[18].equals("Income")) {
					incomeDetails.put("incomeNo", val[0]);
					incomeDetails.put("incomeDate", val[10]);
					incomeDetails.put("name", val[1]);
					incomeDetails.put("fatherName", val[2]);
				} else {
					model.addAttribute("msg", incomeNo + " is not income certificate no.");

				}

			} else {
				String val[] = retriveData.split("\\$");
				incomeDetails.put("incomeNo", val[0]);
				incomeDetails.put("incomeDate", val[10]);
				incomeDetails.put("name", val[1]);
				incomeDetails.put("fatherName", val[2]);
			}
			model.addAttribute("incomeDetails", incomeDetails);
			model.addAttribute("TokenDetails", tokenDetails.get());
			model.addAttribute("No", tokenDetails.get().getMlaCmrfNo().split("/")[0]);
			System.out.println(tokenDetails.get());

			List<InwardCmrf> recommendedList = new ArrayList<InwardCmrf>();
			List<InwardCmrf> hospitalList = new ArrayList<InwardCmrf>();
			List<InwardCmrf> districts = new ArrayList<InwardCmrf>();
			List<InwardCmrf> mandals = new ArrayList<InwardCmrf>();
			List<InwardCmrf> bankNames = new ArrayList<InwardCmrf>();
			List<InwardCmrf> bankIfsc = new ArrayList<InwardCmrf>();
			List<InwardCmrf> villages = new ArrayList<InwardCmrf>();
			villages = inwardService.getVillages(tokenDetails.get().getPatMandal().toString(), tokenDetails.get().getPatDistrict().toString());
			

			Integer hospDistrictCode = mlaInwardCmrfEditService.getHospDistrictCode(tokenDetails.get().getHospCode());
            String branch = mlaInwardCmrfEditService.getBranch(tokenDetails.get().getBankIfsc());
			
			recommendedList = inwardService.getRecommendedDetails(session);
			hospitalList = inwardService.getHospByDistCode(hospDistrictCode.toString());
			districts = inwardService.getDistricts();
			mandals = inwardService.getMandals(tokenDetails.get().getPatDistrict().toString());
			//bankNames = inwardService.getBanksByDistrictName((tokenDetails.get().getBankDist()));
//			bankIfsc = inwardService.getIFSCsByBankName(tokenDetails.get().getBankName(),
//					tokenDetails.get().getBankDist());
			model.addAttribute("recommendedList", recommendedList);
			model.addAttribute("hospitalList", hospitalList);
			model.addAttribute("districts", districts);
			model.addAttribute("mandals", mandals);
			model.addAttribute("hospDistCode", hospDistrictCode);
			model.addAttribute("bankNames", bankNames);
			model.addAttribute("bankIfsc", bankIfsc);
			model.addAttribute("villages", villages);
			tokenDetails.get().setBankBranch(branch);

		} catch (Exception e) {
			e.printStackTrace();
			redirect.addFlashAttribute("error", "Something went wrong");
			return "redirect:/mlaInwardCmrfEdit";
			
		}
		return "mlaInwardCmrfEdit";

	}

	@PostMapping("/saveTokenDetails")
	public String saveTokenDetails(@ModelAttribute("inwardCmrf") InwardCmrf inwardCmrf, HttpServletRequest request,
			RedirectAttributes redirect, Model model, HttpSession session) {
		System.out.println(inwardCmrf);
		String userId = null;
		List<String> errors = new ArrayList<String>();
		try {
			if (session != null && session.getAttribute("userid") != null) {
				userId = (String) session.getAttribute("userid");
				inwardCmrf.setUserId(userId);
			}
			errors = checkErrorsForEdit(errors, inwardCmrf);
			if (errors.size() > 0) {
				redirect.addFlashAttribute("errors", errors);
				return "redirect:/mlaInwardCmrfEdit";
			}
			inwardCmrf.setIpAddress(request.getRemoteAddr());
			Response reponse = mlaInwardCmrfEditService.saveTokenDetails(inwardCmrf, request);
			System.out.println("reponse" + reponse);
			if (reponse.getStatus().equals(HttpStatus.OK)) {
				redirect.addFlashAttribute("success", "Successfully Updated the Inward Cmrf Details");
			} else {
				errors.add(reponse.getMessage());
				redirect.addFlashAttribute("errors", errors);
				return "redirect:/mlaInwardCmrfEdit";
			}
			System.out.println("reponse" + reponse);
		} catch (Exception e) {
			e.printStackTrace();
			redirect.addFlashAttribute("error", "Failed to update Inward Cmrf Details");
			return "redirect:/mlaInwardCmrfEdit";
		}
		return "redirect:/mlaInwardCmrfEdit";
	}

	public List<String> checkErrorsForEdit(List<String> errors, InwardCmrf inwardCmrf) throws Exception {
		
		if (!CommonFunctions.validateData(inwardCmrf.getPatientName())) {
			errors.add("Enter Name");
		}
		if (!CommonFunctions.validateData(inwardCmrf.getAge())) {
			errors.add("Enter Age");
		}
		if (!CommonFunctions.validateData(inwardCmrf.getFatherName())) {
			errors.add("Enter Gaurdian name");
		}
		if (!CommonFunctions.validateData(inwardCmrf.getAadharNo())) {
			errors.add("Enter Aadhar No");
		}
		
		String aadharNo = inwardCmrf.getAadharNo();
		if (CommonFunctions.validateData(inwardCmrf.getAadharNo())) {
			if (!UIDVerfication.validateVerhoeff(aadharNo)) {
				errors.add("Enter Valid Aadhar");
			}
		}
		if (!CommonFunctions.validateData(inwardCmrf.getMobileNo())) {
			errors.add("Enter Mobile No");
		}
		if(!CommonFunctions.validateData(inwardCmrf.getIncomeCerNo()) && !CommonFunctions.validateData(inwardCmrf.getNewFscNo())) {
			errors.add("Enter Income number or FSC No");
			
		}
		if (!CommonFunctions.validateSelectBox(inwardCmrf.getPatDistrict())) {
			errors.add("Select District");
		}
		if (!CommonFunctions.validateSelectBox(inwardCmrf.getPatMandal())) {
			errors.add("Select Mandal");
		}
		if (!CommonFunctions.validateSelectBox(inwardCmrf.getPatVillage())) {
			errors.add("Select village");
		}
		if (!CommonFunctions.validateData(inwardCmrf.getPinCode())) {
			errors.add("Enter Pincode");
		}
		if (!CommonFunctions.validateData(inwardCmrf.getPatAddress())) {
			errors.add("Enter Address");
		}
		/*
		 * if(!CommonFunctions.validateSelectBox(inwardCmrf.getBankDistrict())) {
		 * errors.add("Select Bank District"); }
		 */
		if(!CommonFunctions.validateData(inwardCmrf.getBankIfsc())) {
			errors.add("Select Enter IFSC Code");
		}
		if(!CommonFunctions.validateData(inwardCmrf.getBankName())) {
			errors.add("Select Enter Bank Name");
		}
		if(!CommonFunctions.validateData(inwardCmrf.getBankAccNo())) {
			errors.add("Enter Bank Account Number");
		}
		if (!CommonFunctions.validateSelectBox(inwardCmrf.getHospDistrict())) {
			errors.add("Select Hospital District");
		}
		if (!CommonFunctions.validateSelectBox(inwardCmrf.getHospCode())) {
			errors.add("Select Hospital");
		}
		if (!CommonFunctions.validateData(inwardCmrf.getPatientIpNo())
				&& !CommonFunctions.validateData(inwardCmrf.getAdmissNo())) {
			errors.add("Enter Either patient No or Admission No");
		}

		if (!CommonFunctions.validateData(inwardCmrf.getPurpose())) {
			errors.add("Enter Purpose");
		}
		if(!CommonFunctions.validateSelectBox(inwardCmrf.getStatus())) {
			errors.add("Select Status");
		}
		String status = inwardCmrf.getStatus();
		if (CommonFunctions.validateSelectBox(status) &&("4".equals(status) || "6".equals(status))){
		    if(!CommonFunctions.validateSelectBox(inwardCmrf.getReason())) {
		    	errors.add("select Reason");
		    }
		}
		if(CommonFunctions.validateSelectBox(inwardCmrf.getReason()) && "other".equals(inwardCmrf.getReason())) {
			if(!CommonFunctions.validateData(inwardCmrf.getRemarks())) {
				errors.add("Enter Remarks");
			}
		}
		return errors;
	}

}
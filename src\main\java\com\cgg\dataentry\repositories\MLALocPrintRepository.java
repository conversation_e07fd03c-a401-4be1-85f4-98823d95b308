package com.cgg.dataentry.repositories;

import java.util.List;

import javax.persistence.Tuple;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.cgg.dataentry.entities.LocMlaCmrf;
import com.cgg.dataentry.model.LocMlaCmrfPrintProjection;

@Repository
public interface MLALocPrintRepository extends JpaRepository<LocMlaCmrf, String> {

	@Query(value = "select mla_loc_no, patient_name,father_son_of,address,purpose,assured_amt,hosp.hospname from loc_mla_cmrf locmla "
			+ " inner join hospital hosp on hospcode=locmla.hosp_code "
			+ " where recommended_by=:consNo and status='9' and supdt_verified_by is not null", nativeQuery = true)
	List<Tuple> getAllApprovedData(@Param("consNo") String consNo);

	@Query(value = "SELECT mla_loc_no as mla<PERSON><PERSON><PERSON>o, patient_name as patient<PERSON><PERSON>, father_son_of as father<PERSON><PERSON><PERSON><PERSON>, patient_ip as patientIp, age as Age, "
	        + " case when gender = 'M' then 'Male' when gender = 'F' then 'Female' end as gender, hospname, "
	        + "distname as distName, special_name as specialName, department_name as departmentName, address as Address, a.hosp_code as hospCode, "
	        + "a.mobile_no as mobileNo, aadhaar_no as aadhaarNo, opcr_no as opcrNo, purpose as purpose, "
	        + "assured_amt as assuredAmt, case when aarogyasree_covered='Not Covered' then 'No' else 'Yes' end as aarogyasreeCovered, "
	        + "bed_charges as bedCharges, investig_charges as investigCharges, drugs_disp_charges as drugsDispCharges, "
	        + "surg_proc_charges as surgProcCharges, implant_charges as implantCharges, misc_charges as miscCharges "
	        + "FROM loc_mla_cmrf as a "
	        + "inner join district d on d.distno=a.pat_district "
	        + "left join cmrf_treat_sub_special_mst ctd on ctd.slno=a.treat_sub_id "
	        + "inner join cmrf_department_mst cd on cd.dept_id=a.treat_dept_id "
	        + "left join hospital h on a.hosp_code = h.hospcode "
	        + "WHERE mla_loc_no = :locTokenNo "
	        + "AND status = '9' AND supdt_verified_by IS NOT NULL", nativeQuery = true)
	LocMlaCmrfPrintProjection getEstimationData(@Param("locTokenNo") String locTokenNo);




}

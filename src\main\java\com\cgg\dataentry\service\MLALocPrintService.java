package com.cgg.dataentry.service;

import java.util.List;

import com.cgg.dataentry.model.LOCMLACMRFEntryModel;
import com.cgg.dataentry.model.LocMlaCmrfPrintDTO;

public interface MLALocPrintService {

	public List<LOCMLACMRFEntryModel> getAllApprovedData(String consNo,String userId);

	public LOCMLACMRFEntryModel getLOCCoveringLetterData(String locTokenNo) throws Exception;
	
	public LocMlaCmrfPrintDTO  getEstimationData(String locTokenNo) throws Exception;

}

package com.cgg.common;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;


public class jsonDBT2 {

	public static void main(String[] args) {
	
		String message="Your OTP for College Confirmation is :67786969 TSCHE";
		String templateid="1007861765355150212";
		sendSingleSMS("917680884436", message,templateid);
	}
	public static void sendSingleSMS(String mobileNo, String message,String templateid) {
		HttpURLConnection connection = null;
		DataInputStream input=null;
		DataOutputStream output=null;
    	try {
    		String smsservicetype = "singlemsg"; // For single message.
    		/*String query = "user=" + URLEncoder.encode(username) +":" + URLEncoder.encode(password)+ "&msgtxt=" + URLEncoder.encode(message) + "&receipientno="+ URLEncoder.encode(mobileNo) + "&senderID="+ URLEncoder.encode(senderid)
    		+"&type=1 &Service Key=31af9092-d353-11eb-9c5d-0abea36e3286&template_id="+templateid;*/
    		String query =getSmsJsonRequest(mobileNo,message,templateid);
    		System.out.println("query--"+query);
    		
    		String smsId=null;
    		String str="";
    	    //System.out.println(ipAddress );//prints *************
    		URL url = new URL("https://api.imiconnect.in/resources/v1/messaging");
    		connection = (HttpURLConnection) url.openConnection();
    		connection.setDoInput(true);
    		connection.setDoOutput(true);
    		connection.setRequestMethod("POST");
    		connection.setFollowRedirects(true);
    		connection.setRequestProperty("Content-length", String.valueOf(query.length()));
    		connection.setRequestProperty("Content-Type","application/json");
    		connection.setRequestProperty("Key","31af9092-d353-11eb-9c5d-0abea36e3286");
    		connection.setRequestProperty("User-Agent","Mozilla/4.0 (compatible; MSIE 5.0; Windows 98; DigExt)");
    		connection.setReadTimeout(20000);
    		connection.setConnectTimeout(20000);    		
    		 output = new DataOutputStream(connection.getOutputStream());
    		output.writeBytes(query);
    		
    		smsId=insertSmsJsonRequest(mobileNo,templateid,query);
    	if(smsId!=null && !smsId.equals("")) {
			StringBuilder sb = new StringBuilder();
    		 input = new DataInputStream(connection.getInputStream());
    		for (int c = input.read(); c != -1; c = input.read()) {
    		 sb.append((char) c);
    		}
    		str = sb.toString();
          // converjsonToJavaArray(str);
    		
          int updcCnt=updateSmsJsonResponse(smsId,str);
    	}else {
    		//connection.disconnect();
    		  connection = (HttpURLConnection) url.openConnection();
    	}
    	}catch (Exception e) {
    		System.out.println("Something bad just happened.");
    		e.printStackTrace();
    	}finally {
    		try {
				input.close();
				output.close();
			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
    	}

    //return connection;
    } 
	
	public static String getSmsJsonRequest(String mobileNo, String message,String templateid) {		 
		 JSONObject jo = new JSONObject();	
		 JSONObject jo1 = new JSONObject();		 
		 JSONObject jo2 = new JSONObject();		 
		 JSONObject jo3 = new JSONObject();	
		 JSONObject jo4 = new JSONObject();	

		  try {
			JSONArray ja = new JSONArray();
		    JSONArray ja1 = new JSONArray();
		    /*Map m = new LinkedHashMap(3);
		    Map m1 = new LinkedHashMap(2);	       
	        Map m2 = new LinkedHashMap(2);*/
	        jo.put("deliverychannel", "sms");		       	       
	        jo1.put("type", "1");
	        jo1.put("senderid", "TSDOST");
	        jo1.put("text", "Your OTP for College Confirmation is :67786969 TSCHE");		       
	        jo2.put("dlt_templateid","1007861765355150212");
	        jo1.put("extras",jo2);
	        jo3.put("sms", jo1); 
	        jo.put("channels", jo3);
	        ja1.add(mobileNo);
	        jo4.put("correlationid", "Test_API_Optional");
	        jo4.put("msisdn", ja1);		     
	        ja.add(jo4);		       
	        jo.put("destination", ja);		    
		    System.out.println(jo.toString());			
		} catch (Exception e) {
			// TODO: handle exception
		}
		
		 return jo.toString();
		 
	}

	public static void converjsonToJavaArray(String jsonData)
	
	{
		try {		
        System.out.println("JSON Object");  
        JSONObject obj=(JSONObject)JSONValue.parse(jsonData); 
        JSONArray arr=(JSONArray)obj.get("response"); 
        System.out.println(arr.get(0));  //this will print {"id":42,...sities ..
        JSONArray arr1=(JSONArray)obj.get(arr.get(0)); 
        List<Object>list=new ArrayList<Object>();   
        for(int i=0;i<arr.size();i++) {
            JSONObject index = (JSONObject) arr.get(i);
            String data = (String) index.get("code");
            String transid = (String) index.get("transid");
            String description = (String) index.get("description");
            String correlationid = (String) index.get("correlationid");
            System.out.println(data);
            System.out.println(transid);
            System.out.println(description);
            System.out.println(correlationid);
        }
        
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} 
	}
	
	
	
	  public static String insertSmsJsonRequest(String mobileNo,String
	  templateid,String jsonReq) {
		  String jsonReqSql=null; int insCnt=0; 
		  String smsId=null;
		 // smsId=DatabasePlugin.getStringfromQueryNew("select nextval('sms_imicon_seq')",null);
   jsonReqSql ="insert into sms_imiconnect_msg(sms_id,template_id,mobile_no,"
	  + "sms_req_msg,sms_req_date,ip_address)" + " values" +
	  " ('"+smsId+"','"+templateid+"','"+mobileNo+"','"+jsonReq+"',now())";
	 // insCnt=DatabasePlugin.executeUpdate_dost(jsonReqSql); 
	  return smsId;
	  
	  } 
	  public static int updateSmsJsonResponse(String smsId,String response) {
	  String jsonResSql=null; int updCnt=0;
	  jsonResSql="update sms_imiconnect_msg set response="
	  +response+",sms_res_date=now() where" + " sms_id='"+smsId+"'";
	//  updCnt=DatabasePlugin.executeUpdate_dost(jsonResSql); return updCnt;
	  return updCnt;
	  }
	 
	//{"response":[{"code":"1001","transid":"f02eb8c5-e144-41ab-bcb7-b8614d19882a","description":"Queued","correlationid":"Test_API_Optional"}]}
}

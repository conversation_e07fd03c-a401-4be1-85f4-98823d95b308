package com.cgg.dataentry.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.cgg.common.Encryptor;
import com.cgg.common.Response;
import com.cgg.dataentry.model.UpdateChequeForm;
import com.cgg.dataentry.service.UpdateChequeService;

@Controller
public class UpdateChequeController {
	
	@Autowired
	UpdateChequeService updateChequeService;
	
	@GetMapping("/updateCheque")
	public String updateCheque(HttpServletRequest request,Model model) {
		try {
			HttpSession session = request.getSession();
			String userId = null;
			userId = (String) session.getAttribute("userid");
			if (userId == null || userId.equals("null")) {
				return "redirect:/";
			}
			Map<String, Object> result = updateChequeService.getTodaysCheckHistory(request);

            if ("success".equals(result.get("status"))) {
                  
                List<UpdateChequeForm> chequeHistoryList = (List<UpdateChequeForm>) result.get("data");
                if (chequeHistoryList != null && !chequeHistoryList.isEmpty()) {
                	model.addAttribute("todayCheckHistory", chequeHistoryList);
				}
                else {
                    model.addAttribute("emptydatamsg", "No data found for today's check history");
                }
            } else {
                model.addAttribute("histroymsg", result.get("message"));  
            }
		} catch (Exception e) {
			e.printStackTrace();
			 model.addAttribute("error", "Error: Something went wrong. Please try again.");
		}
		
		return "updateCheque";
		
	}
	
	@PostMapping("/getDetails")
	public String getDetails(@ModelAttribute("updateChequeForm") UpdateChequeForm updateChequeForm, Model model,RedirectAttributes redirect, HttpServletRequest request) {
		
		try {
			String cmrfNo = updateChequeForm.getCmrfNo();
			String cmrfYear = updateChequeForm.getCmrfYear();
			String chequeNo = updateChequeForm.getChequeNo();
			if((cmrfNo==null || cmrfNo.isEmpty() || cmrfYear.length() != 10)&&(chequeNo==null || chequeNo.isEmpty())) {
				redirect.addFlashAttribute("error", "Enter either CMRF No or Cheque No");
				return "redirect:/updateCheque";
			}
			Response response = updateChequeService.getDetails(updateChequeForm);
			if(response.getStatus().equals(HttpStatus.OK)) {
				Map<String, Object> mapData = (Map<String, Object>)  response.getData();
					if (mapData.containsKey("chequeDetails")) {
					    Object chequeDetails = mapData.get("chequeDetails");
					    if (chequeDetails != null) {
					        model.addAttribute("chequeDetails", chequeDetails);
					    }
					}if(mapData.get("chequeHistory")!=null) {
						model.addAttribute("chequeHistory", mapData.get("chequeHistory"));
					}
				}
			else {
				redirect.addFlashAttribute("error", response.getMessage());
				return "redirect:/updateCheque";
			}
			Map<String, Object> result = updateChequeService.getTodaysCheckHistory(request);
			if ("success".equals(result.get("status"))) {
                
                List<UpdateChequeForm> chequeHistoryList = (List<UpdateChequeForm>) result.get("data");
                if (chequeHistoryList != null && !chequeHistoryList.isEmpty()) {
                	model.addAttribute("todayCheckHistory", chequeHistoryList);
				}
                else {
                    model.addAttribute("emptydatamsg", "No data found for today's check history");
                }
            } else {
                model.addAttribute("histroymsg", result.get("message"));  
            }
		}catch (Exception e) {
			e.printStackTrace();
			redirect.addFlashAttribute("error", "Something went wrong");
			return "redirect:/updateCheque";
		}
		
		return "updateCheque";
		
	}
	
	@PostMapping("/updateChequeDetails")
	public String updateChequeDetails(@ModelAttribute("updateChequeForm") UpdateChequeForm updateChequeForm,RedirectAttributes redirect,Model model,HttpServletRequest request) {
		try {
			System.out.println(updateChequeForm);
			
			  String chequeRevDate = updateChequeForm.getRevalidatedDate();
			  String name = updateChequeForm.getCorrectedName();
			  String accountNo = updateChequeForm.getCorrectedAccountNo();
			  String othersRemarks = updateChequeForm.getOthersRemarks();
			  String newCheque = updateChequeForm.getNewChequeNo();
			  String isNewChequeIssued = updateChequeForm.getIsNewChequeIssued();
			  String revalidatedDate = updateChequeForm.getRevalidatedDate();
			 
			 List<String> errors = new ArrayList<>();
			      if(revalidatedDate == null || revalidatedDate.isEmpty()) {
					  errors.add("Please enter revalidated Date."); 
			      }
				  if(isNewChequeIssued == null || isNewChequeIssued.isEmpty()) {
				  errors.add("Please select whether a new Cheque is issued or not."); }
				  if(isNewChequeIssued.equals("yes")) { 
					  String remarks = updateChequeForm.getRemarks();
					  String newChequeNo = updateChequeForm.getNewChequeNo();
					  if (remarks == null || remarks.isEmpty()) {
					  errors.add("Please select a reason for issuing a new cheque.");
					  }
					  if(newChequeNo == null || newChequeNo.trim().isEmpty()) {
					  errors.add("Please enter the new cheque number.");
					  }
					  if(remarks.equals("Name Correction")||remarks.equals("Death Case") ) {
						  if(name == null || name.trim().isEmpty()) {
							  errors.add("Please enter the Name.");
							} 
					  }
					  if(remarks.equals("Account No Correction") ) {
						  if(accountNo == null || accountNo.trim().isEmpty()) {
							  errors.add("Please enter the Account Number.");
							} 
					  }
					  if(remarks.equals("Name & Account No Correction") ) {
						  if(name == null || name.trim().isEmpty() || accountNo == null || accountNo.trim().isEmpty()) {
							  errors.add("Please enter the correct details (Name & Account Number).");
							} 
					  }
					  if(remarks.equals("Others")) {
						  if(othersRemarks == null || othersRemarks.trim().isEmpty()) {
							  errors.add("Please enter other reasons.");
							} 
					  }
				  }
		        if (!errors.isEmpty()) {
		        	redirect.addFlashAttribute("errors", errors);
					return "redirect:/updateCheque";
		        }
			System.out.println(updateChequeForm);
			boolean updateChequeDetails = updateChequeService.updateChequeDetails(updateChequeForm,request);
			if(updateChequeDetails) {
				model.addAttribute("success", "Cheque Updated Successfully");
			}else {
				model.addAttribute("error", "Error Updating Cheque");
			}
			Map<String, Object> result = updateChequeService.getTodaysCheckHistory(request);
			if ("success".equals(result.get("status"))) {
                
                List<UpdateChequeForm> chequeHistoryList = (List<UpdateChequeForm>) result.get("data");
                if (chequeHistoryList != null && !chequeHistoryList.isEmpty()) {
                	model.addAttribute("todayCheckHistory", chequeHistoryList);
				}
                else {
                    model.addAttribute("emptydatamsg", "No data found for today's check history");
                }
            } else {
                model.addAttribute("histroymsg", result.get("message"));  
            }
		}catch (Exception e) {
			e.printStackTrace();
		}
		return "updateCheque";
	}

	@GetMapping("/editCheque")
	public String editCheque(@RequestParam String data, HttpServletRequest request, Model model) {
		if (data != null && !data.isEmpty()) {
			try {
				String decryptedData = Encryptor.decrypt(data);
				System.out.println("Decrypted Data: " + decryptedData); // Debug log

				String[] params = decryptedData.split("&");

				for (String pair : params) {
					String[] keyValue = pair.split("=");
					if (keyValue.length > 0) {
						String key = keyValue[0];
						String value = (keyValue.length > 1) ? keyValue[1] : "";
						model.addAttribute(key, value);
					}
				}

				System.out.println("Model: " + model);

			} catch (Exception e) {
				e.printStackTrace();
				request.setAttribute("error", "Failed to decrypt data.");
			}
		}

		return "editCheque";
	}

	@PostMapping("/editChequeDetails")
	public String editChequeDetails(@ModelAttribute("updateChequeForm") UpdateChequeForm updateChequeForm,RedirectAttributes redirect,Model model,HttpServletRequest request) {
		try {
			
			System.out.println("Updated Form :: " + updateChequeForm);

			  String name = updateChequeForm.getCorrectedName();
			  String accountNo = updateChequeForm.getCorrectedAccountNo();
			  String othersRemarks = updateChequeForm.getOthersRemarks();
			  String isNewChequeIssued = updateChequeForm.getIsNewChequeIssued();
			  String revalidatedDate = updateChequeForm.getRevalidatedDate();
			 
			 List<String> errors = new ArrayList<>();
			      if(revalidatedDate == null || revalidatedDate.isEmpty()) {
					  errors.add("Please enter revalidated Date."); 
			      }
				  if(isNewChequeIssued == null || isNewChequeIssued.isEmpty()) {
				  errors.add("Please select whether a new Cheque is issued or not."); }
				  if(isNewChequeIssued.equals("yes")) { 
					  String remarks = updateChequeForm.getRemarks();
					  String newChequeNo = updateChequeForm.getNewChequeNo();
					  if (remarks == null || remarks.isEmpty()) {
					  errors.add("Please select a reason for issuing a new cheque.");
					  }
					  if(newChequeNo == null || newChequeNo.trim().isEmpty()) {
					  errors.add("Please enter the new cheque number.");
					  }
					  if(remarks.equals("Name Correction")||remarks.equals("Death Case") ) {
						  if(name == null || name.trim().isEmpty()) {
							  errors.add("Please enter the Name.");
							} 
					  }
					  if(remarks.equals("Account No Correction") ) {
						  if(accountNo == null || accountNo.trim().isEmpty()) {
							  errors.add("Please enter the Account Number.");
							} 
					  }
					  if(remarks.equals("Name & Account No Correction") ) {
						  if(name == null || name.trim().isEmpty() || accountNo == null || accountNo.trim().isEmpty()) {
							  errors.add("Please enter the correct details (Name & Account Number).");
							} 
					  }
					  if(remarks.equals("Others")) {
						  if(othersRemarks == null || othersRemarks.trim().isEmpty()) {
							  errors.add("Please enter other reasons.");
							} 
					  }
				  }
		        if (!errors.isEmpty()) {
		        	redirect.addFlashAttribute("errors", errors);
					return "redirect:/updateCheque";
		        }
			System.out.println(updateChequeForm);
			boolean updateChequeDetails = updateChequeService.editChequeDetails(updateChequeForm,request);
			if(updateChequeDetails) {
				model.addAttribute("success", "Cheque Updated Successfully");
			}else {
				model.addAttribute("error", "Error Updating Cheque");
			}
			Map<String, Object> result = updateChequeService.getTodaysCheckHistory(request);
			if ("success".equals(result.get("status"))) {
                
                List<UpdateChequeForm> chequeHistoryList = (List<UpdateChequeForm>) result.get("data");
                if (chequeHistoryList != null && !chequeHistoryList.isEmpty()) {
                	model.addAttribute("todayCheckHistory", chequeHistoryList);
				}
                else {
                    model.addAttribute("emptydatamsg", "No data found for today's check history");
                }
            } else {
                model.addAttribute("histroymsg", result.get("message"));  
            }
		}catch (Exception e) {
			e.printStackTrace();
		}
		return "updateCheque";
	}

	@GetMapping("/revChequeDetails")
	public String revChequeDetails(HttpSession session) {
		String userId = null;
		userId = (String) session.getAttribute("userid");
		if(userId==null || userId.equals("null")) {
			 return  "redirect:/";
		  }

		return "revChequeDetails";
		
	}

	@PostMapping("/getChequeDetailsFromTo")
	public String getDetailsFromTo(@ModelAttribute("updateChequeForm") UpdateChequeForm updateChequeForm, Model model,RedirectAttributes redirect) {
		
		try {
			String dateFrom = updateChequeForm.getDateFrom();
			String dateTo = updateChequeForm.getDateTo();
			System.out.println(dateFrom + " " + dateTo);
			SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy");
			if(dateFrom==null || dateFrom.isEmpty() || dateTo==null || dateTo.isEmpty()) {
				redirect.addFlashAttribute("error", "Enter dates correctly");
				return "redirect:/revChequeDetails";
			}
			if (dateFrom!=null || !dateFrom.isEmpty() || dateTo!=null || !dateTo.isEmpty()) {
				Date fromDate = dateFormat.parse(dateFrom);
				Date toDate = dateFormat.parse(dateTo);
				if (fromDate.after(toDate)) {
					redirect.addFlashAttribute("error", "The 'from' date cannot be after the 'to' date");
					return "redirect:/revChequeDetails";
				}
			}
			Response response = updateChequeService.getChequeDetailsFromTo(updateChequeForm);
			if(response.getStatus().equals(HttpStatus.OK)) {
				Map<String, Object> mapData = (Map<String, Object>)  response.getData();
					if(mapData.get("chequeHistory")!=null) {
						model.addAttribute("chequeHistory", mapData.get("chequeHistory"));
						model.addAttribute("dateFrom", dateFrom);
						model.addAttribute("dateTo", dateTo);
					}
				}
			else {
				redirect.addFlashAttribute("error", response.getMessage());
				return "redirect:/revChequeDetails";
			}
		}catch (Exception e) {
			e.printStackTrace();
			redirect.addFlashAttribute("error", "Something went wrong");
			return "redirect:/revChequeDetails";
		}
		return "revChequeDetails";
		
	}

	@GetMapping("/printUpdatedCheque")
	public String printUpdatedCheque(HttpSession session) {
		String userId = null;
		userId = (String) session.getAttribute("userid");
		if(userId==null || userId.equals("null")) {
			 return  "redirect:/";
		  }

		return "printUpdatedCheque";
		
	}

	@PostMapping("/getUpdatedChequeDetails")
	public String getUpdChqDtlsForPrint(@ModelAttribute("updateChequeForm") UpdateChequeForm updateChequeForm, Model model,RedirectAttributes redirect) {
		
		try {
			String cmrfNo = updateChequeForm.getCmrfNo();
			String cmrfYear = updateChequeForm.getCmrfYear();
			String chequeNo = updateChequeForm.getChequeNo();
			String dateFrom = updateChequeForm.getDateFrom();
			String dateTo = updateChequeForm.getDateTo();
			boolean cmrfFlag=false,dateFlag=false;
			System.out.println("CMRF No : "+cmrfNo+"\nCMRF Year : "+cmrfYear+"\nCheque Number : "+chequeNo+"\nDate From : "+dateFrom+"\nDate To : "+dateTo);
			
			if((cmrfNo==null || cmrfNo.isEmpty() || cmrfYear.length() != 10) && (chequeNo==null || chequeNo.isEmpty()) && (dateFrom==null || dateFrom.isEmpty() || dateTo==null || dateTo.isEmpty())) {
				redirect.addFlashAttribute("error", "Please Enter the data correctly");
				return "redirect:/printUpdatedCheque";
			}
			Response response = null;
			 
			if((!cmrfNo.isEmpty() || cmrfYear.length() == 10) || (!chequeNo.isEmpty())) {
				System.out.println("IN CMRF FLAG");
				cmrfFlag=true;
				dateFlag=false;
				response = updateChequeService.getUpdChqDtlsForPrint(updateChequeForm,cmrfFlag,dateFlag);
			}else if((!dateFrom.isEmpty()) && (!dateTo.isEmpty())) {
				System.out.println("IN DATES FLAG");
				dateFlag=true;
				cmrfFlag=false;
				response = updateChequeService.getUpdChqDtlsForPrint(updateChequeForm,cmrfFlag,dateFlag);
			}
			// Response response = updateChequeService.getUpdDtlsChqForPrint(updateChequeForm);
			if(response != null && response.getStatus().equals(HttpStatus.OK)) {
				Map<String, Object> mapData = (Map<String, Object>)  response.getData();
					if(mapData.get("chequeHistory")!=null) {
						model.addAttribute("chequeHistory", mapData.get("chequeHistory"));
						model.addAttribute("dateFrom", dateFrom);
						model.addAttribute("dateTo", dateTo);
					}
				}
			else {
				redirect.addFlashAttribute("error", response.getMessage());
				return "redirect:/printUpdatedCheque";
			}
		}catch (Exception e) {
			e.printStackTrace();
			redirect.addFlashAttribute("error", "Something went wrong");
			return "redirect:/printUpdatedCheque";
		}
		return "printUpdatedCheque";
		
	}

	@PostMapping("/printLetter")
	public String printLetter(@ModelAttribute("UpdateChequeForm") UpdateChequeForm updateChequeForm,Map<String, Object> model,RedirectAttributes redirectAttributes,HttpServletRequest request) throws Exception {
		boolean updFlag=false;
		Map<String, Object> updateResult;
		String message=null;
		List<UpdateChequeForm>  chequeList = new ArrayList<UpdateChequeForm>();
		try {
			String cmrfNo = updateChequeForm.getCmrfNo();
			String newChequeNo = updateChequeForm.getNewChequeNo();
			System.out.println(cmrfNo + " --- " + newChequeNo);
			updateChequeForm.setIpAddr(request.getRemoteAddr());
			updateChequeForm.setUserId((String)request.getSession().getAttribute("userid"));
			if (cmrfNo != null && newChequeNo != null) {
				chequeList = updateChequeService.generateBeneficiaryLetters(cmrfNo, newChequeNo);
				System.out.println(chequeList.size()>0);
				if (!chequeList.isEmpty()) {
					updateResult = updateChequeService.updateProceedings(updateChequeForm);
					updFlag = (boolean) updateResult.getOrDefault("flag", false);
                	message = (String) updateResult.getOrDefault("message", "Unexpected Error");
				
					if (updFlag) {
						if(chequeList.size()>0) {
							model.put("dirExgList",chequeList);
							return "/proceedings/beneficiaryCheque";
						}
					}else{
						redirectAttributes.addFlashAttribute("error", message);
						return "redirect:/printUpdatedCheque";
					}
				}else{
					redirectAttributes.addFlashAttribute("error", "Some Error in Fetching Details");
					return "redirect:/printUpdatedCheque";
				}
			}
			// }
			
		}catch(Exception e) {
			e.printStackTrace();
			model.put("error",e.getMessage());
		}
		return "printUpdatedCheque";
	}
	
	
	@GetMapping("/reprintChequeReport")
	public String viewPage() {
		return "chequeReprintReport";
	}
	
	@PostMapping("/reprintChequeReport")
	public String getDeoTokenVerifiedReportCount(@ModelAttribute("updateChequeForm") UpdateChequeForm updateChequeForm,
			HttpSession session, Model model, RedirectAttributes redirect,HttpServletRequest request) throws Exception {
		
		List chequeReprintDetails =null;
		
		try {
			chequeReprintDetails = updateChequeService.getChequeReprintDetails(updateChequeForm);
			if (chequeReprintDetails.size() > 0) {
				model.addAttribute("chequeReprintDetails", chequeReprintDetails);
			//	request.getSession().setAttribute("formData", deoVerifiedStatusForm);
				model.addAttribute("dateFrom", updateChequeForm.getDateFrom());
				model.addAttribute("dateTo", updateChequeForm.getDateTo());
			}else {
				model.addAttribute("msg", "No records found!");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "chequeReprintReport";
	}

	@PostMapping("/checkChequeNoIsExists")
    public ResponseEntity<Boolean> checkChequeNoIsExists(@RequestParam String newChequeNo) throws Exception {
        boolean isValid = updateChequeService.checkChequeNoIsExists(newChequeNo);
        return ResponseEntity.ok(isValid);
    }
}

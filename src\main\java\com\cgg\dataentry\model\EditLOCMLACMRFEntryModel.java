package com.cgg.dataentry.model;

import java.io.Serializable;

public class EditLOCMLACMRFEntryModel implements Serializable { 
	
	private String patient_name;
	private String father_name;
	private String patDistrict=null;
	private String Address;
	private String purpose;
	private String assured_amt;
	private String recommendedBy,constNo,constName;
	private String vipletter_date;
	private String hospCode,hospName;
	private String status;
	private String loc_mla_no;
	private String mla_cmrf_year;
	private String loc_token;
	private String aadharNo;
	private String mobileNo;
	private String distNo,distName;
	
	public String getLoc_token() {
		return loc_token;
	}
	public void setLoc_token(String loc_token) {
		this.loc_token = loc_token;
	}
	public String getMla_cmrf_year() {
		return mla_cmrf_year;
	}
	public void setMla_cmrf_year(String mla_cmrf_year) {
		this.mla_cmrf_year = mla_cmrf_year;
	}
	public String getPatient_name() {
		return patient_name;
	}
	public void setPatient_name(String patient_name) {
		this.patient_name = patient_name;
	}
	public String getFather_name() {
		return father_name;
	}
	public void setFather_name(String father_name) {
		this.father_name = father_name;
	}
	public String getAddress() {
		return Address;
	}
	public void setAddress(String address) {
		Address = address;
	}
	public String getPurpose() {
		return purpose;
	}
	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}
	public String getAssured_amt() {
		return assured_amt;
	}
	public void setAssured_amt(String assured_amt) {
		this.assured_amt = assured_amt;
	}
	public String getRecommendedBy() {
		return recommendedBy;
	}
	public void setRecommendedBy(String recommendedBy) {
		this.recommendedBy = recommendedBy;
	}
	public String getConstNo() {
		return constNo;
	}
	public void setConstNo(String constNo) {
		this.constNo = constNo;
	}
	public String getConstName() {
		return constName;
	}
	public void setConstName(String constName) {
		this.constName = constName;
	}
	public String getVipletter_date() {
		return vipletter_date;
	}
	public void setVipletter_date(String vipletter_date) {
		this.vipletter_date = vipletter_date;
	}
	public String getHospCode() {
		return hospCode;
	}
	public void setHospCode(String hospCode) {
		this.hospCode = hospCode;
	}
	public String getHospName() {
		return hospName;
	}
	public void setHospName(String hospName) {
		this.hospName = hospName;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getLoc_mla_no() {
		return loc_mla_no;
	}
	public void setLoc_mla_no(String loc_mla_no) {
		this.loc_mla_no = loc_mla_no;
	}
	public String getAadharNo() {
		return aadharNo;
	}
	public void setAadharNo(String aadharNo) {
		this.aadharNo = aadharNo;
	}
	public String getMobileNo() {
		return mobileNo;
	}
	public void setMobileNo(String mobileNo) {
		this.mobileNo = mobileNo;
	}
	public String getPatDistrict() {
		return patDistrict;
	}
	public void setPatDistrict(String patDistrict) {
		this.patDistrict = patDistrict;
	}
	public String getDistNo() {
		return distNo;
	}
	public void setDistNo(String distNo) {
		this.distNo = distNo;
	}
	public String getDistName() {
		return distName;
	}
	public void setDistName(String distName) {
		this.distName = distName;
	}
		

}


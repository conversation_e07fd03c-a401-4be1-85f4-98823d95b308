package com.cgg.proceedings.controller;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.cgg.proceedings.model.PRAckLettersForm;
import com.cgg.proceedings.model.PRDataWrapper;
import com.cgg.proceedings.service.PRAckLettersService;
import com.cgg.reports.entities.uploadAckLetterForm;

@Controller
@RequestMapping(value = "/prAckLetters")
public class PRAckLettersController {
    
    @Autowired
    PRAckLettersService prAckService;
    
    @GetMapping
    public String viewPRAckLetters(Map<String, Object> model) throws Exception {
        PRAckLettersForm prAckForm = new PRAckLettersForm();
        model.put("prAckForm", prAckForm);
        return "prAckLetters";
    }
    
    @PostMapping
    public String getPRAckLetters(@ModelAttribute PRAckLettersForm prAckForm,Map<String, Object> model) throws Exception {
        try {
            System.out.println("FORM : " + prAckForm.toString());
            List<?> prAckList = prAckService.getPRAckLetters(prAckForm);

            String fromDate = prAckForm.getFromDate();
            String toDate = prAckForm.getToDate();

            DateTimeFormatter inputFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd"); // incoming format
            DateTimeFormatter outputFormat = DateTimeFormatter.ofPattern("dd-MM-yyyy"); // desired format

            LocalDate fromLocalDate = LocalDate.parse(fromDate, inputFormat);
            LocalDate toLocalDate = LocalDate.parse(toDate, inputFormat);

            String dateFrom = fromLocalDate.format(outputFormat);
            String dateTo = toLocalDate.format(outputFormat);

            // System.out.println("prAckList size: " + prAckList.toString());
            if(prAckList !=null && !prAckList.isEmpty()) {
                model.put("prAckList", prAckList);
                model.put("dateFrom", dateFrom);
                model.put("dateTo", dateTo);
                model.put("prAckForm", prAckForm);
            }else {
                model.put("error", "No Data Found");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "prAckLetters";
    }

    @GetMapping("/getCmpDetails")
    @ResponseBody
    public List<PRAckLettersForm> getCmpDetails(
        @RequestParam String dateFrom,
        @RequestParam String dateTo,
        @RequestParam String cno,
        @RequestParam String otherConst
    ) throws Exception {
        return prAckService.getCmpDetails(dateFrom, dateTo, cno, otherConst);
    }

    @RequestMapping("/printAckLetter")
    public String printAckLetter(@RequestParam String dateFrom, @RequestParam String dateTo, @RequestParam String cno, @RequestParam String otherConst, Model model) throws Exception {
        try {
            Integer chequesCnt = 0;

            List<PRAckLettersForm> cmpDetails = prAckService.getCmpDetails(dateFrom, dateTo, cno, otherConst);

            for (PRAckLettersForm prAckLettersForm : cmpDetails) {
                chequesCnt += Integer.parseInt(prAckLettersForm.getCheques_cnt());
            }

            String totalCheques = String.valueOf(chequesCnt);

            String dataForQR = "PR/ACKSCAN/" + totalCheques + "/" + cno + "/" + otherConst + "/" + cmpDetails.get(0).getEsigned_date();

            model.addAttribute("dataForQR", dataForQR);
            model.addAttribute("cmpDetailsList", cmpDetails);
        } catch (Exception e) {
            e.printStackTrace();
        }
        // return "prAckLetters";
        return "/proceedings/printPRAckLetter";
    }

    @RequestMapping("/printAllAckLetters")
    public String printAllAckLetters(@RequestParam String dateFrom,
                                    @RequestParam String dateTo,
                                    Model model) throws Exception {
        try {
            // Fetch all PRs' data within date range
            List<PRAckLettersForm> allCmpDetails = prAckService.getPRCmpDetails(dateFrom, dateTo);

            // Group by CNO (PR-wise)
            Map<String, List<PRAckLettersForm>> prWiseMap = new LinkedHashMap<>();
            for (PRAckLettersForm form : allCmpDetails) {
                String key;
                
                if ("998".equals(form.getCno())) {
                    // Special key using both cno and other_const
                    key = form.getCno() + "~" + form.getOther_const();
                } else {
                    key = form.getCno(); // Default grouping by CNO
                }

                prWiseMap.computeIfAbsent(key, k -> new ArrayList<>()).add(form);
            }

            // System.out.println("PR WISE MAP : " + prWiseMap);

            // Build a list of PR-wise data + QR
            List<PRDataWrapper> prDataList = new ArrayList<>();
            for (Map.Entry<String, List<PRAckLettersForm>> entry : prWiseMap.entrySet()) {
                String key = entry.getKey();
                List<PRAckLettersForm> cmpList = entry.getValue();

                String cno;
                String groupedOtherConst;

                if (key.contains("~")) {
                    String[] parts = key.split("~");
                    cno = parts[0];
                    groupedOtherConst = parts[1];
                } else {
                    cno = key;
                    groupedOtherConst = cmpList.get(0).getOther_const();;
                }

                int totalCheques = 0;
                for (PRAckLettersForm form : cmpList) {
                    totalCheques += Integer.parseInt(form.getCheques_cnt());
                }

                String esignedDate = cmpList.get(0).getEsigned_date();
                String otherConst = cmpList.get(0).getOther_const();
                String dataForQR = "PR/ACKSCAN/" + totalCheques + "/" + cno + "/" + otherConst + "/" + esignedDate;

                PRDataWrapper wrapper = new PRDataWrapper();
                wrapper.setCno(cno);
                wrapper.setCno(groupedOtherConst);
                wrapper.setCmpDetails(cmpList);
                wrapper.setDataForQR(dataForQR);

                prDataList.add(wrapper);
            }

            // System.out.println("PR DATA LIST : " + prDataList);

            model.addAttribute("prDataList", prDataList);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "/proceedings/printAllPRAckLetters";
        // return null;
    }

    @PostMapping(value="/uploadAcknowledgementLetter")
	public String uploadAckLetter(HttpServletRequest request, @ModelAttribute uploadAckLetterForm uploadAckLetterForm, RedirectAttributes redirect) throws Exception {
		System.out.println("uploadAckLetter : " + uploadAckLetterForm.toString());
		try {
			String userId = null;
			HttpSession session = request.getSession();
			if(session!=null && session.getAttribute("userid")!=null) {
				userId=(String)session.getAttribute("userid");
				if(userId==null || userId.equals("null")){
					return "redirect:/sessionExpired";
				} 
			}
			
			uploadAckLetterForm.setEnteredBy(userId);
			uploadAckLetterForm.setEnteredIp(request.getRemoteAddr());

			Map<String, String> uploadAckLetter = prAckService.uploadAckLetter(uploadAckLetterForm);
			if(uploadAckLetter.get("status").equals("true")) {
				redirect.addFlashAttribute("success", uploadAckLetter.get("message"));
			}else {
				redirect.addFlashAttribute("error", uploadAckLetter.get("message"));
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
		return "redirect:/prAckLetters";
	}
}

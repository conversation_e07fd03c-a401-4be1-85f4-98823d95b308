<%@ page language="java" import="java.util.*" pageEncoding="ISO-8859-1"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%
	String path = request.getContextPath();
	String basePath = path + "/";
%>

<!DOCTYPE HTML>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<title>Cheques Received By PR in Month Report</title>

<link rel="stylesheet" href="css/jquery-ui.css">
<script type="text/javascript" src="https://code.jquery.com/jquery-1.11.3.min.js"></script>
<script src="js/jquery-1.12.4.js"></script>
<script src="js/jquery-ui.js"></script>
<jsp:include page="/WEB-INF/jsp/include_DT.jsp" />


<style type="text/css">
.main_content {
	min-height: 85vh;
}
.display thead tr th {
	text-align: center;
}
</style>


<script type="text/javascript">
$(document).ready(function() {
	var titleValue = "cheques received by PR Report";
		$('#myTablePR').DataTable({
			dom : 'Bfrtip',
			paging : true,
			lengthMenu: [
				[10, 20, 50, 100, -1],
				[10, 20, 50, 100, 'All']
			],
			pageLength: -1,
			processing : true,
			language : {
				"loadingRecords" : "Please wait - loading..."
			},
			buttons : [
			{
				extend : 'print',
				text: '<i class="fa fa-print"></i>',
				title : titleValue,
				titleAttr: 'Print',
				orientation : 'landscape',
				pageSize : 'A4',
			},

			{
				extend : 'excelHtml5',
				text: '<i class="fa fa-file-excel-o"></i>',
				title : titleValue,
				titleAttr: 'Export to Excel',
				orientation : 'landscape',
				pageSize : 'A4',
			}, {
				extend : 'pdfHtml5',
				text: '<i class="fa fa-file-pdf-o"></i>',
				title : titleValue,
				titleAttr: 'Export to PDF',
				orientation : 'landscape',
				pageSize : 'A4',
				customize: function(doc) {
                       
                        doc.styles.tableBodyEven.alignment = 'center';
                        doc.styles.tableBodyOdd.alignment = 'center';
                        doc.styles.tableHeader.alignment = 'center';
                        doc.styles.tableHeader.fillColor = '#563d7c';
                        doc.styles.tableHeader.color = 'white';
                        doc.styles.tableBodyEven.fontSize = 10; 
                        doc.styles.tableBodyOdd.fontSize = 10; 
                        doc.styles.tableHeader.fontSize = 12;
                    }
			},
			{
                extend: 'colvis',
                text: '<i class="fa fa-list"></i>',
                titleAttr: 'Show/Hide Columns'
            },
			{
                extend: 'pageLength',
                titleAttr: 'Page Length',
                lengthMenu: [
                    [10, 20, 50, 100, -1],
                    [10, 20, 50, 100, 'All']
                ],
            }
		],		
		"footerCallback": function (row, data, start, end, display) {
		    var api = this.api();
		    
		    var parseValue = function(val) {
		        // Extract number even if it's inside an <a> tag
		        var numericVal = $('<div>').html(val).text().trim(); 
		        return typeof numericVal === 'string' ?
		            parseFloat(numericVal.replace(/,/g, '')) :
		            (typeof numericVal === 'number' ? numericVal : 0);
		    };
		    
		    var totalLocCount = api.column(2, { page: 'current' }).data().reduce(function (acc, curr) {
		        return parseValue(acc) + parseValue(curr);
		    }, 0);
		    
		    $(api.column(2).footer()).html(totalLocCount.toLocaleString('en-IN'));

		    var totalAssuredAmt = api.column(3, { page: 'current' }).data().reduce(function (acc, curr) {
		        return parseValue(acc) + parseValue(curr);
		    }, 0);

		    $(api.column(3).footer()).html(totalAssuredAmt.toLocaleString('en-IN'));
		},
			initComplete: function () {
              // Focus on the search text box
              $('#myTablePR_filter input').focus();
          }
		})
	});

</script>
</head>

<body>
	<div class="container-fluid main_content" style="background: white; border-top: 5px solid black;margin: 20px;padding: 20px;">
	
	   <c:if test="${not empty msg}">
           <br>
           <div class="col-sm-12">
              <div style="color:red;font-size: 20px;" class="text-center"><b>${msg}</b></div>
           </div>
          <br>
       </c:if> 
	   
	   <input type="hidden" name="monthYear" id="monthYear" value="${monthYear}" /> 
	
			<c:if test="${chequesReceivedByPRMonthReportDataFlag}"> 
				<div class="container">
					<h3 style="text-align: center;">Cheques Received By PR Report  ${monthYear}</h3>
						
					<div style="display: flex; justify-content: end;margin-bottom: 10px;">
						<button type="button" class="btn btn-success" onclick="window.history.go(-1);">Back</button>
					</div>
							
					<hr>
					<table border="2" align="center" style="font-size: 13px;" 	class="display" id="myTablePR">
						<thead style="background: #563d7c;color: white;">
							<tr>
								<th>Sl No.</th>								
					            <th>People Representative Name</th>
					            <th>Cheques Received By PR</th>
					            <th>Acknowledgement's Scanned</th>
							</tr>
						</thead>
					    <tbody>
							<c:forEach var="monthlySanReport" items="${chequesReceivedByPRMonthReportData}" varStatus="row">
								<tr>
									<td>${row.index+1}</td>								
									<td>${monthlySanReport.mlaMpName}</td>									
									<td align="right">
										<c:choose>
											<c:when test="${monthlySanReport.totalChqRecvByPR != '0'}" >
												<a href="monthWiseSummary/monthYearPRCmrfAckDetails?monthYear=${dateMonthYear}&cno=${monthlySanReport.cno}&otherConst=${monthlySanReport.otherConst}">
													<c:out value="${monthlySanReport.totalChqRecvByPR}"/>
												</a>
											</c:when>
											<c:otherwise>
												<c:out value="${monthlySanReport.totalChqRecvByPR}"/>
											</c:otherwise>
										</c:choose>
					        		</td>
				               		<td align="right">
										<c:choose>
											<c:when test="${monthlySanReport.totalChqScanByPR != '0'}" >
												<a href="monthWiseSummary/monthYearPRCmrfAckDetails?monthYear=${dateMonthYear}&cno=${monthlySanReport.cno}&otherConst=${monthlySanReport.otherConst}&scan=true">
													<c:out value="${monthlySanReport.totalChqScanByPR}"/>
												</a>
											</c:when>
											<c:otherwise>
												<c:out value="${monthlySanReport.totalChqScanByPR}"/>
											</c:otherwise>
										</c:choose>
									</td>
									
								</tr>
							</c:forEach>
						</tbody>
						 <tfoot>
							<tr>
								<th colspan="2">Total</th>
								<th style="text-align: right;"></th>
								<th style="text-align: right;"></th>
							</tr>
						</tfoot> 
					</table>
				</div>
			 </c:if>	 	
	</div>
	
	<script type="text/javascript">
		$('#myTablePR').addClass('table-bordered');
	</script>
	
</body>
</html>

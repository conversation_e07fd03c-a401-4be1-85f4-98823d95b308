package com.cgg.reports.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.cgg.common.Encryptor;
import com.cgg.reports.model.MonthWiseSummary;


@Repository
public class MonthWiseChequesReceivedByPRDao {
	
	
	@Autowired
	private DataSource dataSource;

	@Autowired
	JdbcTemplate jdbcTemlate;
	
	public List<MonthWiseSummary> getChequesReceivedByPRMonthReport(String dateFrom,String dateTo) throws Exception 
	{
		Connection con = null;
		PreparedStatement pst = null;
		ResultSet rs = null;
		List<MonthWiseSummary> monthWiseSummaryList = new ArrayList<MonthWiseSummary>();
		
		try 
		{
			con = dataSource.getConnection();					
			pst = con.prepareStatement(" SELECT  COALESCE(pal.other_const, '-') AS other_const,"
					+ " CASE  WHEN c.cno = '998' THEN 'PEOPLE REPRESENTATIVE ' || COALESCE('(' || pal.other_const || ')', '') "
					+ " WHEN c.cno IN ('999', '997', '194') THEN mlamp   ELSE mlamp || ' (' || COALESCE(cname, '') || ')'  END AS mlamp, "
					+ " SUM(CASE WHEN pal.is_qr_scanned in (true,false) THEN pal.total_cheques ELSE 0 END) AS total_ack_rec_by_pr, "
					+ " SUM(CASE WHEN pal.is_qr_scanned is true THEN pal.total_cheques ELSE 0 END) AS total_ack_scan_by_pr, pal.cno "
					+ " FROM pr_acknowledgement_letters pal "				
					+ " LEFT JOIN constituency c on c.cno = pal.cno "
					+ " WHERE pal.esigned_date::date >= TO_DATE('"+dateFrom+"', 'DD-MM-YYYY') AND pal.esigned_date::date <= TO_DATE('"+dateTo+"', 'DD-MM-YYYY') "
					+ " GROUP BY c.mlamp, c.cname,c.cno,pal.other_const, pal.cno order by mlamp  ");
			
			System.out.println("getChequesReceivedByPRMonthReport() sql --->>"+pst);
			
			rs = pst.executeQuery();
			while(rs.next())
			{
				MonthWiseSummary monthWiseSummaryobj = new MonthWiseSummary();				
				monthWiseSummaryobj.setMlaMpName(rs.getString("mlamp"));
				monthWiseSummaryobj.setTotalChqRecvByPR(rs.getString("total_ack_rec_by_pr"));
				monthWiseSummaryobj.setTotalChqScanByPR(rs.getString("total_ack_scan_by_pr"));
				
				monthWiseSummaryobj.setCno(Encryptor.encrypt(rs.getString("cno")));
				if(rs.getString("cno")!=null && rs.getString("cno").equals("998")) {
					monthWiseSummaryobj.setOtherConst(Encryptor.encrypt(rs.getString("other_const")));
			    }else {
				   monthWiseSummaryobj.setOtherConst(Encryptor.encrypt("-"));
				}
				
				monthWiseSummaryList.add(monthWiseSummaryobj);
			}
			
		}catch (Exception e) {
			e.printStackTrace();
		}finally {
			if(rs!=null) {rs.close();rs=null;}
			if(pst!=null) {pst.close();pst=null;}
			if(con!=null && !con.isClosed()) {con.close();con=null;}
		}
		
	  return monthWiseSummaryList;
	}

}

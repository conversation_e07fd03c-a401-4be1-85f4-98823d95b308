package com.cgg.dataentry.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.cgg.dataentry.model.MlaCmrfEntityModel;
import com.cgg.dataentry.service.UpdateOldBundleService;


@Controller
public class UpdateOldBundleController {

	@Autowired
	UpdateOldBundleService updateOldBundleService;

	@GetMapping("/updateOldBundle")
	public String updateOldBundleView(HttpServletRequest request) {
		HttpSession session = request.getSession();
		// List<String> validRoleIds = Arrays.asList("25");
	//	List<String> validRoleIds = Arrays.asList(ApplicationConstants.PS_OSD_CM_ROLE, ApplicationConstants.CMRF_OSD_ROLE);
		String userId = null;
		String roleId = null;
		userId = (String) session.getAttribute("userid");
		roleId = (String) session.getAttribute("rolesStr");
	/*	if (roleId == null || !validRoleIds.contains(roleId) || userId == null) {
			return "redirect:/";
		}*/
		return "updateOldBundle";
	}

	
	@PostMapping("getOldTokenDetails")
	public String getOldTokenDetails(HttpServletRequest request ,Model model,@RequestParam("mlaCmrfNo") String mlaCmrfNo,@RequestParam("year") String year,
			RedirectAttributes redirect) {
		
		String mlatTokenNo=mlaCmrfNo+year;
		MlaCmrfEntityModel mlaCmrfDetails = updateOldBundleService.getOldTokenDetails(mlatTokenNo);	
		
		if(mlaCmrfDetails==null) {
			redirect.addFlashAttribute("error","Details Not Found");
			return  "redirect:/updateOldBundle";
		} else if(mlaCmrfDetails.getBatchSerialNo() != null) {
			redirect.addFlashAttribute("error","Bundle Already Updated with Serial No. - "+mlaCmrfDetails.getBatchSerialNo());
			return  "redirect:/updateOldBundle";
		}

		mlaCmrfDetails.setStatus(updateOldBundleService.getStatusName(mlaCmrfDetails.getStatus()));

		model.addAttribute("mlaCmrfDetails", mlaCmrfDetails);
		return "updateOldBundle";
	}
	
	
	@PostMapping("updateOldBundleDetails")
	public String updateOldBundleDetails(HttpServletRequest request,Model model,@RequestParam("mlaCmrfNo1") String mlaCmrfNo) {
	
		HttpSession session = request.getSession();
		String userId = (String) session.getAttribute("userid");
	
		if (userId == null) {
			return "redirect:/";
		}

        String data = updateOldBundleService.updateOldBundle(mlaCmrfNo, userId);
		
		if (data ==null) {
			model.addAttribute("error", "Details Not Found");
		} else {
			model.addAttribute("success", "Batch Updated Successfully--"+data);		
		}
		
		return "updateOldBundle";
	}
	
	

}

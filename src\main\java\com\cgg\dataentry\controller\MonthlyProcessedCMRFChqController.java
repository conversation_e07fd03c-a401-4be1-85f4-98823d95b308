package com.cgg.dataentry.controller;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.cgg.dataentry.service.MonthlyProcessedCMRFChqService;

@Controller
public class MonthlyProcessedCMRFChqController {
	
	@Autowired MonthlyProcessedCMRFChqService chqService;

	@GetMapping("/addMonthlyProcCheq")
	public String monthlyProcessedChqEntry(Map<String, Object> model, HttpServletRequest request,
			RedirectAttributes redirectAttributes) throws Exception {
		HttpSession session = request.getSession();
		String userid = (String) session.getAttribute("userid");
		if (userid == null || userid.equals("null") || !userid.equals("cmrf_cgg")) {
			redirectAttributes.addFlashAttribute("msg", "Please try again");
			return "redirect:/";
		}
		return "addMonthlyProcCheq";
	}
	@PostMapping("/uploadMonthlyCheque")
	public String handleChequeUpload(@RequestParam("month") String month,
	                                 @RequestParam("year") String year,
	                                 @RequestParam("excelFile") MultipartFile file,
									 HttpServletRequest request,
	                                 RedirectAttributes redirectAttributes) {
			HttpSession session = request.getSession();
			String userid = (String) session.getAttribute("userid");
	    try {
	    	chqService.processExcelAndInsert(month, year, file, userid);
	        redirectAttributes.addFlashAttribute("success", "File uploaded and data processed successfully.");
	    } catch (Exception e) {
	        redirectAttributes.addFlashAttribute("error", "Error: " + e.getMessage());
	        e.printStackTrace();
	    }
	    return "redirect:/addMonthlyProcCheq";
	}

}

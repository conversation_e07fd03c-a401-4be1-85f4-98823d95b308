package com.cgg.dataentry.model;

public class LocDetailsEntryForm {
private String patientName;
private String fatherName;
private String aadharNo;
private String mobileNo;
private String patDistrict=null;
private String address;
private String purpose;
private String assuredAmt;
private String Recommended;
private String vipLetterDate;
private String hospCode;
private String hospName;
private String txthospname;
private String locTokenSno;
private String letterDate;
private String prefix;
private String vipName;
private String prevLocNo;
private String userId;
private String previousAssuredAmt;
private String preLocDate;
private String locType;
private String ipAddress;
private String distNo,distName;
private String prMobileNo;
private String supdtMobileNo;
private String filePath;
private String cmcoReferredBy;



public String getFilePath() {
	return filePath;
}
public void setFilePath(String filePath) {
	this.filePath = filePath;
}
public String getPrMobileNo() {
	return prMobileNo;
}
public void setPrMobileNo(String prMobileNo) {
	this.prMobileNo = prMobileNo;
}
public String getIpAddress() {
	return ipAddress;
}
public void setIpAddress(String ipAddress) {
	this.ipAddress = ipAddress;
}
public String getLocType() {
	return locType;
}
public void setLocType(String locType) {
	this.locType = locType;
}
public String getPreLocDate() {
	return preLocDate;
}
public void setPreLocDate(String preLocDate) {
	this.preLocDate = preLocDate;
}
public String getPreviousAssuredAmt() {
	return previousAssuredAmt;
}
public void setPreviousAssuredAmt(String previousAssuredAmt) {
	this.previousAssuredAmt = previousAssuredAmt;
}
public String getFormatAmt() {
	return formatAmt;
}
public void setFormatAmt(String formatAmt) {
	this.formatAmt = formatAmt;
}
public String getAmtInWords() {
	return amtInWords;
}
public void setAmtInWords(String amtInWords) {
	this.amtInWords = amtInWords;
}
private String formatAmt;
private String amtInWords;

public String getPrevLocNo() {
	return prevLocNo;
}
public void setPrevLocNo(String prevLocNo) {
	this.prevLocNo = prevLocNo;
}
public String getPrefix() {
	return prefix;
}
public void setPrefix(String prefix) {
	this.prefix = prefix;
}
public String getVipName() {
	return vipName;
}
public void setVipName(String vipName) {
	this.vipName = vipName;
}
private String vipDesg;


public String getVipDesg() {
	return vipDesg;
}
public void setVipDesg(String vipDesg) {
	this.vipDesg = vipDesg;
}
public String getLetterDate() {
	return letterDate;
}
public void setLetterDate(String letterDate) {
	this.letterDate = letterDate;
}
public String getLocNo() {
	return locNo;
}
public void setLocNo(String locNo) {
	this.locNo = locNo;
}
private String locToken;
private String locNo;
public String getLocToken() {
	return locToken;
}
public void setLocToken(String locToken) {
	this.locToken = locToken;
}
public String getLocTokenSno() {
	return locTokenSno;
}
public void setLocTokenSno(String locTokenSno) {
	this.locTokenSno = locTokenSno;
}
public String getTxthospname() {
	return txthospname;
}
public void setTxthospname(String txthospname) {
	this.txthospname = txthospname;
}
private String recommendedBy,constName,constNo;
public String getPatientName() {
	return patientName;
}
public void setPatientName(String patientName) {
	this.patientName = patientName;
}
public String getAddress() {
	return address;
}
public void setAddress(String address) {
	this.address = address;
}
public String getPurpose() {
	return purpose;
}
public void setPurpose(String purpose) {
	this.purpose = purpose;
}
public String getAssuredAmt() {
	return assuredAmt;
}
public void setAssuredAmt(String assuredAmt) {
	this.assuredAmt = assuredAmt;
}
public String getRecommended() {
	return Recommended;
}
public void setRecommended(String recommended) {
	Recommended = recommended;
}
public String getVipLetterDate() {
	return vipLetterDate;
}
public void setVipLetterDate(String vipLetterDate) {
	this.vipLetterDate = vipLetterDate;
}
public String getHospCode() {
	return hospCode;
}
public void setHospCode(String hospCode) {
	this.hospCode = hospCode;
}
public String getHospName() {
	return hospName;
}
public void setHospName(String hospName) {
	this.hospName = hospName;
}
public String getFatherName() {
	return fatherName;
}
public void setFatherName(String fatherName) {
	this.fatherName = fatherName;
}
public String getRecommendedBy() {
	return recommendedBy;
}
public void setRecommendedBy(String recommendedBy) {
	this.recommendedBy = recommendedBy;
}
public String getConstName() {
	return constName;
}
public void setConstName(String constName) {
	this.constName = constName;
}
public String getConstNo() {
	return constNo;
}
public void setConstNo(String constNo) {
	this.constNo = constNo;
}
public String getUserId() {
	return userId;
}
public void setUserId(String userId) {
	this.userId = userId;
}
public String getAadharNo() {
	return aadharNo;
}
public void setAadharNo(String aadharNo) {
	this.aadharNo = aadharNo;
}
public String getMobileNo() {
	return mobileNo;
}
public void setMobileNo(String mobileNo) {
	this.mobileNo = mobileNo;
}
public String getPatDistrict() {
	return patDistrict;
}
public void setPatDistrict(String patDistrict) {
	this.patDistrict = patDistrict;
}
public String getDistNo() {
	return distNo;
}
public void setDistNo(String distNo) {
	this.distNo = distNo;
}
public String getDistName() {
	return distName;
}
public void setDistName(String distName) {
	this.distName = distName;
}
public String getSupdtMobileNo() {
	return supdtMobileNo;
}
public void setSupdtMobileNo(String supdtMobileNo) {
	this.supdtMobileNo = supdtMobileNo;
}
public String getCmcoReferredBy() {
	return cmcoReferredBy;
}
public void setCmcoReferredBy(String cmcoReferredBy) {
	this.cmcoReferredBy = cmcoReferredBy;
}

}

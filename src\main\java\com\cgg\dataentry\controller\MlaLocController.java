package com.cgg.dataentry.controller;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.cgg.common.ApplicationConstants;
import com.cgg.common.CommonFunctions;
import com.cgg.common.UIDVerfication;
import com.cgg.dataentry.model.LOCMLACMRFEntryModel;
import com.cgg.dataentry.service.LOCMLACMRFEntryService;
import com.cgg.hospital.service.InwardCmrfService;


@Controller
@RequestMapping(value = "/mlaLoc")
public class MlaLocController {

    @Autowired
	private LOCMLACMRFEntryService locService;

	@Autowired
	private InwardCmrfService inwardService;

	public String FILES_UPLOAD_PATH = "/Uploads/tgcmrf/FILES/";
	
	@RequestMapping(method = RequestMethod.GET)
    public String locMlaCmrfView(Map<String, Object> model,HttpSession session,RedirectAttributes redirect)throws Exception {
		String userId=null;
		String roleId = (String) session.getAttribute("rolesStr");
		if(session!=null && session.getAttribute("userid")!=null) 
			userId=(String)session.getAttribute("userid");
		 if(userId==null || userId.equals("null") ) {
				System.out.println("enhancementlocDetails"+userId);
			 return  "redirect:/mlaLoc";
		  }
		 if (Integer.parseInt(roleId) != ApplicationConstants.MLA_ROLE) {
			 return  "redirect:/";
			}
		LOCMLACMRFEntryModel locEntryForm = new LOCMLACMRFEntryModel();
		
		List<LOCMLACMRFEntryModel> recommendedList = new ArrayList<LOCMLACMRFEntryModel>();  
		recommendedList = locService.getRecommendedDetails(session);
		
		List<LOCMLACMRFEntryModel> hospitalList = new ArrayList<LOCMLACMRFEntryModel>();  
		hospitalList = locService.getHospitalDetails(session);

		List<LOCMLACMRFEntryModel> districts = new ArrayList<LOCMLACMRFEntryModel>();
		districts=locService.getDistricts();
		
		if(recommendedList.size()>0) {
			model.put("recommendedList",recommendedList);
			LOCMLACMRFEntryModel locModel=	recommendedList.get(0);
			if("999".equals(locModel.getConstNo())) {
				model.put("referredByDiv",true);
			}			
		}
		model.put("districts",districts);
		model.put("hospitalList",hospitalList);
        model.put("locEntryForm", locEntryForm);
         
		
        return "mlaLoc";
    }
	
	@RequestMapping(method = RequestMethod.POST)
    public String saveMlaLocCmrfEntry(@ModelAttribute("locEntryForm") LOCMLACMRFEntryModel locEntryForm,Map<String, Object> model,
    		RedirectAttributes redirectAttributes,HttpSession session,HttpServletRequest request) throws Exception {

		String userId=null;
		String aadharNo=null;
		String result = null;
		String msg="";
		String succMsg="";
		String treatmentDetails=null;
		String incomeCerNo=null;
		String newFscNo=null;
		 String opcrNo=null;
		 String referredBy=null;

		if(session!=null && session.getAttribute("userid")!=null) 
			userId=(String)session.getAttribute("userid");
		if(userId==null || userId.equals("null") ) 
			return  "redirect:/mlaLoc";
		  
        try {
        	   locEntryForm.setUserId(userId);
        	   
	           aadharNo=locEntryForm.getAadharNo().trim();
	           incomeCerNo=locEntryForm.getIncomeCerNo().trim();
	           newFscNo=locEntryForm.getNewFscNo().trim();
	           treatmentDetails=locEntryForm.getPurpose().trim();
	           opcrNo=locEntryForm.getOpCRNo().trim();
	           referredBy=locEntryForm.getReferredBy();
	           
	           if(CommonFunctions.validateData(aadharNo)) {
		            if (!UIDVerfication.validateVerhoeff(aadharNo)) {
			            redirectAttributes.addFlashAttribute("msg", "Aadhaar is not valid.please enter valid aadhaar");
			            return  "redirect:/mlaLoc";
		             }
	            }

	           	if(CommonFunctions.validateData(aadharNo)) {
		            if (isAadhaarEnteredToday(aadharNo, null, 1)) {
			            redirectAttributes.addFlashAttribute("msg", "Failed to insert. A record with this Aadhaar number already entered today.");
			            return  "redirect:/mlaLoc";
		            }
	            }
            
	            if ((incomeCerNo == null || incomeCerNo.isEmpty()) && (newFscNo == null || newFscNo.isEmpty())) {
	            	 redirectAttributes.addFlashAttribute("msg", " Enter Either Income Certificate Number or New FSC number (Anyone is Mandatory)");
		        	 return  "redirect:/mlaLoc";
	            }
	            
	            if(opcrNo==null || opcrNo.isEmpty()) {
		        	  redirectAttributes.addFlashAttribute("msg", "Enter OP CR Number");
		        	  return  "redirect:/mlaLoc";
		        }
	            
	            if(treatmentDetails==null || treatmentDetails.isEmpty()) {
		        	   redirectAttributes.addFlashAttribute("msg", "Please Enter Patient Details of Treatment");
		        	   return  "redirect:/mlaLoc";
		        }
	            	
	            if("999".equals(locEntryForm.getRecommendedBy())){            
	            	if(referredBy==null || referredBy.isEmpty() ) {
	            		 redirectAttributes.addFlashAttribute("msg", "Please enter referred by ");
			        	 return  "redirect:/mlaLoc";
	            	}
	            }
	            
	          /*  if(opcrNo !=null && !opcrNo.isEmpty()) {
	            	boolean isExist=inwardService.isOPCRNoExist(opcrNo);	            	
	            	if(isExist) {
	            		redirectAttributes.addFlashAttribute("msg", "This Consultation Receipt(CR) No Already Exists.");
			        	return  "redirect:/mlaLoc";
	            	}
	            } */
	            
	            result=locService.saveMlaLocCmrfEntry(locEntryForm,request);
	            if(result != null && !result.isEmpty()) {
	            	succMsg="Registration Completed. Token Number is: <span style='color:blue;'>" + locEntryForm.getLoc_mla_no() + "</span><br>Your application is successfully forwarded to NIMS for estimation.<br>please contact Nims Hospital Administartion Land Line Office Number <br>040-23489041 (Dr.Charan, Dr.Rakesh)<br>for further assistance.";
		            redirectAttributes.addFlashAttribute("succMsg", succMsg);
	            }else {
		            msg="Error While Inserting";
		            redirectAttributes.addFlashAttribute("msg", msg);
	            }
	            	        
	           locEntryForm.setPatient_name("");locEntryForm.setFather_name("");locEntryForm.setAddress("");
	           locEntryForm.setPurpose("");locEntryForm.setRecommendedBy("");
	           locEntryForm.setVipletter_date("");locEntryForm.setHospCode("");locEntryForm.setOpCRNo("");
               model.put("locEntryForm", locEntryForm); 
         
        } catch (Exception e) {
	        e.printStackTrace();
        }
       return "redirect:/mlaLoc"; 	  	
    }
	
	@RequestMapping(value = "/editLocToken",method = RequestMethod.GET)
    public String editlocView(Map<String, Object> model,HttpSession session)throws Exception {
		
		LOCMLACMRFEntryModel locEntryForm = new LOCMLACMRFEntryModel();
		
		List<LOCMLACMRFEntryModel> recommendedList = new ArrayList<LOCMLACMRFEntryModel>();  
		recommendedList = locService.getRecommendedDetails(session);
		
		List<LOCMLACMRFEntryModel> hospitalList = new ArrayList<LOCMLACMRFEntryModel>();  
		hospitalList = locService.getHospitalDetails(session);
		
		model.put("recommendedList",recommendedList);
		model.put("hospitalList",hospitalList);
        model.put("locEntryForm", locEntryForm);
         
        return "editLocTokenDetails";
    }
	
	@RequestMapping(value = "/getLocTokenData",method = RequestMethod.POST)
    public @ResponseBody String retrieveLocDetails(Map<String, Object> model,@RequestParam("loc_token")String loc_token,@ModelAttribute("locEntryForm") LOCMLACMRFEntryModel locEntryForm)throws Exception {
		
		List<LOCMLACMRFEntryModel> loc_data=new ArrayList<LOCMLACMRFEntryModel>();
		StringBuilder mainData = new StringBuilder();
	
		if(loc_token !=null && !loc_token.isEmpty() && !loc_token.equals("0")) {		
			loc_data=locService.getLocData(loc_token);
			for(LOCMLACMRFEntryModel tempDTO : loc_data) {
                mainData.append(tempDTO.getPatient_name()+":"); 
                mainData.append(tempDTO.getFather_name()+":"); 
                mainData.append(tempDTO.getAddress()+":"); 
                mainData.append(tempDTO.getPurpose()+":");
                mainData.append(tempDTO.getAssured_amt()+":");
                mainData.append(tempDTO.getRecommendedBy()+":");
                mainData.append(tempDTO.getVipletter_date()+":");
                mainData.append(tempDTO.getHospCode()+":");
                mainData.append(tempDTO.getStatus());
			}
			
		}else {
			model.put("msg", "Invalid Loc Token Number");
		}
		locEntryForm.setLoc_token(loc_token);
        model.put("locEntryForm", locEntryForm);      
        return mainData.toString();
    }
	
	@RequestMapping(value = "/editLocToken",method = RequestMethod.POST)
    public String updateLocDetails(Map<String, Object> model,@ModelAttribute("locEntryForm") LOCMLACMRFEntryModel locEntryForm,HttpSession session)throws Exception {
		
		boolean flag=false;
		flag=locService.updateLocDetails(locEntryForm);
		if(flag) {
			model.put("msg", "Loc Token Details Updated.");
		}else {
			model.put("msg", "Updation Failed.");
		}
		model.put("recommendedList",locService.getRecommendedDetails(session));
		model.put("hospitalList",locService.getHospitalDetails(session));
		locEntryForm.setPatient_name("");locEntryForm.setFather_name("");locEntryForm.setAddress("");
		locEntryForm.setPurpose("");locEntryForm.setAssured_amt("");locEntryForm.setRecommendedBy("");
		locEntryForm.setVipletter_date("");locEntryForm.setHospCode("");locEntryForm.setStatus("");  
		locEntryForm.setLoc_mla_no("");locEntryForm.setMla_cmrf_year("0");
        return "editLocTokenDetails";
    }

	public static boolean uploadFile(MultipartFile file, String folder, String fileName) {
        boolean flag = false;

        if (!file.isEmpty()) {
            try {
                byte[] bytes = file.getBytes();
             
                File dir = new File(folder);
                if (!dir.exists())
                    dir.mkdirs();

                // Create the file on server
                
                File serverFile = new File(folder + File.separator + fileName);
                
                BufferedOutputStream stream = new BufferedOutputStream(new FileOutputStream(serverFile));
                stream.write(bytes);
                stream.close();
                flag = true;
            
                System.out.println("Successfully uploaded file=" + serverFile);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            System.out.println("Failed to upload " + folder + fileName + " because the file was empty.");
        }
        return flag;
    }

    public @ResponseBody Boolean isAadhaarEnteredToday(String aadhaarNo, Integer hospCode, Integer type) throws Exception {
		// System.out.println("Type : " + type);
		// System.out.println("Aadhar : " + aadhaarNo);
		// System.out.println("hospCode : " + hospCode);
		return inwardService.isAadhaarEnteredToday(aadhaarNo, hospCode, type);
    }
}

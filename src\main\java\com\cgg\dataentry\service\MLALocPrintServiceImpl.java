package com.cgg.dataentry.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.dataentry.model.LOCMLACMRFEntryModel;
import com.cgg.dataentry.model.LocMlaCmrfPrintDTO;
import com.cgg.dataentry.model.LocMlaCmrfPrintProjection;
import com.cgg.dataentry.repositories.MLALocPrintRepository;
import com.cgg.hospital.dao.MLALocPrintCmrfDao;

@Service
public class MLALocPrintServiceImpl implements MLALocPrintService{

	@Autowired
	private  MLALocPrintCmrfDao mLALocPrintCmrfDao;
	
	@Autowired
	private MLALocPrintRepository mLALocPrintRepository;
	
	@Override
	public List<LOCMLACMRFEntryModel> getAllApprovedData(String consNo,String userId) {
	   return mLALocPrintCmrfDao.getAllApprovedData(consNo,userId);
	}
	
	@Override
	public LOCMLACMRFEntryModel getLOCCoveringLetterData(String locTokenNo) throws Exception {
		return mLALocPrintCmrfDao.getLOCCoveringLetterData(locTokenNo);
	}
	
	public LocMlaCmrfPrintDTO getEstimationData(String locTokenNo) {
	    //System.out.println("Fetching Estimation Data for locTokenNo: " + locTokenNo);

	    LocMlaCmrfPrintProjection projection = mLALocPrintRepository.getEstimationData(locTokenNo);
	    
	    // Print projection data before processing
	    if (projection == null) {
	        System.out.println("No data found for locTokenNo: " + locTokenNo);
	        return null;
	    }

	    //System.out.println("Fetched Projection: " + projection);
    

	 // Assuming projection is an object that holds the data from a database or another source
	    LocMlaCmrfPrintDTO dto = new LocMlaCmrfPrintDTO(
	    	    projection.getMlaLocNo() != null ? projection.getMlaLocNo() : null, // If null, assign null
	    	    projection.getPatientName() != null && !projection.getPatientName().isEmpty() ? projection.getPatientName() : null, // Handle empty strings
	    	    projection.getFatherSonOf() != null && !projection.getFatherSonOf().isEmpty() ? projection.getFatherSonOf() : null,
	    	    projection.getPatientIp() != null && !projection.getPatientIp().isEmpty() ? projection.getPatientIp() : null,
	    	    projection.getAge() != null && !projection.getAge().isEmpty() ? projection.getAge() : null, // Assuming age is a string or integer
	    	    projection.getGender() != null && !projection.getGender().isEmpty() ? projection.getGender() : null, // Assuming age is a string or integer
	    	    projection.getDistName() != null ? projection.getDistName() : null,  // Ensure distName is a String, handle null
	    	    projection.getSpecialName() != null ? projection.getSpecialName() : null,
	    	    projection.getDepartmentName() != null ? projection.getDepartmentName() : null,
	    	    projection.getAddress() != null ? projection.getAddress() : null,
	    	    projection.getHospName() != null ? projection.getHospName() : null,
	    	    projection.getHospCode() != null ? projection.getHospCode() : null,
	    	    projection.getMobileNo() != null ? projection.getMobileNo() : BigDecimal.ZERO, // Default to 0 if null
	    	    projection.getAadhaarNo() != null && !projection.getAadhaarNo().isEmpty() ? projection.getAadhaarNo() : null,
	    	    projection.getOpcrNo() != null && !projection.getOpcrNo().isEmpty() ? projection.getOpcrNo() : null,
	    	    projection.getPurpose() != null && !projection.getPurpose().isEmpty() ? projection.getPurpose() : null,
	    	    projection.getAssuredAmt() != null ? projection.getAssuredAmt() : 0, // Default to 0 if null
	    	    projection.getAarogyasreeCovered() != null && !projection.getAarogyasreeCovered().isEmpty() ? projection.getAarogyasreeCovered() : null,
	    	    projection.getBedCharges() != null ? projection.getBedCharges() : 0,
	    	    projection.getInvestigCharges() != null ? projection.getInvestigCharges() : 0,
	    	    projection.getDrugsDispCharges() != null ? projection.getDrugsDispCharges() : 0,
	    	    projection.getSurgProcCharges() != null ? projection.getSurgProcCharges() : 0,
	    	    projection.getImplantCharges() != null ? projection.getImplantCharges() : 0,
	    	    projection.getMiscCharges() != null ? projection.getMiscCharges() : 0
	    	);


	    dto.setCurrentDate(LocalDate.now().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
	    dto.setAmountInWords(convertNumberToWords(projection.getAssuredAmt()));
	    //System.out.println("Final DTO: " + dto);

	    return dto;
	}

		private static final String[] ONES = { "", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine",
				"Ten", "Eleven", "Twelve", "Thirteen", "Fourteen", "Fifteen", "Sixteen", "Seventeen", "Eighteen", "Nineteen" };

		private static final String[] TENS = { "", "", "Twenty", "Thirty", "Forty", "Fifty", "Sixty", "Seventy", "Eighty",
				"Ninety" };

		public String convertNumberToWords(int number) {
			if (number == 0)
				return "Zero Rupees only";

			StringBuilder words = new StringBuilder();

			if (number >= 10000000) {
				words.append(convertLessThanThousand(number / 10000000)).append(" Crore ");
				number %= 10000000;
			}

			if (number >= 100000) {
				words.append(convertLessThanThousand(number / 100000)).append(" Lakh ");
				number %= 100000;
			}

			if (number >= 1000) {
				words.append(convertLessThanThousand(number / 1000)).append(" Thousand ");
				number %= 1000;
			}

			if (number >= 100) {
				words.append(convertLessThanThousand(number / 100)).append(" Hundred ");
				number %= 100;
			}

			if (number > 0) {
				if (words.length() != 0) {
					words.append("and ");
				}
				words.append(convertLessThanThousand(number));
			}

			return words.toString().trim() + " Rupees only";
		}

		private String convertLessThanThousand(int number) {
			StringBuilder result = new StringBuilder();

			if (number >= 100) {
				result.append(ONES[number / 100]).append(" Hundred ");
				number %= 100;
			}

			if (number >= 20) {
				result.append(TENS[number / 10]).append(" ");
				number %= 10;
			}

			if (number > 0) {
				result.append(ONES[number]).append(" ");
			}

			return result.toString().trim();
		}

}

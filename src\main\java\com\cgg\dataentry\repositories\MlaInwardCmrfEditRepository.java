package com.cgg.dataentry.repositories;

import java.util.List;
import java.util.Optional;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.cgg.dataentry.entities.MlaCmrf;

@Repository
public interface MlaInwardCmrfEditRepository extends JpaRepository<MlaCmrf, String>{

	 	Optional<MlaCmrf> findByMlaCmrfNoAndStatusIn(String mlaCmrfNo,List<String> status);
	 
		@Query(value = "SELECT dist_code FROM hospital WHERE hospcode = :hospcode", nativeQuery = true)
		Integer findDistrictByHospcode(@Param("hospcode") Integer hospcode);
		
		@Query(value = "select branch from ifsccodes where state ilike 'Telangana' and ifsccode = :ifsccode",nativeQuery = true)
		String getBranchByIfsc(@Param("ifsccode") String ifsccode);
	  
	  	@Modifying
		@Transactional
		@Query(value = "INSERT INTO public.mla_cmrf_log(\r\n"
				+ "	mla_cmrf_no, patient_name, time_stamp, user_id, recommended_by, status, aadhar_no, father_son_of, cmrf_no, cmrf_priority, entered_on, delete_flag, updated_on, hosp_code, patient_ip_status, rej_reasons, updated_by, verified_by_deo, deo_verified_date, deo_rej_reasons, patient_ip, admission_no, hos_verified_date, patient_ip_upd_date, pat_address, pat_district, pat_mandal, mobile_no, age, purpose, pincode, logged_timestamp, logged_ipaddress, logged_by, patient_ip_updated_by, logged_remarks, ip_address, old_fsc_no, new_fsc_no, gender, pending_reasons, aadhar_copy, bank_acc_no, bank_branch, bank_dist, bank_ifsc, bank_name, bank_passbook, bank_pass_copy, cheque_dt, cmrf_ent_by, cmrf_ent_dt, cmrf_ent_ipaddr, deo_upd_by, deo_upd_dt, deo_upd_ipaddr, deo_upd_reasons, deo_upd_remarks, deo_verified_by, fsc_copy, fsc_ration_card, hard_copy_received, hosp_bills_copy, hospital_bills, hosp_ver_by, hosp_ver_ipaddr, inc_cer_copy, income_certificate, income_no, mla_letter_copy, pat_village, token_hash, upload_path, deo_upd_others_remarks, doc_verif_amt, hosp_bill_amt, hosp_pend_res, treat_par_id, treat_sub_id, treat_proc_id, deo_pen_reasons, status_upd_res,bank_acc_holder_name, is_qr_scanned, qr_scanned_by, qr_scanned_timestamp, is_special_flg, special_flg_ref_by, special_flg_updated_by, special_flg_updated_on, batch_name, batch_serial_no, batch_updated_by, batch_updated_on, bill_amt_upd_by ) "
				+ "SELECT mla_cmrf_no, patient_name, time_stamp, user_id, recommended_by, status, aadhar_no, father_son_of, cmrf_no, cmrf_priority, entered_on, delete_flag, updated_on, hosp_code, patient_ip_status, rej_reasons, updated_by, verified_by_deo, deo_verified_date, deo_rej_reasons,patient_ip, admission_no, hos_verified_date, patient_ip_upd_date, pat_address, pat_district, pat_mandal, mobile_no, age, purpose, pincode,now(),:logged_ipaddress,:logged_by, patient_ip_updated_by, :log_type,ip_address, old_fsc_no, new_fsc_no, gender, pending_reasons, aadhar_copy, bank_acc_no, bank_branch, bank_dist, bank_ifsc, bank_name, bank_passbook, bank_pass_copy, cheque_dt, cmrf_ent_by, cmrf_ent_dt, cmrf_ent_ipaddr, deo_upd_by, deo_upd_dt, deo_upd_ipaddr, deo_upd_reasons, deo_upd_remarks, deo_verified_by, fsc_copy, fsc_ration_card, hard_copy_received, hosp_bills_copy, hospital_bills, hosp_ver_by, hosp_ver_ipaddr, inc_cer_copy, income_certificate, income_no, mla_letter_copy, pat_village, token_hash, upload_path, deo_upd_others_remarks, doc_verif_amt, hosp_bill_amt, hosp_pend_res, treat_par_id, treat_sub_id, treat_proc_id, deo_pen_reasons, status_upd_res,bank_acc_holder_name, is_qr_scanned, qr_scanned_by, qr_scanned_timestamp, is_special_flg, special_flg_ref_by, special_flg_updated_by, special_flg_updated_on, batch_name, batch_serial_no, batch_updated_by, batch_updated_on, bill_amt_upd_by "
				+" from mla_cmrf where mla_cmrf_no= :mla_cmrf_no ;", nativeQuery = true)
		Integer insertMlaCmrfLog(
				@Param("mla_cmrf_no") String mla_cmrf_no,
	 			@Param("log_type") String log_type,
	 			@Param("logged_by") String logged_by,
	 			@Param("logged_ipaddress") String logged_ipaddress
				) ;
	  
		@Query(value ="select count(1) from cmrelief where inward_id=:mlaCmrfTokenNo ",nativeQuery = true)
		Integer mlaCMRFTokenCount(@Param("mlaCmrfTokenNo") String mlaCmrfTokenNo);
		
		@Query(value = "select  cmrf_no from cmrelief where inward_id=:mlaCmrfTokenNo ", nativeQuery = true)
		String mlaCMRFTokenNo(@Param("mlaCmrfTokenNo") String mlaCmrfTokenNo);

	  	@Modifying
		@Transactional
		@Query(value = "INSERT INTO public.cmrelief_log("
				+ "	cmrf_dt,pat_name,father_son_of,pat_address,pat_mandal,pat_district,pat_caste ,white_cardno ,hosp_code ,rec ,endorsement_old ,req_amt ,sanc_date ,payment_to ,ex_gratia ,rejected ,cmp_date ,revenue_no ,revenue_date ,prevsanc ,prevdate ,year001 ,prevapplnos ,det_diseases ,doc_name ,doc_design ,type_est ,letter_date ,proceedings ,cheque_no ,cheque_dt ,ref_tocm ,cmp_no ,endorsement ,sms_status ,email_status ,cmrf_no ,is_canceled ,canceled_amount ,proceedings_date ,is_verified ,is_reimbursement ,age ,prevcmpno ,letter_no ,economy ,userid ,enteredon ,purpose ,diseasetype ,percentage ,cmapproved ,cmapprovedtime ,colpurpose ,psapproved1 ,psapproved2 ,loagenerated ,chequeissued ,recommended_by ,rec_from ,govt ,sanc_amt ,sined ,priority ,arogya_sri ,remote_addr ,time_stamp ,inward_id ,logged_timestamp ,logged_ipaddress ,logged_by ,logged_remarks ,old_fsc_no ,new_fsc_no ,cmrf_type ,is_chq_reprinted ,bank_account_no, bank_acc_hol_name, old_cmrf_no ) "
				+ "SELECT cmrf_dt,pat_name,father_son_of,pat_address,pat_mandal,pat_district,pat_caste ,white_cardno ,hosp_code ,rec ,endorsement_old ,req_amt ,sanc_date ,payment_to ,ex_gratia ,rejected ,cmp_date ,revenue_no ,revenue_date ,prevsanc ,prevdate ,year001 ,prevapplnos ,det_diseases ,doc_name ,doc_design ,type_est ,letter_date ,proceedings ,cheque_no ,cheque_dt ,ref_tocm ,cmp_no ,endorsement ,sms_status ,email_status ,cmrf_no ,is_canceled ,canceled_amount ,proceedings_date ,is_verified ,is_reimbursement ,age ,prevcmpno ,letter_no ,economy ,userid ,enteredon ,purpose ,diseasetype ,percentage ,cmapproved ,cmapprovedtime ,colpurpose ,psapproved1 ,psapproved2 ,loagenerated ,chequeissued ,recommended_by ,rec_from ,govt ,sanc_amt ,sined ,priority ,arogya_sri ,:logged_ipaddress ,now() ,inward_id ,now() ,:logged_ipaddress ,:logged_by ,:logged_remarks ,old_fsc_no ,new_fsc_no ,cmrf_type ,is_chq_reprinted ,bank_account_no, bank_acc_hol_name, old_cmrf_no "
				+" from cmrelief where cmrf_no= :cmrf_no ;", nativeQuery = true)
		Integer insertCmreliefLog(
				@Param("cmrf_no") String cmrf_no,
	 			@Param("logged_remarks") String logged_remarks,
	 			@Param("logged_by") String logged_by,
	 			@Param("logged_ipaddress") String logged_ipaddress
				) ;

	  	@Modifying
		@Transactional
		@Query(value = "INSERT INTO public.cmrelief_log("
				+ "	cmrf_dt,pat_name,father_son_of,pat_address,pat_mandal,pat_district,pat_caste ,white_cardno ,hosp_code ,rec ,endorsement_old ,req_amt ,sanc_date ,payment_to ,ex_gratia ,rejected ,cmp_date ,revenue_no ,revenue_date ,prevsanc ,prevdate ,year001 ,prevapplnos ,det_diseases ,doc_name ,doc_design ,type_est ,letter_date ,proceedings ,cheque_no ,cheque_dt ,ref_tocm ,cmp_no ,endorsement ,sms_status ,email_status ,cmrf_no ,is_canceled ,canceled_amount ,proceedings_date ,is_verified ,is_reimbursement ,age ,prevcmpno ,letter_no ,economy ,userid ,enteredon ,purpose ,diseasetype ,percentage ,cmapproved ,cmapprovedtime ,colpurpose ,psapproved1 ,psapproved2 ,loagenerated ,chequeissued ,recommended_by ,rec_from ,govt ,sanc_amt ,sined ,priority ,arogya_sri ,remote_addr ,time_stamp ,inward_id ,logged_timestamp ,logged_ipaddress ,logged_by ,logged_remarks ,old_fsc_no ,new_fsc_no ,cmrf_type ,is_chq_reprinted ,bank_account_no, bank_acc_hol_name, old_cmrf_no ) "
				+ "SELECT cmrf_dt,pat_name,father_son_of,pat_address,pat_mandal,pat_district,pat_caste ,white_cardno ,hosp_code ,rec ,endorsement_old ,req_amt ,sanc_date ,payment_to ,ex_gratia ,rejected ,cmp_date ,revenue_no ,revenue_date ,prevsanc ,prevdate ,year001 ,prevapplnos ,det_diseases ,doc_name ,doc_design ,type_est ,letter_date ,proceedings ,cheque_no ,cheque_dt ,ref_tocm ,cmp_no ,endorsement ,sms_status ,email_status ,cmrf_no ,is_canceled ,canceled_amount ,proceedings_date ,is_verified ,is_reimbursement ,age ,prevcmpno ,letter_no ,economy ,userid ,enteredon ,purpose ,diseasetype ,percentage ,cmapproved ,cmapprovedtime ,colpurpose ,psapproved1 ,psapproved2 ,loagenerated ,chequeissued ,recommended_by ,rec_from ,govt ,sanc_amt ,sined ,priority ,arogya_sri ,:logged_ipaddress ,now() ,inward_id ,now() ,:logged_ipaddress ,:logged_by ,:logged_remarks ,old_fsc_no ,new_fsc_no ,cmrf_type ,is_chq_reprinted ,bank_account_no, bank_acc_hol_name, old_cmrf_no "
				+" from cmrelief where cmrf_no in (:cmrf_no);", nativeQuery = true)
		Integer insertMultiCmreliefLog(
				@Param("cmrf_no") List<String> cmrf_no,
	 			@Param("logged_remarks") String logged_remarks,
	 			@Param("logged_by") String logged_by,
	 			@Param("logged_ipaddress") String logged_ipaddress
				) ;
}

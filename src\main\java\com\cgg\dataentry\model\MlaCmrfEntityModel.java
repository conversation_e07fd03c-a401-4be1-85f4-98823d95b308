package com.cgg.dataentry.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

public class MlaCmrfEntityModel {

	private String mlaCmrfNo;

	private Integer docVerifAmt;

	private String incomeNo;

	private String patientName;

	private Date timeStamp;

	private String userId;

	private String recommendedBy;

	private String status;

	private String aadharNo;

	private String fatherSonOf;

	private String cmrfNo;

	private String cmrfPriority;

	private LocalDateTime enteredOn;

	private boolean deleteFlag;

	private LocalDateTime updatedOn;

	private String patientIp;

	private Integer hospCode;

	private String patientIpStatus;

	private String rejReasons;

	private String updatedBy;

	private String verifiedByDeo;

	private LocalDateTime deoVerifiedDate;

	private String deoRejReasons;

	private String admissionNo;

	private LocalDateTime hosVerifiedDate;

	private LocalDateTime patientIpUpdDate;

	private String patAddress;

	private Integer patDistrict;

	private Integer patMandal;

	private Long mobileNo;

	private String age;

	private String purpose;

	private Integer pincode;

	private String ipAddress;

	private String patientIpUpdatedBy;

	private String deoVerifiedBy;

	private String oldFscNo;

	private String newFscNo;

	private String gender;

	private String uploadPath;

	private Boolean hardCopyReceived;

	private String bankDist;

	private String bankName;

	private String bankAccNo;

	private String bankIfsc;

	private String bankBranch;

	private String bankAccHolderName;

	private Boolean hospBillsCopy;

	private Boolean incCerCopy;

	private Boolean fscCopy;

	private Boolean mlaLetterCopy;

	private Boolean bankPassCopy;

	private Boolean aadharCopy;

	private String deoUpdBy;

	private LocalDateTime deoUpdDt;

	private String deoUpdIpaddr;

	private String remarks;

	private Integer patVillage;

	private String reason;

	private String hospPendRes;

	private String pendingReasons;

	private String hospVerBy;

	private String hospVerIpaddr;

	private String cmrfEntBy;

	private LocalDateTime cmrfEntDt;

	private String cmrfEntIpaddr;

	private LocalDateTime chequeDt;

	private String tokenHash;

	private Boolean bankPassbook;

	private Boolean fscRationCard;

	private Boolean hospitalBills;

	private Boolean incomeCertificate;

	private String deoUpdOthersRemarks;

	private BigDecimal hospBillAmt;

	private Integer treatParId;

	private Integer treatSubId;

	private Integer treatProcId;

	private String statusUpdRes;

	private Boolean isQrScanned;

	private String qrScannedBy;

	private LocalDateTime qrScannedTimestamp;

	private String deoPenReasons;

	private boolean isSpecialFlag;

	private LocalDateTime specialFlgUpdatedOn;

	private String specialFlgUpdatedBy;

	private String specialFlgReferredBy;

	private String hospname;

	private String cname;

	private String mlamp;
	
	private String batchName;
	
	private String batchSerialNo;
	
	private String batchUpdatedBy;
	
	private LocalDateTime batchUpdatedOn;
	
	public MlaCmrfEntityModel() {
		super();
	}

	public MlaCmrfEntityModel(String mlaCmrfNo, String cmrfNo, String patientName, String fatherSonOf,
			String patAddress, String purpose, String hospname, String cname, String mlamp, boolean isSpecialFlag) {
		this.mlaCmrfNo = mlaCmrfNo;
		this.cmrfNo = cmrfNo;
		this.patientName = patientName;
		this.fatherSonOf = fatherSonOf;
		this.patAddress = patAddress;
		this.purpose = purpose;
		this.hospname = hospname;
		this.cname = cname;
		this.mlamp = mlamp;
		this.isSpecialFlag = isSpecialFlag;
		
	}
	
	public MlaCmrfEntityModel(String mlaCmrfNo, String cmrfNo, String patientName, String fatherSonOf,
			String patAddress, String purpose, String hospname, String cname, String mlamp, boolean isSpecialFlag,String status, String batchSerialNo) {
		this.mlaCmrfNo = mlaCmrfNo;
		this.cmrfNo = cmrfNo;
		this.patientName = patientName;
		this.fatherSonOf = fatherSonOf;
		this.patAddress = patAddress;
		this.purpose = purpose;
		this.hospname = hospname;
		this.cname = cname;
		this.mlamp = mlamp;
		this.isSpecialFlag = isSpecialFlag;
		this.status=status;
		this.batchSerialNo=batchSerialNo;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getMlaCmrfNo() {
		return mlaCmrfNo;
	}

	public void setMlaCmrfNo(String mlaCmrfNo) {
		this.mlaCmrfNo = mlaCmrfNo;
	}

	public String getIncomeNo() {
		return incomeNo;
	}

	public void setIncomeNo(String incomeNo) {
		this.incomeNo = incomeNo;
	}

	public String getPatientName() {
		return patientName;
	}

	public void setPatientName(String patientName) {
		this.patientName = patientName;
	}

	public Date getTimeStamp() {
		return timeStamp;
	}

	public void setTimeStamp(Date timeStamp) {
		this.timeStamp = timeStamp;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getRecommendedBy() {
		return recommendedBy;
	}

	public void setRecommendedBy(String recommendedBy) {
		this.recommendedBy = recommendedBy;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getAadharNo() {
		return aadharNo;
	}

	public void setAadharNo(String aadharNo) {
		this.aadharNo = aadharNo;
	}

	public String getFatherSonOf() {
		return fatherSonOf;
	}

	public void setFatherSonOf(String fatherSonOf) {
		this.fatherSonOf = fatherSonOf;
	}

	public String getCmrfNo() {
		return cmrfNo;
	}

	public void setCmrfNo(String cmrfNo) {
		this.cmrfNo = cmrfNo;
	}

	public String getCmrfPriority() {
		return cmrfPriority;
	}

	public void setCmrfPriority(String cmrfPriority) {
		this.cmrfPriority = cmrfPriority;
	}

	public LocalDateTime getEnteredOn() {
		return enteredOn;
	}

	public void setEnteredOn(LocalDateTime enteredOn) {
		this.enteredOn = enteredOn;
	}

	public boolean isDeleteFlag() {
		return deleteFlag;
	}

	public void setDeleteFlag(boolean deleteFlag) {
		this.deleteFlag = deleteFlag;
	}

	public LocalDateTime getUpdatedOn() {
		return updatedOn;
	}

	public void setUpdatedOn(LocalDateTime updatedOn) {
		this.updatedOn = updatedOn;
	}

	public String getPatientIp() {
		return patientIp;
	}

	public void setPatientIp(String patientIp) {
		this.patientIp = patientIp;
	}

	public Integer getHospCode() {
		return hospCode;
	}

	public void setHospCode(Integer hospCode) {
		this.hospCode = hospCode;
	}

	public String getPatientIpStatus() {
		return patientIpStatus;
	}

	public void setPatientIpStatus(String patientIpStatus) {
		this.patientIpStatus = patientIpStatus;
	}

	public String getRejReasons() {
		return rejReasons;
	}

	public void setRejReasons(String rejReasons) {
		this.rejReasons = rejReasons;
	}

	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	public String getVerifiedByDeo() {
		return verifiedByDeo;
	}

	public void setVerifiedByDeo(String verifiedByDeo) {
		this.verifiedByDeo = verifiedByDeo;
	}

	public LocalDateTime getDeoVerifiedDate() {
		return deoVerifiedDate;
	}

	public void setDeoVerifiedDate(LocalDateTime deoVerifiedDate) {
		this.deoVerifiedDate = deoVerifiedDate;
	}

	public String getDeoRejReasons() {
		return deoRejReasons;
	}

	public void setDeoRejReasons(String deoRejReasons) {
		this.deoRejReasons = deoRejReasons;
	}

	public String getAdmissionNo() {
		return admissionNo;
	}

	public void setAdmissionNo(String admissionNo) {
		this.admissionNo = admissionNo;
	}

	public LocalDateTime getHosVerifiedDate() {
		return hosVerifiedDate;
	}

	public void setHosVerifiedDate(LocalDateTime hosVerifiedDate) {
		this.hosVerifiedDate = hosVerifiedDate;
	}

	public LocalDateTime getPatientIpUpdDate() {
		return patientIpUpdDate;
	}

	public void setPatientIpUpdDate(LocalDateTime patientIpUpdDate) {
		this.patientIpUpdDate = patientIpUpdDate;
	}

	public String getPatAddress() {
		return patAddress;
	}

	public void setPatAddress(String patAddress) {
		this.patAddress = patAddress;
	}

	public Integer getPatDistrict() {
		return patDistrict;
	}

	public void setPatDistrict(Integer patDistrict) {
		this.patDistrict = patDistrict;
	}

	public Integer getPatMandal() {
		return patMandal;
	}

	public void setPatMandal(Integer patMandal) {
		this.patMandal = patMandal;
	}

	public Long getMobileNo() {
		return mobileNo;
	}

	public void setMobileNo(Long mobileNo) {
		this.mobileNo = mobileNo;
	}

	public String getAge() {
		return age;
	}

	public void setAge(String age) {
		this.age = age;
	}

	public String getPurpose() {
		return purpose;
	}

	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}

	public Integer getPincode() {
		return pincode;
	}

	public void setPincode(Integer pincode) {
		this.pincode = pincode;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	public String getPatientIpUpdatedBy() {
		return patientIpUpdatedBy;
	}

	public void setPatientIpUpdatedBy(String patientIpUpdatedBy) {
		this.patientIpUpdatedBy = patientIpUpdatedBy;
	}

	public String getDeoVerifiedBy() {
		return deoVerifiedBy;
	}

	public void setDeoVerifiedBy(String deoVerifiedBy) {
		this.deoVerifiedBy = deoVerifiedBy;
	}

	public String getOldFscNo() {
		return oldFscNo;
	}

	public void setOldFscNo(String oldFscNo) {
		this.oldFscNo = oldFscNo;
	}

	public String getNewFscNo() {
		return newFscNo;
	}

	public void setNewFscNo(String newFscNo) {
		this.newFscNo = newFscNo;
	}

	public String getGender() {
		return gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getUploadPath() {
		return uploadPath;
	}

	public void setUploadPath(String uploadPath) {
		this.uploadPath = uploadPath;
	}

	public Boolean getHardCopyReceived() {
		return hardCopyReceived;
	}

	public void setHardCopyReceived(Boolean hardCopyReceived) {
		this.hardCopyReceived = hardCopyReceived;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBankAccNo() {
		return bankAccNo;
	}

	public void setBankAccNo(String bankAccNo) {
		this.bankAccNo = bankAccNo;
	}

	public String getBankIfsc() {
		return bankIfsc;
	}

	public void setBankIfsc(String bankIfsc) {
		this.bankIfsc = bankIfsc;
	}

	public String getBankBranch() {
		return bankBranch;
	}

	public void setBankBranch(String bankBranch) {
		this.bankBranch = bankBranch;
	}

	public Boolean getHospBillsCopy() {
		return hospBillsCopy;
	}

	public void setHospBillsCopy(Boolean hospBillsCopy) {
		this.hospBillsCopy = hospBillsCopy;
	}

	public Boolean getIncCerCopy() {
		return incCerCopy;
	}

	public void setIncCerCopy(Boolean incCerCopy) {
		this.incCerCopy = incCerCopy;
	}

	public Boolean getFscCopy() {
		return fscCopy;
	}

	public void setFscCopy(Boolean fscCopy) {
		this.fscCopy = fscCopy;
	}

	public Boolean getMlaLetterCopy() {
		return mlaLetterCopy;
	}

	public void setMlaLetterCopy(Boolean mlaLetterCopy) {
		this.mlaLetterCopy = mlaLetterCopy;
	}

	public Boolean getBankPassCopy() {
		return bankPassCopy;
	}

	public void setBankPassCopy(Boolean bankPassCopy) {
		this.bankPassCopy = bankPassCopy;
	}

	public Boolean getAadharCopy() {
		return aadharCopy;
	}

	public void setAadharCopy(Boolean aadharCopy) {
		this.aadharCopy = aadharCopy;
	}

	public String getDeoUpdBy() {
		return deoUpdBy;
	}

	public void setDeoUpdBy(String deoUpdBy) {
		this.deoUpdBy = deoUpdBy;
	}

	public LocalDateTime getDeoUpdDt() {
		return deoUpdDt;
	}

	public void setDeoUpdDt(LocalDateTime deoUpdDt) {
		this.deoUpdDt = deoUpdDt;
	}

	public String getDeoUpdIpaddr() {
		return deoUpdIpaddr;
	}

	public void setDeoUpdIpaddr(String deoUpdIpaddr) {
		this.deoUpdIpaddr = deoUpdIpaddr;
	}

	public String getBankDist() {
		return bankDist;
	}

	public void setBankDist(String bankDist) {
		this.bankDist = bankDist;
	}

	public String getHospPendRes() {
		return hospPendRes;
	}

	public void setHospPendRes(String hospPendRes) {
		this.hospPendRes = hospPendRes;
	}

	public String getDeoPenReasons() {
		return deoPenReasons;
	}

	public void setDeoPenReasons(String deoPenReasons) {
		this.deoPenReasons = deoPenReasons;
	}

	public Integer getDocVerifAmt() {
		return docVerifAmt;
	}

	public void setDocVerifAmt(Integer docVerifAmt) {
		this.docVerifAmt = docVerifAmt;
	}

	public String getBankAccHolderName() {
		return bankAccHolderName;
	}

	public void setBankAccHolderName(String bankAccHolderName) {
		this.bankAccHolderName = bankAccHolderName;
	}

	public Integer getPatVillage() {
		return patVillage;
	}

	public void setPatVillage(Integer patVillage) {
		this.patVillage = patVillage;
	}

	public String getPendingReasons() {
		return pendingReasons;
	}

	public void setPendingReasons(String pendingReasons) {
		this.pendingReasons = pendingReasons;
	}

	public String getHospVerBy() {
		return hospVerBy;
	}

	public void setHospVerBy(String hospVerBy) {
		this.hospVerBy = hospVerBy;
	}

	public String getHospVerIpaddr() {
		return hospVerIpaddr;
	}

	public void setHospVerIpaddr(String hospVerIpaddr) {
		this.hospVerIpaddr = hospVerIpaddr;
	}

	public String getCmrfEntBy() {
		return cmrfEntBy;
	}

	public void setCmrfEntBy(String cmrfEntBy) {
		this.cmrfEntBy = cmrfEntBy;
	}

	public LocalDateTime getCmrfEntDt() {
		return cmrfEntDt;
	}

	public void setCmrfEntDt(LocalDateTime cmrfEntDt) {
		this.cmrfEntDt = cmrfEntDt;
	}

	public String getCmrfEntIpaddr() {
		return cmrfEntIpaddr;
	}

	public void setCmrfEntIpaddr(String cmrfEntIpaddr) {
		this.cmrfEntIpaddr = cmrfEntIpaddr;
	}

	public LocalDateTime getChequeDt() {
		return chequeDt;
	}

	public void setChequeDt(LocalDateTime chequeDt) {
		this.chequeDt = chequeDt;
	}

	public String getTokenHash() {
		return tokenHash;
	}

	public void setTokenHash(String tokenHash) {
		this.tokenHash = tokenHash;
	}

	public Boolean getBankPassbook() {
		return bankPassbook;
	}

	public void setBankPassbook(Boolean bankPassbook) {
		this.bankPassbook = bankPassbook;
	}

	public Boolean getFscRationCard() {
		return fscRationCard;
	}

	public void setFscRationCard(Boolean fscRationCard) {
		this.fscRationCard = fscRationCard;
	}

	public Boolean getHospitalBills() {
		return hospitalBills;
	}

	public void setHospitalBills(Boolean hospitalBills) {
		this.hospitalBills = hospitalBills;
	}

	public Boolean getIncomeCertificate() {
		return incomeCertificate;
	}

	public void setIncomeCertificate(Boolean incomeCertificate) {
		this.incomeCertificate = incomeCertificate;
	}

	public String getDeoUpdOthersRemarks() {
		return deoUpdOthersRemarks;
	}

	public void setDeoUpdOthersRemarks(String deoUpdOthersRemarks) {
		this.deoUpdOthersRemarks = deoUpdOthersRemarks;
	}

	public BigDecimal getHospBillAmt() {
		return hospBillAmt;
	}

	public void setHospBillAmt(BigDecimal hospBillAmt) {
		this.hospBillAmt = hospBillAmt;
	}

	public Integer getTreatParId() {
		return treatParId;
	}

	public void setTreatParId(Integer treatParId) {
		this.treatParId = treatParId;
	}

	public Integer getTreatSubId() {
		return treatSubId;
	}

	public void setTreatSubId(Integer treatSubId) {
		this.treatSubId = treatSubId;
	}

	public Integer getTreatProcId() {
		return treatProcId;
	}

	public void setTreatProcId(Integer treatProcId) {
		this.treatProcId = treatProcId;
	}

	public String getStatusUpdRes() {
		return statusUpdRes;
	}

	public void setStatusUpdRes(String statusUpdRes) {
		this.statusUpdRes = statusUpdRes;
	}

	public Boolean getIsQrScanned() {
		return isQrScanned;
	}

	public void setIsQrScanned(Boolean isQrScanned) {
		this.isQrScanned = isQrScanned;
	}

	public String getQrScannedBy() {
		return qrScannedBy;
	}

	public void setQrScannedBy(String qrScannedBy) {
		this.qrScannedBy = qrScannedBy;
	}

	public LocalDateTime getQrScannedTimestamp() {
		return qrScannedTimestamp;
	}

	public void setQrScannedTimestamp(LocalDateTime qrScannedTimestamp) {
		this.qrScannedTimestamp = qrScannedTimestamp;
	}

	public boolean isSpecialFlag() {
		return isSpecialFlag;
	}

	public void setSpecialFlag(boolean isSpecialFlag) {
		this.isSpecialFlag = isSpecialFlag;
	}

	public LocalDateTime getSpecialFlgUpdatedOn() {
		return specialFlgUpdatedOn;
	}

	public void setSpecialFlgUpdatedOn(LocalDateTime specialFlgUpdatedOn) {
		this.specialFlgUpdatedOn = specialFlgUpdatedOn;
	}

	public String getSpecialFlgUpdatedBy() {
		return specialFlgUpdatedBy;
	}

	public void setSpecialFlgUpdatedBy(String specialFlgUpdatedBy) {
		this.specialFlgUpdatedBy = specialFlgUpdatedBy;
	}

	public String getSpecialFlgReferredBy() {
		return specialFlgReferredBy;
	}

	public void setSpecialFlgReferredBy(String specialFlgReferredBy) {
		this.specialFlgReferredBy = specialFlgReferredBy;
	}

	public String getHospname() {
		return hospname;
	}

	public void setHospname(String hospname) {
		this.hospname = hospname;
	}

	public String getCname() {
		return cname;
	}

	public void setCname(String cname) {
		this.cname = cname;
	}

	public String getMlamp() {
		return mlamp;
	}

	public void setMlamp(String mlamp) {
		this.mlamp = mlamp;
	}

	public String getBatchName() {
		return batchName;
	}

	public void setBatchName(String batchName) {
		this.batchName = batchName;
	}

	public String getBatchSerialNo() {
		return batchSerialNo;
	}

	public void setBatchSerialNo(String batchSerialNo) {
		this.batchSerialNo = batchSerialNo;
	}

	public String getBatchUpdatedBy() {
		return batchUpdatedBy;
	}

	public void setBatchUpdatedBy(String batchUpdatedBy) {
		this.batchUpdatedBy = batchUpdatedBy;
	}

	public LocalDateTime getBatchUpdatedOn() {
		return batchUpdatedOn;
	}

	public void setBatchUpdatedOn(LocalDateTime batchUpdatedOn) {
		this.batchUpdatedOn = batchUpdatedOn;
	}

}

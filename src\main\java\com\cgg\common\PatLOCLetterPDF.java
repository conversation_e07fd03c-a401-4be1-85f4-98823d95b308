package com.cgg.common;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.cgg.dataentry.model.LocDetailsEntryForm;
import com.google.zxing.WriterException;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.font.constants.StandardFonts;
import com.itextpdf.io.image.ImageData;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.font.PdfFontFactory.EmbeddingStrategy;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Div;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.properties.VerticalAlignment;
import com.itextpdf.licensing.base.LicenseKey;

public class PatLOCLetterPDF {
	
	public Map<String,String> patientLetterPdf(HttpServletRequest request) throws IOException{
		HttpSession sess = request.getSession();
        ServletContext context = sess.getServletContext();       
        String context_path=context.getRealPath("/");;
        System.out.println("context path is -------> "+context_path);
		 String mainPath = ApplicationConstants.WHATSAPP_LOC_PATH + ApplicationConstants.AC_YEAR;
		
		 LocDetailsEntryForm formData = (LocDetailsEntryForm) request.getAttribute("formData");
	        if (formData == null) {
	            System.out.println("Form data not found in the request.");
	            return null;
	        }
	        String locNumber = formData.getLocNo().split("/")[0]; 
	        String fileName = locNumber + "_LOC" + ".pdf";
	        
	        String filePath = mainPath + "/" + fileName;
	        System.out.println("PDF will be saved to: " + filePath);
	        CreateFileDirectory fileDirectory = new CreateFileDirectory();
	        try {
				boolean fileCreated = fileDirectory.createDirectoriesAndEmptyFile(
				    ApplicationConstants.BASE_PATH + mainPath, fileName );
				 System.out.println(fileCreated+"fileCreated");
			} catch (Exception e) {
				e.printStackTrace();
			}
	       // File licensePath1 = new File("iTextlicense/10f21439604cf245e3bf45eb0ab48823f9a34602bd86754457c0d380ff96302c.json");
	       // File licensePath2 = new File("iTextlicense/33c2febc8d2ef5d69c9557ea9034233f3f32257e0022f3d95b662019573fd806.json");
	        ServletContext scontext = request.getServletContext();
	        String realPath = scontext.getRealPath("/iTextlicense/10f21439604cf245e3bf45eb0ab48823f9a34602bd86754457c0d380ff96302c.json");

	        // Create File objects using the absolute path
	        File licensePath1 = new File(realPath);
	        realPath = scontext.getRealPath("/iTextlicense/33c2febc8d2ef5d69c9557ea9034233f3f32257e0022f3d95b662019573fd806.json");
	        File licensePath2 = new File(realPath);

	        
	        LicenseKey.loadLicenseFile(licensePath1);
	        LicenseKey.loadLicenseFile(licensePath2);
	        
			PdfFont font = PdfFontFactory.createFont(context_path+"fonts/gautami.ttf", PdfEncodings.IDENTITY_H,EmbeddingStrategy.PREFER_EMBEDDED, true);

	        try {
	            PdfWriter writer = new PdfWriter( ApplicationConstants.BASE_PATH +filePath);  
	            PdfDocument pdf = new PdfDocument(writer);
	            pdf.setTagged();  
	            pdf.getCatalog().setLang(new com.itextpdf.kernel.pdf.PdfString("te-IN"));  

	            Document document = new Document(pdf);
	            document.setMargins(50, 70, 50, 70);

	            float[] columnWidths = {1f, 4f, 1f};  
	            Table headTable = new Table(columnWidths).setWidth(UnitValue.createPercentValue(100)); 

	            try {
	                ImageData leftImageData = ImageDataFactory.create(context_path + "/images/tsemblem.gif");
	                Image leftImage = new Image(leftImageData).scaleToFit(80, 70).setRelativePosition(0,5,0,0); 
	                Cell leftImageCell = new Cell()
	                        .add(leftImage)
	                        .setBorder(Border.NO_BORDER)  
	                        .setHorizontalAlignment(HorizontalAlignment.LEFT);  
	                headTable.addCell(leftImageCell);
	            } catch (IOException e) {
	                e.printStackTrace();
	            }

	            try {
	                ImageData centerImageData = ImageDataFactory.create(context_path + "/images/CmImageNOH.jpg");
	                Image centerImage = new Image(centerImageData).scaleToFit(80, 80).setHorizontalAlignment(HorizontalAlignment.CENTER);  

	                Cell centerCell = new Cell()
	                        .add(centerImage)
	                        .add(new Paragraph("ఎ. రేవంత్ రెడ్డి")
	                                .setFont(font)
	                                .setFontSize(12)
	                                .setTextAlignment(TextAlignment.CENTER)
	                                .setMargin(0)
	                                .setPadding(0)
	                                .setMultipliedLeading(0.8f)
	                                .setFontColor(new DeviceRgb(236, 125, 20))) 
	                        .add(new Paragraph("ముఖ్యమంత్రి")
	                                .setFont(font)
	                                .setFontSize(12)
	                                .setTextAlignment(TextAlignment.CENTER)
	                                .setMargin(0)
	                                .setPadding(0)
	                                .setMultipliedLeading(0.5f)
	                                .setFontColor(new DeviceRgb(236, 125, 20)))
	                        .setBorder(Border.NO_BORDER)  
	                        .setHorizontalAlignment(HorizontalAlignment.CENTER);  
	                headTable.addCell(centerCell);
	            } catch (IOException e) {
	                e.printStackTrace();
	            }

	            try {
	                ImageData rightImageData = ImageDataFactory.create(context_path + "/images/rising-whiteBg.jpg");
	                Image rightImage = new Image(rightImageData).scaleToFit(80, 80).setHorizontalAlignment(HorizontalAlignment.RIGHT);  
	                Cell rightImageCell = new Cell()
	                        .add(rightImage)
	                        .setBorder(Border.NO_BORDER)  
	                        .setHorizontalAlignment(HorizontalAlignment.RIGHT);  
	                headTable.addCell(rightImageCell);
	            } catch (IOException e) {
	                e.printStackTrace();
	            }

	            document.add(headTable);
	            Text boldText = new Text(formData.getPatientName() + " గారికి.").setBold();

	            document.add(new Paragraph()
	                    .add(boldText)  
	                    .setFont(font)  
	                    .setFontSize(12)
	                    .setTextAlignment(TextAlignment.LEFT)
	                    .setMarginBottom(10)
	                    .setFontColor(new DeviceRgb(0, 100, 0)));  


	            document.add(new Paragraph(
	                    "\u00A0\u00A0\u00A0\u00A0\u00A0మీ దరఖాస్తును పరిశీలించి, ముఖ్యమంత్రి సహాయనిధి నుండి రూ " + formData.getFormatAmt() + "/-\n" +
	                    "(" + formData.getAmtInWords() + ") లు వైద్య ఖర్చు నిమిత్తము " + formData.getHospName() +
	                    " ఆసుపత్రి కి లెటర్ ఆఫ్ క్రెడిట్ సంఖ్య " + formData.getLocNo() + ", తేదీ: " + formData.getLetterDate() + " ద్వారా మంజూరు చేయడం జరిగింది.")
	                    .setFont(font)
	                    .setFontSize(12)
	                    .setTextAlignment(TextAlignment.JUSTIFIED)
	                    .setMarginBottom(15)
	                    .setMultipliedLeading(0.8f)  
	                    .setFontColor(new DeviceRgb(0, 100, 0))); 


	            document.add(new Paragraph(
	                    "\u00A0\u00A0\u00A0\u00A0\u00A0మీరు, మీ కుటుంబ సభ్యులు ఆరోగ్యంగా, ఆనందంగా ఉండేందుకు మన ప్రజా ప్రభుత్వం చిత్తశుద్ధితో కృషి చేస్తుంది.")
	                    .setFont(font)
	                    .setFontSize(12)
	                    .setTextAlignment(TextAlignment.JUSTIFIED)
	                    .setMultipliedLeading(0.8f)
	                    .setFontColor(new DeviceRgb(0, 100, 0)));

	            Text boldText1 = new Text("భవదీయ,").setBold();
	            document.add(new Paragraph().add(boldText1)
	                    .setFont(font)
	                    .setPaddingRight(15f)
	                    .setFontSize(12)
	                    .setTextAlignment(TextAlignment.RIGHT)
	                    .setFontColor(new DeviceRgb(0, 100, 0)));

	            try {
	                ImageData rightImageData = ImageDataFactory.create(context_path + "/images/cmtelsig.jpg");
	                Image rightImage = new Image(rightImageData).scaleToFit(70, 70).setBorder(Border.NO_BORDER);

	                Paragraph imageParagraph = new Paragraph().add(rightImage).setTextAlignment(TextAlignment.RIGHT);
	                document.add(imageParagraph);
	            } catch (IOException e) {
	                e.printStackTrace();
	            }
	            
	            Text boldText2 = new Text("(ఎ. రేవంత్ రెడ్డి)").setBold();
	            document.add(new Paragraph().add(boldText2)
	                    .setFont(font)
	                    .setFontSize(12)
	                    .setTextAlignment(TextAlignment.RIGHT)
	                    .setFontColor(new DeviceRgb(0, 100, 0)));
	            
	            Text boldText3 = new Text("To").setBold();
	            document.add(new Paragraph().add(boldText3)
	                    .setFont(font)
	                    .setFontSize(12)
	                    .setTextAlignment(TextAlignment.LEFT)
	                    .setFontColor(new DeviceRgb(0, 100, 0)));
	            
	            Text boldText4 = new Text(formData.getPatientName()+",").setBold();
	            document.add(new Paragraph().add(boldText4)
	                    .setFont(font)
	                    .setFontSize(12)
	                    .setTextAlignment(TextAlignment.LEFT)
	                    .setMultipliedLeading(0.8f)
	                    .setFontColor(new DeviceRgb(0, 100, 0)));
	            
	            document.add(new Paragraph(
	                    formData.getFatherName()+",")
	                    .setFont(font)
	                    .setFontSize(12)
	                    .setTextAlignment(TextAlignment.LEFT)
	                    .setMultipliedLeading(0.5f)
	                    .setFontColor(new DeviceRgb(0, 100, 0)));
	            
	            document.add(new Paragraph(formData.getAddress())
	                    .setFont(font)
	                    .setFontSize(12)
	                    .setTextAlignment(TextAlignment.LEFT)
	                    .setMultipliedLeading(0.6f)
	                    .setFontColor(new DeviceRgb(0, 100, 0)));

	            try {
	                ImageData triColor = ImageDataFactory.create(context_path + "/images/tricolorimg.jpg");
	                Image triImage = new Image(triColor).setBorder(Border.NO_BORDER);

	                float pageWidth = document.getPdfDocument().getDefaultPageSize().getWidth();
	                float margins = document.getLeftMargin() + document.getRightMargin();
	                float availableWidth = pageWidth - margins;

	                triImage.scaleAbsolute(availableWidth, 80);

	                Paragraph imagParagraph = new Paragraph().add(triImage).setTextAlignment(TextAlignment.CENTER);
	                document.add(imagParagraph);
	            } catch (IOException e) {
	                e.printStackTrace();
	            }


	            document.add(new Paragraph("CMRF Cell, LG Room No.5, Dr.B.R.Ambedkar Telangana Secretariat, Hyderabad. Ph.No. 040-23459944")
	                    .setFont(font)
	                    .setFontSize(9)
	                    .setTextAlignment(TextAlignment.CENTER)
	                    .setFontColor(new DeviceRgb(0, 100, 0)));

	            document.close();

	        } catch (IOException e) {
	            e.printStackTrace();
	        }
	        Map<String, String> resultMap = new HashMap<>();
	        resultMap.put("filePath", filePath);  
	        resultMap.put("mobileNo", formData.getMobileNo());  
	        resultMap.put("prMobileNo", formData.getPrMobileNo());  

	        return resultMap;
	}

	public Map<String,String> locLetterPdfDetails(HttpServletRequest request) throws IOException, WriterException{
		HttpSession sess = request.getSession();
        ServletContext context = sess.getServletContext();       
        String context_path=context.getRealPath("/");;
        System.out.println("context path is -------> "+context_path);
		// String pdfDirectoryPath = context_path + "generated-pdfs";
		 String mainPath = ApplicationConstants.WHATSAPP_LOC_LETTER_PATH + ApplicationConstants.AC_YEAR;
		
		 LocDetailsEntryForm formData = (LocDetailsEntryForm) request.getAttribute("formData");
	        if (formData == null) {
	            System.out.println("Form data not found in the request.");
	            return null;
	        }
	        String locNumber = formData.getLocNo().split("/")[0]; 
	        String fileName = locNumber + "_LOCLETTER" + ".pdf";
	        
	        // String filePath = pdfDirectoryPath + "/" + fileName;
	        String filePath = mainPath + "/" + fileName;
	        System.out.println("PDF will be saved to: " + filePath);
	        CreateFileDirectory fileDirectory = new CreateFileDirectory();
	        try {
				boolean fileCreated = fileDirectory.createDirectoriesAndEmptyFile(
				    ApplicationConstants.BASE_PATH + mainPath, fileName );
				 System.out.println(fileCreated+"fileCreated");
			} catch (Exception e) {
				e.printStackTrace();
			}

			//PDF Start

			try {
				// PdfWriter writer = new PdfWriter(filePath);  
				PdfWriter writer = new PdfWriter( ApplicationConstants.BASE_PATH +filePath);  
	            PdfDocument pdf = new PdfDocument(writer);

				Document document = new Document(pdf);
				document.setMargins(50, 50, 50, 50);

				PdfFont headingsFont = PdfFontFactory.createFont(StandardFonts.HELVETICA_BOLD);
				PdfFont normalFont = PdfFontFactory.createFont(StandardFonts.HELVETICA);

				float[] columnWidths = {2f, 2f, 2f}; // Column widths (1:4:1 ratio)
				Table headTable = new Table(columnWidths).setWidth(UnitValue.createPercentValue(100)); // Set table width to 100%

				// Left Cell Text
				Cell leftCell = new Cell()
								.add(new Paragraph("VEMULA SRINIVASULU,")
										.setFont(headingsFont)
										.setFontSize(12)
										.setTextAlignment(TextAlignment.LEFT)
										.setMargin(0)
										.setPaddingLeft(0)
										.setFontColor(new DeviceRgb(0, 51, 153))) 
								.add(new Paragraph("LLM., MBA.,")
										.setFont(headingsFont)
										.setFontSize(12)
										.setTextAlignment(TextAlignment.LEFT)
										.setMargin(0)
										.setPaddingLeft(70)
										.setFontColor(new DeviceRgb(0, 51, 153))) 
								.add(new Paragraph("O.S.D. TO CHIEF MINISTER")
										.setFont(headingsFont)
										.setFontSize(12)
										.setTextAlignment(TextAlignment.LEFT)
										.setMargin(0)
										.setPaddingLeft(0)
										.setFontColor(new DeviceRgb(0, 51, 153))) 
								.add(new Paragraph("GOVERNMENT OF TELANGANA")
										.setFont(headingsFont)
										.setFontSize(12)
										.setTextAlignment(TextAlignment.LEFT)
										.setMargin(0)
										.setPaddingLeft(0)
										.setFontColor(new DeviceRgb(0, 51, 153))) 
								.setBorder(Border.NO_BORDER)  
								.setHorizontalAlignment(HorizontalAlignment.CENTER);  

				// Add the cell to the table
				headTable.addCell(leftCell);

				// Add left image (left-aligned)
				try {
					ImageData centerImageData = ImageDataFactory.create(context_path + "/images/tsemblem.gif");
					Image centerImage = new Image(centerImageData).scaleToFit(80, 70).setRelativePosition(0,5,0,0); // Scale image
					Cell centerImageCell = new Cell()
							.add(centerImage)
							.setBorder(Border.NO_BORDER) // No border
							.setHorizontalAlignment(HorizontalAlignment.CENTER); // Force left alignment
					headTable.addCell(centerImageCell);
				} catch (IOException e) {
					e.printStackTrace();
				}
				
				// Left Cell Text
				Paragraph address = new Paragraph("Room No.3, 6th Floor,\nDr.B.R.Ambedkar Telangana\nSecretariat Hyderabad.\nPh.No. 040-23450466\nFax No. 040-23459944")
						.setFont(normalFont)
						.setFontSize(10)
						.setFontColor(new DeviceRgb(0, 51, 153))
						.setTextAlignment(TextAlignment.RIGHT); 

				// Create left cell
				Cell rightCell = new Cell()
						.add(address)
						.setBorder(Border.NO_BORDER) // No border
						.setTextAlignment(TextAlignment.LEFT) // Center text horizontally
						.setVerticalAlignment(VerticalAlignment.MIDDLE); // Vertically center

				// Add the cell to the table
				headTable.addCell(rightCell);

				// Add the table to the document
				document.add(headTable);

				document.add(new Paragraph("CHIEF MINISTER'S OFFICE")
						.setFont(headingsFont) // Use the existing font
						.setFontSize(18)
						.setTextAlignment(TextAlignment.CENTER)
						.setPaddingLeft(30)
						.setMarginBottom(10)
						.setBorder(Border.NO_BORDER)
						.setFontColor(new DeviceRgb(165, 42, 42))); 

				document.add(new Paragraph("Lr. No."+ formData.getLocNo() +", Dated:" + formData.getLetterDate())
						.setFont(headingsFont) // Use the existing font
						.setFontSize(12)
						.setTextAlignment(TextAlignment.CENTER)
						.setPaddingLeft(30)
						.setUnderline()
						.setMarginBottom(10)); 

				Cell ToCell = new Cell()
								.add(new Paragraph("To\nThe Director,\nM/s " + formData.getHospName() + ".")
										.setFont(headingsFont)
										.setFontSize(10)
										.setTextAlignment(TextAlignment.LEFT)
										.setMarginBottom(10)
										.setPaddingLeft(20)) 
								.setBorder(Border.NO_BORDER)  
								.setHorizontalAlignment(HorizontalAlignment.CENTER);  

				// Add the cell to the table
				document.add(ToCell);

				Cell SubCell = new Cell()
								.add(new Paragraph("Sir,")
										.setFont(normalFont)
										.setFontSize(10)
										.setTextAlignment(TextAlignment.LEFT)
										.setMargin(0)
										.setPaddingLeft(20)) 
								.add(new Paragraph("Sub:- CMRF-Sanction of LOC Under CMRF to M/s "+ formData.getHospName() +" towards treatment of individuals-Reg.")
										.setFont(normalFont)
										.setFontSize(10)
										.setTextAlignment(TextAlignment.LEFT)
										.setMarginBottom(5)
										.setPaddingLeft(50)
										.setPaddingRight(50)
										.setTextAlignment(TextAlignment.JUSTIFIED)) 
								.setBorder(Border.NO_BORDER)  
								.setHorizontalAlignment(HorizontalAlignment.CENTER);  

				// Add the cell to the table
				document.add(SubCell);

				int sno = 0; 

				Table refTable = new Table(UnitValue.createPercentArray(new float[]{1f, 0.3f, 8.7f})).useAllAvailableWidth(); // Define table

				// Condition: If VIP Name is not empty
				if (formData.getVipName() != null && !formData.getVipName().isEmpty()) {
					sno++; // Increment serial number

					refTable.addCell(new Cell().add(new Paragraph("Ref:-"))
							.setFont(normalFont)
							.setFontSize(10)
							.setBorder(Border.NO_BORDER)
							.setPaddingLeft(53)
							.setTextAlignment(TextAlignment.LEFT));

					refTable.addCell(new Cell().add(new Paragraph(sno + "."))
							.setFont(normalFont)
							.setFontSize(10)
							.setBorder(Border.NO_BORDER)
							.setTextAlignment(TextAlignment.LEFT));

					refTable.addCell(new Cell().add(new Paragraph("Letter from the " + formData.getPrefix() + " " 
							+ formData.getVipName() + ", " + formData.getVipDesg() + ", dt.:" + formData.getVipLetterDate()))
							.setFont(normalFont)
							.setFontSize(10)
							.setBorder(Border.NO_BORDER)
							.setTextAlignment(TextAlignment.LEFT));
				}

				// Condition: If VIP Name is empty
				if (formData.getVipName() == null || formData.getVipName().isEmpty()) {
					refTable.addCell(new Cell().add(new Paragraph("Ref:-"))
							.setFont(normalFont)
							.setFontSize(10)
							.setBorder(Border.NO_BORDER)
							.setPaddingLeft(53)
							.setTextAlignment(TextAlignment.LEFT));

					refTable.addCell(new Cell().add(new Paragraph(" "))
							.setBorder(Border.NO_BORDER));

					refTable.addCell(new Cell().add(new Paragraph(" "))
							.setBorder(Border.NO_BORDER));
				}

				// Condition: If previous LOC number exists
				if (formData.getPrevLocNo() != null && !formData.getPrevLocNo().isEmpty()) {
					sno++; // Increment serial number

					refTable.addCell(new Cell().add(new Paragraph(" "))
							.setBorder(Border.NO_BORDER));

					refTable.addCell(new Cell().add(new Paragraph(sno + "."))
							.setFont(normalFont)
							.setFontSize(10)
							.setBorder(Border.NO_BORDER)
							.setTextAlignment(TextAlignment.LEFT));

					refTable.addCell(new Cell().add(new Paragraph("LOC Lr.No. " + formData.getPrevLocNo() + ", Dated: " + formData.getPreLocDate()))
							.setFont(normalFont)
							.setFontSize(10)
							.setBorder(Border.NO_BORDER)
							.setTextAlignment(TextAlignment.LEFT));
				}

				// Add table to document
				document.add(refTable);

				Paragraph centeredText = new Paragraph("@@@")
						.setTextAlignment(TextAlignment.CENTER)
						.setFont(normalFont)
						.setFontSize(10)
						.setMarginTop(5); 

				document.add(centeredText);

				Paragraph justificationText = new Paragraph()
						.setFont(normalFont)
						.setFirstLineIndent(40)
						.setFontSize(10)
						.setTextAlignment(TextAlignment.JUSTIFIED)
						.setPaddingLeft(20)
						.setPaddingRight(30);

				justificationText.add("The Hon'ble Chief Minister desires that the treatment of ")
						.add(new Text(formData.getPatientName())).add(", ")
						.add(new Text(formData.getFatherName())).add(", ")
						.add(new Text(formData.getAddress())).add(" be extended as the Hon'ble Chief Minister has assured for sanction of an amount of Rs. ")
						.add(new Text(formData.getFormatAmt() + "/-").setBold())
						.add(new Text(" (Rupees ").setBold()).add(new Text(formData.getAmtInWords()).setBold()).add(new Text(")").setBold());

				if (formData.getPurpose() != null && !formData.getPurpose().isEmpty()) {
					justificationText.add(" to undergo " + formData.getPurpose());
				}

				if (formData.getPreLocDate() != null && !formData.getPreLocDate().isEmpty()) {
					justificationText.add(". In addition to the previous LOC, dated " + formData.getPreLocDate() + ".");
				}

				document.add(justificationText);

				Paragraph actionText = new Paragraph()
						.setFont(normalFont)
						.setFontSize(10)
						.setFirstLineIndent(40)
						.setTextAlignment(TextAlignment.JUSTIFIED) 
						.setPaddingLeft(20)
						.setPaddingRight(30)
						.add("Necessary action may please be taken for providing treatment to the individual and send the bill after completion of the treatment.");

				document.add(actionText);

				Paragraph bestWishes = new Paragraph("With best wishes,")
						.setFont(normalFont)
						.setFontSize(10)
						.setBold()
						.setTextAlignment(TextAlignment.CENTER) // Center align
						.setMarginTop(5); // Margin for spacing

				document.add(bestWishes);

				document.add(new Paragraph(" ").setMarginTop(5)); // Add spacing

				Paragraph yoursSincerely = new Paragraph("Yours sincerely,")
						.setFont(normalFont)
						.setFontSize(10)
						.setBold()
						.setPaddingRight(30)
						.setTextAlignment(TextAlignment.RIGHT); // Right align

				document.add(yoursSincerely);

				Div signatureDiv = new Div().setTextAlignment(TextAlignment.RIGHT);

				if ("Old".equals(formData.getLocType())) {
					signatureDiv.add(new Paragraph("(P. RAJASEKHAR REDDY)")
							.setFont(normalFont)
							.setFontSize(10)
							.setMarginTop(10)
							.setPaddingRight(30)
							.setFontColor(new DeviceRgb(0, 0, 255)) // Blue color
							.setBold());
				} else if ("New".equals(formData.getLocType())) {
					try {
						ImageData signImageData = ImageDataFactory.create(context_path + "/images/OSD-sign.png");
						Image signImage = new Image(signImageData).scaleToFit(150, 40).setHorizontalAlignment(HorizontalAlignment.RIGHT).setMarginRight(30);

						signatureDiv.add(signImage);

						signatureDiv.add(new Paragraph("(Vemula Srinivasulu)")
								.setFont(normalFont)
								.setFontSize(10)
								.setPaddingRight(30)
								.setFontColor(new DeviceRgb(0, 0, 255)) // Blue color
								.setBold());
					} catch (IOException e) {
						e.printStackTrace();
					}
				}

				Cell signatureCell = new Cell()
						.add(signatureDiv)
						.setBorder(Border.NO_BORDER)
						.setTextAlignment(TextAlignment.RIGHT); // Ensures everything stays right-aligned

				document.add(signatureCell);

				// Create the "Copy to:" label
				Paragraph copyTo = new Paragraph(new Text("Copy to:\n").setBold())
						.setFontSize(10)
						.setPaddingLeft(20);
				
				Paragraph vipDetails = new Paragraph();
				// Add VIP details conditionally
				if (formData.getVipName() != null && !formData.getVipName().isEmpty()) {
					vipDetails = new Paragraph()
							.setPaddingLeft(20)
							.add(new Text(formData.getPrefix() + " " + formData.getVipName() + ",")
									.setFont(normalFont)
									.setFontSize(10)
									.setBold());
							if (formData.getVipDesg() != null && !formData.getVipDesg().isEmpty()) {
								vipDetails.add("\n")
								.add(new Text(formData.getVipDesg())
										.setFont(normalFont)
										.setFontSize(10));
							}
							vipDetails.add("\n")
							.add(new Text("Copy to:\n").setBold().setFontSize(10));
				}

				// Create paragraph for patient details
				Paragraph patientDetails = new Paragraph()
						.setPaddingLeft(20)
						.add(new Text(formData.getPatientName())
								.setFontSize(10)
								.setBold())
						.add("\n")  // Newline after patient name
						.add(new Text(formData.getFatherName())
								.setFont(normalFont)
								.setFontSize(10))
						.add("\n")  // Newline after father name
						.add(new Text(formData.getAddress())
								.setFont(normalFont)
								.setFontSize(10));

				// Create a table with two columns
				Table QRTABLE = new Table(2);
				QRTABLE.setWidth(UnitValue.createPercentValue(100)); // Set table width to 100%

				// Add "Copy to" content to the left column
				Cell copyToCell = new Cell()
						.add(copyTo)
						.add(vipDetails)
						.add(patientDetails)
						.setBorder(Border.NO_BORDER)
						.setTextAlignment(TextAlignment.LEFT);
				QRTABLE.addCell(copyToCell);

				// QR Code data
				String qrData = "{\"tokenNumber\" : \"" + formData.getLocNo().trim() + "\","
						+ "\"patientName\" : \"" + formData.getPatientName().trim() + "\","
					 	// + "\"PeopleRepresentative\" : \"" + formData.getVipName().trim() + ", " + formData.getVipDesg().trim() + "\","
						+ "\"Amount\" : \"" + formData.getAssuredAmt().trim() + "\"}";

				// Generate QR Code
				byte[] qrCodeImage = QRCodeUtil.generateQRCode(qrData, 100, 100);

				// Create the cell for the QR code (span across two columns)
				Cell qrCell = new Cell(1, 2);
				qrCell.setHorizontalAlignment(HorizontalAlignment.RIGHT);
				qrCell.setBorder(Border.NO_BORDER);

				if (qrCodeImage != null) {
					ImageData qrImageData = ImageDataFactory.create(qrCodeImage);
					Image qrImage = new Image(qrImageData);
					qrImage.scaleToFit(100, 100); // Scale the image if needed
					qrImage.setMarginRight(15);
					qrImage.setHorizontalAlignment(HorizontalAlignment.RIGHT);
					qrCell.add(qrImage);
				} else {
					// If QR code generation fails, display placeholder text
					qrCell.add(new Paragraph("QR Code Here").setFont(normalFont).setFontSize(10));
				}

				// Add QR code cell to the table
				QRTABLE.addCell(qrCell);

				document.add(QRTABLE);

				Table noteTable = new Table(2);
				noteTable.setWidth(UnitValue.createPercentValue(100));  

				// First column - Note label
				Cell noteLabelCell = new Cell()
						.add(new Paragraph("Note:-").setFont(normalFont).setFontSize(10))
						.setBorder(Border.NO_BORDER)
						.setVerticalAlignment(VerticalAlignment.TOP)
						.setPaddingLeft(20);
				noteTable.addCell(noteLabelCell);

				// Second column - Text content
				Cell noteContentCell = new Cell()
						.add(new Paragraph("This letter does not require physical signature and this certificate can be verified by scanning QR code printed on this letter.")
								.setFont(normalFont)
								.setFontSize(10)
								.setTextAlignment(TextAlignment.JUSTIFIED))
						.setBorder(Border.NO_BORDER)
						.setPaddingRight(50);
				noteTable.addCell(noteContentCell);

				// Add the table to the document
				document.add(noteTable);

				// Close the document
				document.close();

			} catch (IOException e) {
				e.printStackTrace();
			}

			//PDF END

	        Map<String, String> resultMap = new HashMap<>();
	        resultMap.put("filePath", filePath);  
	        resultMap.put("mobileNo", formData.getMobileNo());  
	        resultMap.put("prMobileNo", formData.getPrMobileNo());  
	        resultMap.put("supdtMobileNo", formData.getSupdtMobileNo());  

	        return resultMap;
	}
	
	
    public void PatLOCLetterPDF(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException  {
        System.out.println("GENERATING PDF");

        HttpSession sess = request.getSession();
        ServletContext context = sess.getServletContext();       
        String context_path=context.getRealPath("/");;
        System.out.println("context path is -------> "+context_path);

        String pdfDirectoryPath = context_path + "generated-pdfs/";
        File pdfDirectory = new File(pdfDirectoryPath);

        // Retrieve formData object from the request/session
        LocDetailsEntryForm formData = (LocDetailsEntryForm) request.getAttribute("formData");
        if (formData == null) {
            System.out.println("Form data not found in the request.");
            return;
        }

        String LOCNo = formData.getLocNo().split("/")[0];
        String pdfFilePath = pdfDirectoryPath + LOCNo + "-LOC.pdf";
        System.out.println("PDF will be saved to: " + pdfFilePath);

        File licensePath1 = new File("C:\\Users\\<USER>\\Desktop\\cmrf_utils\\10f21439604cf245e3bf45eb0ab48823f9a34602bd86754457c0d380ff96302c.json");
        // File licensePath = new File("C:\\Users\\<USER>\\Desktop\\cmrf_utils\\license.json");
        File licensePath2 = new File("C:\\Users\\<USER>\\Desktop\\cmrf_utils\\33c2febc8d2ef5d69c9557ea9034233f3f32257e0022f3d95b662019573fd806.json");


        LicenseKey.loadLicenseFile(licensePath1);
        // LicenseKey.loadLicenseFile(licensePath);
        LicenseKey.loadLicenseFile(licensePath2);
        
		PdfFont font = PdfFontFactory.createFont(context_path+"fonts/Gautami.ttf", PdfEncodings.IDENTITY_H,EmbeddingStrategy.PREFER_EMBEDDED, true);

        try {
            PdfWriter writer = new PdfWriter(pdfFilePath); // Save the PDF to the specified file path
            PdfDocument pdf = new PdfDocument(writer);
            pdf.setTagged(); // Enable complex text rendering
            pdf.getCatalog().setLang(new com.itextpdf.kernel.pdf.PdfString("te-IN")); // Set Telugu language

            Document document = new Document(pdf);
            document.setMargins(50, 70, 50, 70);

            float[] columnWidths = {1f, 4f, 1f}; // Column widths (1:4:1 ratio)
            Table headTable = new Table(columnWidths).setWidth(UnitValue.createPercentValue(100)); // Set table width to 100%

            // Add left image (left-aligned)
            try {
                ImageData leftImageData = ImageDataFactory.create(context_path + "/images/tsemblem.gif");
                Image leftImage = new Image(leftImageData).scaleToFit(80, 70).setRelativePosition(0,5,0,0); // Scale image
                Cell leftImageCell = new Cell()
                        .add(leftImage)
                        .setBorder(Border.NO_BORDER) // No border
                        .setHorizontalAlignment(HorizontalAlignment.LEFT); // Force left alignment
                headTable.addCell(leftImageCell);
            } catch (IOException e) {
                e.printStackTrace();
            }

            // Add center image and text (center-aligned)
            try {
                ImageData centerImageData = ImageDataFactory.create(context_path + "/images/CmImageNOH.jpg");
                Image centerImage = new Image(centerImageData).scaleToFit(80, 80).setHorizontalAlignment(HorizontalAlignment.CENTER); // Scale image

                // Stack the image and text vertically in the center cell
                Cell centerCell = new Cell()
                        .add(centerImage)
                        .add(new Paragraph("ఎ. రేవంత్ రెడ్డి")
                                .setFont(font)
                                .setFontSize(12)
                                .setTextAlignment(TextAlignment.CENTER)
                                .setMargin(0)
                                .setPadding(0)
                                .setMultipliedLeading(0.8f)
                                .setFontColor(new DeviceRgb(236, 125, 20))) 
                        .add(new Paragraph("ముఖ్యమంత్రి")
                                .setFont(font)
                                .setFontSize(12)
                                .setTextAlignment(TextAlignment.CENTER)
                                .setMargin(0)
                                .setPadding(0)
                                .setMultipliedLeading(0.5f)
                                .setFontColor(new DeviceRgb(236, 125, 20)))
                        .setBorder(Border.NO_BORDER) // No border
                        .setHorizontalAlignment(HorizontalAlignment.CENTER); // Center alignment
                headTable.addCell(centerCell);
            } catch (IOException e) {
                e.printStackTrace();
            }

            // Add right image (right-aligned)
            try {
                ImageData rightImageData = ImageDataFactory.create(context_path + "/images/rising-whiteBg.jpg");
                Image rightImage = new Image(rightImageData).scaleToFit(80, 80).setHorizontalAlignment(HorizontalAlignment.RIGHT); // Scale image
                Cell rightImageCell = new Cell()
                        .add(rightImage)
                        .setBorder(Border.NO_BORDER) // No border
                        .setHorizontalAlignment(HorizontalAlignment.RIGHT); // Force right alignment
                headTable.addCell(rightImageCell);
            } catch (IOException e) {
                e.printStackTrace();
            }

            // Add the table to the document
            document.add(headTable);

            // Add a title or introductory paragraph with bold text
            Text boldText = new Text(formData.getPatientName() + " గారికి.").setBold();

            document.add(new Paragraph()
                    .add(boldText) // Add the bold text
                    .setFont(font) // Use the existing font
                    .setFontSize(12)
                    .setTextAlignment(TextAlignment.LEFT)
                    .setMarginBottom(10)
                    .setFontColor(new DeviceRgb(0, 100, 0))); // Adjust margin for spacing


            // Add the main content paragraph
            document.add(new Paragraph(
                    "\u00A0\u00A0\u00A0\u00A0\u00A0మీ దరఖాస్తును పరిశీలించి, ముఖ్యమంత్రి సహాయనిధి నుండి రూ " + formData.getFormatAmt() + "/-\n" +
                    "(" + formData.getAmtInWords() + ") లు వైద్య ఖర్చు నిమిత్తము " + formData.getHospName() +
                    " ఆసుపత్రి కి లెటర్ ఆఫ్ క్రెడిట్ సంఖ్య " + formData.getLocNo() + ", తేదీ: " + formData.getLetterDate() + " ద్వారా మంజూరు చేయడం జరిగింది.")
                    .setFont(font)
                    .setFontSize(12)
                    .setTextAlignment(TextAlignment.JUSTIFIED)
                    .setMarginBottom(15)
                    .setMultipliedLeading(0.8f) // Set custom line spacing (0.5x the default leading)
                    .setFontColor(new DeviceRgb(0, 100, 0))); // Adjust font color to dark green


            // Add the final statement paragraph
            document.add(new Paragraph(
                    "\u00A0\u00A0\u00A0\u00A0\u00A0మీరు, మీ కుటుంబ సభ్యులు ఆరోగ్యంగా, ఆనందంగా ఉండేందుకు మన ప్రజా ప్రభుత్వం చిత్తశుద్ధితో కృషి చేస్తుంది.")
                    .setFont(font)
                    .setFontSize(12)
                    .setTextAlignment(TextAlignment.JUSTIFIED)
                    .setMultipliedLeading(0.8f)
                    .setFontColor(new DeviceRgb(0, 100, 0)));

            // Add the final statement paragraph
            Text boldText1 = new Text("భవదీయ,").setBold();
            document.add(new Paragraph().add(boldText1)
                    .setFont(font)
                    .setPaddingRight(15f)
                    .setFontSize(12)
                    .setTextAlignment(TextAlignment.RIGHT)
                    .setFontColor(new DeviceRgb(0, 100, 0)));

            try {
                ImageData rightImageData = ImageDataFactory.create(context_path + "/images/cmtelsig.jpg");
                Image rightImage = new Image(rightImageData).scaleToFit(70, 70).setBorder(Border.NO_BORDER);

                Paragraph imageParagraph = new Paragraph().add(rightImage).setTextAlignment(TextAlignment.RIGHT);
                document.add(imageParagraph);
            } catch (IOException e) {
                e.printStackTrace();
            }
            
            // Add the final statement paragraph
            Text boldText2 = new Text("(ఎ. రేవంత్ రెడ్డి)").setBold();
            document.add(new Paragraph().add(boldText2)
                    .setFont(font)
                    .setFontSize(12)
                    .setTextAlignment(TextAlignment.RIGHT)
                    .setFontColor(new DeviceRgb(0, 100, 0)));
            
            // Add the final statement paragraph
            Text boldText3 = new Text("To").setBold();
            document.add(new Paragraph().add(boldText3)
                    .setFont(font)
                    .setFontSize(12)
                    .setTextAlignment(TextAlignment.LEFT)
                    .setFontColor(new DeviceRgb(0, 100, 0)));
            
            // Add the final statement paragraph
            Text boldText4 = new Text(formData.getPatientName()+",").setBold();
            document.add(new Paragraph().add(boldText4)
                    .setFont(font)
                    .setFontSize(12)
                    .setTextAlignment(TextAlignment.LEFT)
                    .setMultipliedLeading(0.8f)
                    .setFontColor(new DeviceRgb(0, 100, 0)));
            
            // Add the final statement paragraph
            document.add(new Paragraph(
                    formData.getFatherName()+",")
                    .setFont(font)
                    .setFontSize(12)
                    .setTextAlignment(TextAlignment.LEFT)
                    .setMultipliedLeading(0.5f)
                    .setFontColor(new DeviceRgb(0, 100, 0)));
            
            // Add the final statement paragraph
            document.add(new Paragraph(formData.getAddress())
                    .setFont(font)
                    .setFontSize(12)
                    .setTextAlignment(TextAlignment.LEFT)
                    .setMultipliedLeading(0.6f)
                    .setFontColor(new DeviceRgb(0, 100, 0)));

            try {
                ImageData triColor = ImageDataFactory.create(context_path + "/images/tricolorimg.jpg");
                Image triImage = new Image(triColor).setBorder(Border.NO_BORDER);

                float pageWidth = document.getPdfDocument().getDefaultPageSize().getWidth();
                float margins = document.getLeftMargin() + document.getRightMargin();
                float availableWidth = pageWidth - margins;

                triImage.scaleAbsolute(availableWidth, 80);

                Paragraph imagParagraph = new Paragraph().add(triImage).setTextAlignment(TextAlignment.CENTER);
                document.add(imagParagraph);
            } catch (IOException e) {
                e.printStackTrace();
            }


            // Add the final statement paragraph
            document.add(new Paragraph("CMRF Cell, LG Room No.5, Dr.B.R.Ambedkar Telangana Secretariat, Hyderabad. Ph.No. 040-23459944")
                    .setFont(font)
                    .setFontSize(9)
                    .setTextAlignment(TextAlignment.CENTER)
                    .setFontColor(new DeviceRgb(0, 100, 0)));

            // Close the document
            document.close();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
	
    public void LOCLetterPDF(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException, WriterException  {
        System.out.println("GENERATING PDF");

        HttpSession sess = request.getSession();
        ServletContext context = sess.getServletContext();       
        String context_path=context.getRealPath("/");;
        System.out.println("context path is -------> "+context_path);

        LocDetailsEntryForm formData = (LocDetailsEntryForm) request.getAttribute("formData");
        if (formData == null) {
			System.out.println("Form data not found in the request.");
            return;
        }
		
		// Set content type to PDF
		response.setContentType("application/pdf");
		response.setHeader("Content-Disposition", "attachment; filename=" + formData.getLocNo() + "_LOCLETTER.pdf");

        try {
			ServletOutputStream outStream = response.getOutputStream();
            PdfWriter writer = new PdfWriter(outStream);
            PdfDocument pdf = new PdfDocument(writer);

            Document document = new Document(pdf);
            document.setMargins(50, 50, 50, 50);

            PdfFont headingsFont = PdfFontFactory.createFont(StandardFonts.HELVETICA_BOLD);
            PdfFont normalFont = PdfFontFactory.createFont(StandardFonts.HELVETICA);

            float[] columnWidths = {2f, 2f, 2f}; // Column widths (1:4:1 ratio)
            Table headTable = new Table(columnWidths).setWidth(UnitValue.createPercentValue(100)); // Set table width to 100%

            // Left Cell Text
			Cell leftCell = new Cell()
	                        .add(new Paragraph("VEMULA SRINIVASULU,")
	                                .setFont(headingsFont)
	                                .setFontSize(12)
	                                .setTextAlignment(TextAlignment.LEFT)
	                                .setMargin(0)
	                                .setPaddingLeft(0)
	                                .setFontColor(new DeviceRgb(0, 51, 153))) 
	                        .add(new Paragraph("LLM., MBA.,")
	                                .setFont(headingsFont)
	                                .setFontSize(12)
	                                .setTextAlignment(TextAlignment.LEFT)
	                                .setMargin(0)
	                                .setPaddingLeft(70)
	                                .setFontColor(new DeviceRgb(0, 51, 153))) 
	                        .add(new Paragraph("O.S.D. TO CHIEF MINISTER")
	                                .setFont(headingsFont)
	                                .setFontSize(12)
	                                .setTextAlignment(TextAlignment.LEFT)
	                                .setMargin(0)
	                                .setPaddingLeft(0)
	                                .setFontColor(new DeviceRgb(0, 51, 153))) 
	                        .add(new Paragraph("GOVERNMENT OF TELANGANA")
	                                .setFont(headingsFont)
	                                .setFontSize(12)
	                                .setTextAlignment(TextAlignment.LEFT)
	                                .setMargin(0)
	                                .setPaddingLeft(0)
	                                .setFontColor(new DeviceRgb(0, 51, 153))) 
	                        .setBorder(Border.NO_BORDER)  
	                        .setHorizontalAlignment(HorizontalAlignment.CENTER);  

            // Add the cell to the table
            headTable.addCell(leftCell);

            // Add left image (left-aligned)
            try {
                ImageData centerImageData = ImageDataFactory.create(context_path + "/images/tsemblem.gif");
                Image centerImage = new Image(centerImageData).scaleToFit(80, 70).setRelativePosition(0,5,0,0); // Scale image
                Cell centerImageCell = new Cell()
                        .add(centerImage)
                        .setBorder(Border.NO_BORDER) // No border
                        .setHorizontalAlignment(HorizontalAlignment.CENTER); // Force left alignment
                headTable.addCell(centerImageCell);
            } catch (IOException e) {
                e.printStackTrace();
            }
            
            // Left Cell Text
            Paragraph address = new Paragraph("Room No.3, 6th Floor,\nDr.B.R.Ambedkar Telangana\nSecretariat Hyderabad.\nPh.No. 040-23450466\nFax No. 040-23459944")
                    .setFont(normalFont)
                    .setFontSize(10)
                    .setFontColor(new DeviceRgb(0, 51, 153))
                    .setTextAlignment(TextAlignment.RIGHT); 

            // Create left cell
            Cell rightCell = new Cell()
                    .add(address)
                    .setBorder(Border.NO_BORDER) // No border
                    .setTextAlignment(TextAlignment.LEFT) // Center text horizontally
                    .setVerticalAlignment(VerticalAlignment.MIDDLE); // Vertically center

            // Add the cell to the table
            headTable.addCell(rightCell);

            // Add the table to the document
            document.add(headTable);

            document.add(new Paragraph("CHIEF MINISTER'S OFFICE")
                    .setFont(headingsFont) // Use the existing font
                    .setFontSize(18)
                    .setTextAlignment(TextAlignment.CENTER)
					.setPaddingLeft(30)
                    .setMarginBottom(10)
					.setBorder(Border.NO_BORDER)
					.setFontColor(new DeviceRgb(165, 42, 42))); 

            document.add(new Paragraph("Lr. No."+ formData.getLocNo() +", Dated:" + formData.getLetterDate())
                    .setFont(headingsFont) // Use the existing font
                    .setFontSize(12)
                    .setTextAlignment(TextAlignment.CENTER)
					.setPaddingLeft(30)
					.setUnderline()
                    .setMarginBottom(10)); 

			Cell ToCell = new Cell()
	                        .add(new Paragraph("To\nThe Director,\nM/s " + formData.getHospName() + ".")
	                                .setFont(headingsFont)
	                                .setFontSize(10)
	                                .setTextAlignment(TextAlignment.LEFT)
	                                .setMarginBottom(10)
	                                .setPaddingLeft(20)) 
	                        .setBorder(Border.NO_BORDER)  
	                        .setHorizontalAlignment(HorizontalAlignment.CENTER);  

            // Add the cell to the table
            document.add(ToCell);

			Cell SubCell = new Cell()
	                        .add(new Paragraph("Sir,")
	                                .setFont(normalFont)
	                                .setFontSize(10)
	                                .setTextAlignment(TextAlignment.LEFT)
	                                .setMargin(0)
	                                .setPaddingLeft(20)) 
	                        .add(new Paragraph("Sub:- CMRF-Sanction of LOC Under CMRF to M/s "+ formData.getHospName() +" towards treatment of individuals-Reg.")
	                                .setFont(normalFont)
	                                .setFontSize(10)
	                                .setTextAlignment(TextAlignment.LEFT)
	                                .setMarginBottom(5)
	                                .setPaddingLeft(50)
									.setPaddingRight(50)
									.setTextAlignment(TextAlignment.JUSTIFIED)) 
	                        .setBorder(Border.NO_BORDER)  
	                        .setHorizontalAlignment(HorizontalAlignment.CENTER);  

            // Add the cell to the table
            document.add(SubCell);

			int sno = 0; 

			Table refTable = new Table(UnitValue.createPercentArray(new float[]{1f, 0.3f, 8.7f})).useAllAvailableWidth(); // Define table

			// Condition: If VIP Name is not empty
			if (formData.getVipName() != null && !formData.getVipName().isEmpty()) {
				sno++; // Increment serial number

				refTable.addCell(new Cell().add(new Paragraph("Ref:-"))
						.setFont(normalFont)
						.setFontSize(10)
						.setBorder(Border.NO_BORDER)
						.setPaddingLeft(53)
						.setTextAlignment(TextAlignment.LEFT));

				refTable.addCell(new Cell().add(new Paragraph(sno + "."))
						.setFont(normalFont)
						.setFontSize(10)
						.setBorder(Border.NO_BORDER)
						.setTextAlignment(TextAlignment.LEFT));

				refTable.addCell(new Cell().add(new Paragraph("Letter from the " + formData.getPrefix() + " " 
						+ formData.getVipName() + ", " + formData.getVipDesg() + ", dt.:" + formData.getVipLetterDate()))
						.setFont(normalFont)
						.setFontSize(10)
						.setBorder(Border.NO_BORDER)
						.setTextAlignment(TextAlignment.LEFT));
			}

			// Condition: If VIP Name is empty
			if (formData.getVipName() == null || formData.getVipName().isEmpty()) {
				refTable.addCell(new Cell().add(new Paragraph("Ref:-"))
						.setFont(normalFont)
						.setFontSize(10)
						.setBorder(Border.NO_BORDER)
						.setPaddingLeft(53)
						.setTextAlignment(TextAlignment.LEFT));

				refTable.addCell(new Cell().add(new Paragraph(" "))
						.setBorder(Border.NO_BORDER));

				refTable.addCell(new Cell().add(new Paragraph(" "))
						.setBorder(Border.NO_BORDER));
			}

			// Condition: If previous LOC number exists
			if (formData.getPrevLocNo() != null && !formData.getPrevLocNo().isEmpty()) {
				sno++; // Increment serial number

				refTable.addCell(new Cell().add(new Paragraph(" "))
						.setBorder(Border.NO_BORDER));

				refTable.addCell(new Cell().add(new Paragraph(sno + "."))
						.setFont(normalFont)
						.setFontSize(10)
						.setBorder(Border.NO_BORDER)
						.setTextAlignment(TextAlignment.LEFT));

				refTable.addCell(new Cell().add(new Paragraph("LOC Lr.No. " + formData.getPrevLocNo() + ", Dated: " + formData.getPreLocDate()))
						.setFont(normalFont)
						.setFontSize(10)
						.setBorder(Border.NO_BORDER)
						.setTextAlignment(TextAlignment.LEFT));
			}

			// Add table to document
			document.add(refTable);

			Paragraph centeredText = new Paragraph("@@@")
					.setTextAlignment(TextAlignment.CENTER)
					.setFont(normalFont)
					.setFontSize(10)
					.setMarginTop(5); 

			document.add(centeredText);

			Paragraph justificationText = new Paragraph()
					.setFont(normalFont)
					.setFirstLineIndent(40)
					.setFontSize(10)
					.setTextAlignment(TextAlignment.JUSTIFIED)
					.setPaddingLeft(20)
					.setPaddingRight(30);

			justificationText.add("The Hon'ble Chief Minister desires that the treatment of ")
					.add(new Text(formData.getPatientName())).add(", ")
					.add(new Text(formData.getFatherName())).add(", ")
					.add(new Text(formData.getAddress())).add(" be extended as the Hon'ble Chief Minister has assured for sanction of an amount of Rs. ")
					.add(new Text(formData.getFormatAmt() + "/-").setBold())
					.add(new Text(" (Rupees ").setBold()).add(new Text(formData.getAmtInWords()).setBold()).add(new Text(")").setBold());

			if (formData.getPurpose() != null && !formData.getPurpose().isEmpty()) {
				justificationText.add(" to undergo " + formData.getPurpose());
			}

			if (formData.getPreLocDate() != null && !formData.getPreLocDate().isEmpty()) {
				justificationText.add(". In addition to the previous LOC, dated " + formData.getPreLocDate() + ".");
			}

			document.add(justificationText);

			Paragraph actionText = new Paragraph()
					.setFont(normalFont)
					.setFontSize(10)
					.setFirstLineIndent(40)
					.setTextAlignment(TextAlignment.JUSTIFIED) 
					.setPaddingLeft(20)
					.setPaddingRight(30)
					.add("Necessary action may please be taken for providing treatment to the individual and send the bill after completion of the treatment.");

			document.add(actionText);

			Paragraph bestWishes = new Paragraph("With best wishes,")
					.setFont(normalFont)
					.setFontSize(10)
					.setBold()
					.setTextAlignment(TextAlignment.CENTER) // Center align
					.setMarginTop(5); // Margin for spacing

			document.add(bestWishes);

			document.add(new Paragraph(" ").setMarginTop(5)); // Add spacing

			Paragraph yoursSincerely = new Paragraph("Yours sincerely,")
					.setFont(normalFont)
					.setFontSize(10)
					.setBold()
					.setPaddingRight(30)
					.setTextAlignment(TextAlignment.RIGHT); // Right align

			document.add(yoursSincerely);

			Div signatureDiv = new Div().setTextAlignment(TextAlignment.RIGHT);

			if ("Old".equals(formData.getLocType())) {
				signatureDiv.add(new Paragraph("(P. RAJASEKHAR REDDY)")
						.setFont(normalFont)
						.setFontSize(10)
						.setMarginTop(10)
						.setPaddingRight(30)
						.setFontColor(new DeviceRgb(0, 0, 255)) // Blue color
						.setBold());
			} else if ("New".equals(formData.getLocType())) {
				try {
					ImageData signImageData = ImageDataFactory.create(context_path + "/images/OSD-sign.png");
					Image signImage = new Image(signImageData).scaleToFit(150, 40).setHorizontalAlignment(HorizontalAlignment.RIGHT).setMarginRight(30);

					signatureDiv.add(signImage);

					signatureDiv.add(new Paragraph("(Vemula Srinivasulu)")
							.setFont(normalFont)
							.setFontSize(10)
							.setPaddingRight(30)
							.setFontColor(new DeviceRgb(0, 0, 255)) // Blue color
							.setBold());
				} catch (IOException e) {
					e.printStackTrace();
				}
			}

			Cell signatureCell = new Cell()
					.add(signatureDiv)
					.setBorder(Border.NO_BORDER)
					.setTextAlignment(TextAlignment.RIGHT); // Ensures everything stays right-aligned

			document.add(signatureCell);

			// Create the "Copy to:" label
				Paragraph copyTo = new Paragraph(new Text("Copy to:\n").setBold())
						.setFontSize(10)
						.setPaddingLeft(20);
				
				Paragraph vipDetails = new Paragraph();
				
			if(formData.getRecommendedBy().equals("999")) {
				if (formData.getCmcoReferredBy() != null && !formData.getCmcoReferredBy().isEmpty()) {
					vipDetails = new Paragraph()
							.setPaddingLeft(20)
							.add(new Text("CMCO - ("+formData.getCmcoReferredBy() + "),")
									.setFont(normalFont)
									.setFontSize(10)
									.setBold());		
							vipDetails.add("\n")
							.add(new Text("Copy to:\n").setBold().setFontSize(10));
				}
				
			}else {
				// Add VIP details conditionally
				if (formData.getVipName() != null && !formData.getVipName().isEmpty()) {
					vipDetails = new Paragraph()
							.setPaddingLeft(20)
							.add(new Text(formData.getPrefix() + " " + formData.getVipName() + ",")
									.setFont(normalFont)
									.setFontSize(10)
									.setBold());
							if (formData.getVipDesg() != null && !formData.getVipDesg().isEmpty()) {
								vipDetails.add("\n")
								.add(new Text(formData.getVipDesg())
										.setFont(normalFont)
										.setFontSize(10));
							}
							vipDetails.add("\n")
							.add(new Text("Copy to:\n").setBold().setFontSize(10));
				}
			}
				// Create paragraph for patient details
				Paragraph patientDetails = new Paragraph()
						.setPaddingLeft(20)
						.add(new Text(formData.getPatientName())
								.setFontSize(10)
								.setBold())
						.add("\n")  // Newline after patient name
						.add(new Text(formData.getFatherName())
								.setFont(normalFont)
								.setFontSize(10))
						.add("\n")  // Newline after father name
						.add(new Text(formData.getAddress())
								.setFont(normalFont)
								.setFontSize(10));

				// Create a table with two columns
				Table QRTABLE = new Table(2);
				QRTABLE.setWidth(UnitValue.createPercentValue(100)); // Set table width to 100%

				// Add "Copy to" content to the left column
				Cell copyToCell = new Cell()
						.add(copyTo)
						.add(vipDetails)
						.add(patientDetails)
						.setBorder(Border.NO_BORDER)
						.setTextAlignment(TextAlignment.LEFT);
				QRTABLE.addCell(copyToCell);

				// QR Code data
				String qrData = "{\"tokenNumber\" : \"" + formData.getLocNo().trim() + "\","
						+ "\"patientName\" : \"" + formData.getPatientName().trim() + "\","
						// + "\"PeopleRepresentative\" : \"" + formData.getVipName().trim() + ", " + formData.getVipDesg().trim() + "\","
						+ "\"Amount\" : \"" + formData.getAssuredAmt().trim() + "\"}";

				// Generate QR Code
				byte[] qrCodeImage = QRCodeUtil.generateQRCode(qrData, 100, 100);

				// Create the cell for the QR code (span across two columns)
				Cell qrCell = new Cell(1, 2);
				qrCell.setHorizontalAlignment(HorizontalAlignment.RIGHT);
				qrCell.setBorder(Border.NO_BORDER);

				if (qrCodeImage != null) {
					ImageData qrImageData = ImageDataFactory.create(qrCodeImage);
					Image qrImage = new Image(qrImageData);
					qrImage.scaleToFit(100, 100); // Scale the image if needed
					qrImage.setMarginRight(15);
					qrImage.setHorizontalAlignment(HorizontalAlignment.RIGHT);
					qrCell.add(qrImage);
				} else {
					// If QR code generation fails, display placeholder text
					qrCell.add(new Paragraph("QR Code Here").setFont(normalFont).setFontSize(10));
				}

				// Add QR code cell to the table
				QRTABLE.addCell(qrCell);

				document.add(QRTABLE);

			Table noteTable = new Table(2);
			noteTable.setWidth(UnitValue.createPercentValue(100));  

			// First column - Note label
			Cell noteLabelCell = new Cell()
					.add(new Paragraph("Note:-").setFont(normalFont).setFontSize(10))
					.setBorder(Border.NO_BORDER)
					.setVerticalAlignment(VerticalAlignment.TOP)
					.setPaddingLeft(20);
			noteTable.addCell(noteLabelCell);

			// Second column - Text content
			Cell noteContentCell = new Cell()
					.add(new Paragraph("This letter does not require physical signature and this certificate can be verified by scanning QR code printed on this letter.")
							.setFont(normalFont)
							.setFontSize(10)
							.setTextAlignment(TextAlignment.JUSTIFIED))
					.setBorder(Border.NO_BORDER)
					.setPaddingRight(50);
			noteTable.addCell(noteContentCell);

			// Add the table to the document
			document.add(noteTable);

            // Close the document
            document.close();

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}

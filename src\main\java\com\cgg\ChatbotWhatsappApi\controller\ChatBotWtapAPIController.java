package com.cgg.ChatbotWhatsappApi.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.cgg.ChatbotWhatsappApi.dao.ChatbotWtapApiDaoImpl;
import com.cgg.ChatbotWhatsappApi.model.ApiDTO;
import com.cgg.ChatbotWhatsappApi.model.ApiDetails;

@RestController
@RequestMapping(path = "cmrfapi")
public class ChatBotWtapAPIController {

	@Autowired
	ChatbotWtapApiDaoImpl chatbotWtapApiDaoImpl;

    @GetMapping(path = "/locStatus/{phoneNo}", produces = "application/json")
	@ResponseStatus(HttpStatus.OK)
	public ApiDTO getLocsOfPhone(
        //@RequestHeader(name = "wstoken", required = false) String wstoken, 
        @PathVariable String phoneNo,HttpServletRequest request) {
		Boolean isLogExecuted=false;
		String ipAddress = request.getRemoteAddr(); 
		ApiDTO dto = new ApiDTO();
		dto.setPhoneNo(phoneNo);

		// Boolean isProdWithWSTokenOrDefault = false;

		// if (environment.equalsIgnoreCase("prod")) {
		// 	if (wstoken != null && wstoken.equals(dostWstoken)) {
		// 		// Satisfied with WSToken Check in Production
		// 		isProdWithWSTokenOrDefault = true;
		// 	} else {
		// 		dto.setMessage("Header attribute 'wstoken' is NOT PROVIDED for PRODUCTION Environment !!!");
		// 	}

		// } else {
		// 	isProdWithWSTokenOrDefault = true;
		// }
		
		// check if phone no is valid one.
		boolean isphvalid = isValidMobileNo(phoneNo);
		if(!isphvalid){
			dto.setMessage("Mobile Number Provided is INVALID !!!");
		}

		if (
            //isProdWithWSTokenOrDefault && 
        isphvalid) {
			
			List<ApiDetails> dataRows = new ArrayList<ApiDetails>();
			boolean daoException = false;
			try{
				dataRows = (chatbotWtapApiDaoImpl.getLOCApplications(phoneNo));
				System.out.println(dataRows);
			}catch(Exception ex){
				daoException = true;
			}
			
			
			if (dataRows != null && !dataRows.isEmpty()) {
				
				 ArrayList<String> locNos = new  ArrayList<String>();
				 ArrayList<String> messages = new  ArrayList<String>();
				 
				for(ApiDetails dataRow : dataRows){
					
					String locNo = dataRow.getLocNo();
					String name = dataRow.getApplicantName();
					
					locNos.add(locNo);
					messages.add(locNo + " - " + name);

				}
				
				
				dto.setLocNos(locNos);
				
				dto.setPhoneNo(phoneNo);
				
				String messageStr = "";
				for (String msg : messages) {
					messageStr += msg;
					messageStr += ", ";
				}
				messageStr = removeLastCharOptional(messageStr);
				
				dto.setMessage(messageStr);
				
			} else if(daoException){
				dto.setMessage("DAO Exception Occurred for request of : " + phoneNo);
			} else {
			dto.setMessage("NO LOC's are associated with Mobile: "+ phoneNo);
			}
			try {
				isLogExecuted=chatbotWtapApiDaoImpl.insertLogs(phoneNo, ipAddress, "getLOCsOfPhoneNo", dto.toString().replace("'", ""),null);
				if(!isLogExecuted) {
					dto.setLocNos(new ArrayList<String>());
					dto.setMessage("please try again! ");
				}
			} catch (Exception e) {
				dto.setMessage("DAO Exception Occurred for request of : " + phoneNo);
				e.printStackTrace();
			}
			
		}

		return dto;
	}

    @GetMapping(path = "/cmrfStatus/{phoneNo}", produces = "application/json")
	@ResponseStatus(HttpStatus.OK)
	public ApiDTO getCMRFStatusOfPhone(@PathVariable String phoneNo,HttpServletRequest request) {
		Boolean isLogExecuted=false;
		String ipAddress = request.getRemoteAddr(); 
		ApiDTO dto = new ApiDTO();
		dto.setPhoneNo(phoneNo);

		boolean isphvalid = isValidMobileNo(phoneNo);
		if(!isphvalid){
			dto.setMessage("Mobile Number Provided is INVALID !!!");
		}

		if (isphvalid) {
			
			List<ApiDetails> dataRows = new ArrayList<ApiDetails>();
			boolean daoException = false;
			try{
				dataRows = (chatbotWtapApiDaoImpl.getCMRFApplications(phoneNo));
				System.out.println(dataRows);
			}catch(Exception ex){
				daoException = true;
			}
			
			if (dataRows != null && !dataRows.isEmpty()) {
				
				 ArrayList<String> cmrfTokenNos = new  ArrayList<String>();
				 ArrayList<String> messages = new  ArrayList<String>();
				 
				for(ApiDetails dataRow : dataRows){
					
					String cmrfTokenNo = dataRow.getTokenNo();
					String status = dataRow.getStatus();
					
					cmrfTokenNos.add(cmrfTokenNo);
					messages.add("Status For " + cmrfTokenNo + " is " + status);

				}
				
				dto.setTokenNos(cmrfTokenNos);
				
				dto.setPhoneNo(phoneNo);
				
				String messageStr = "";
				for (String msg : messages) {
					messageStr += msg;
					messageStr += ", ";
				}
				messageStr = removeLastCharOptional(messageStr);
				
				dto.setMessage(messageStr);
				
			} else if(daoException){
				dto.setMessage("DAO Exception Occurred for request of : " + phoneNo);
			} else {
			dto.setMessage("NO CMRF Applications are registered with Mobile: "+ phoneNo);
			}
			try {
				isLogExecuted=chatbotWtapApiDaoImpl.insertLogs(phoneNo, ipAddress, "getCMRFStatusOfPhoneNo", dto.toString().replace("'", ""),null);
				if(!isLogExecuted) {
					dto.setLocNos(new ArrayList<String>());
					dto.setMessage("please try again! ");
				}
			} catch (Exception e) {
				dto.setMessage("DAO Exception Occurred for request of : " + phoneNo);
				e.printStackTrace();
			}
			
		}

		return dto;
	}

    @GetMapping(path = "/tokenNo", produces = "application/json")
	@ResponseStatus(HttpStatus.OK)
	public ApiDTO getCMRFStatusOfTokenNo(@RequestParam("TCMRF") String token,HttpServletRequest request) {
		Boolean isLogExecuted=false;
		String ipAddress = request.getRemoteAddr(); 
		ApiDTO dto = new ApiDTO();
		String tokenNumber = token.toUpperCase();
		dto.setTokenNumber(tokenNumber);

		boolean isValidToken = isValidToken(tokenNumber);
		if(!isValidToken){
			dto.setMessage("Provided Token Number is INVALID !!!");
		}

		if (isValidToken) {
			
			List<ApiDetails> dataRows = new ArrayList<ApiDetails>();
			boolean daoException = false;
			try{
				dataRows = (chatbotWtapApiDaoImpl.getApplicationByTokenNo(tokenNumber));
				System.out.println(dataRows);
			}catch(Exception ex){
				daoException = true;
			}
			
			if (dataRows != null && !dataRows.isEmpty()) {
				
				 ArrayList<String> cmrfTokenNos = new  ArrayList<String>();
				 ArrayList<String> messages = new  ArrayList<String>();
				 String phoneNo = null;
				 
				for(ApiDetails dataRow : dataRows){
					
					String tokenNum = dataRow.getTokenNo();
					String status = dataRow.getStatus();
					phoneNo = dataRow.getPhoneNo();
					
					cmrfTokenNos.add(tokenNum);
					messages.add("Status For " + tokenNum + " is " + status);

				}
				
				dto.setTokenNos(cmrfTokenNos);
				
				dto.setPhoneNo(phoneNo);
				
				String messageStr = "";
				for (String msg : messages) {
					messageStr += msg;
					messageStr += ", ";
				}
				messageStr = removeLastCharOptional(messageStr);
				
				dto.setMessage(messageStr);
				
			} else if(daoException){
				dto.setMessage("DAO Exception Occurred for request of : " + tokenNumber);
			} else {
			dto.setMessage("NO Applications are registered with Token: "+ tokenNumber);
			}
			try {
				isLogExecuted=chatbotWtapApiDaoImpl.insertLogs(dto.getPhoneNo(), ipAddress, "getStatusOfTokenNo", dto.toString().replace("'", ""),tokenNumber);
				if(!isLogExecuted) {
					dto.setLocNos(new ArrayList<String>());
					dto.setMessage("please try again! ");
				}
			} catch (Exception e) {
				dto.setMessage("DAO Exception Occurred for request of Token: " + tokenNumber);
				e.printStackTrace();
			}
			
		}

		return dto;
	}

    boolean isValidMobileNo(String mobileNo){
		if(mobileNo==null || StringUtils.isEmpty(mobileNo) || mobileNo.trim().length()!=10){
			return false;
		}else {
			return true;
		}
	}

    boolean isValidToken(String tokenNo){
		if(tokenNo==null || StringUtils.isEmpty(tokenNo) || tokenNo.trim().length()<11 || tokenNo.trim().length()>20){
			return false;
		}else {
			return true;
		}
	}
	
	public static String removeLastCharOptional(String s) {
		return Optional.ofNullable(s).filter(str -> str.length() != 0)
				// Remove ", " //2Char
				.map(str -> str.substring(0, str.length() - 2)).orElse(s);
	}

	@PostMapping(value = "/callBackResponse.cgg", 
             consumes = MediaType.APPLICATION_JSON_VALUE, 
             produces = MediaType.APPLICATION_JSON_VALUE)
	public ResponseEntity<String> chatBotCallBackResponse(@RequestBody Map<String, Object> payload) {
		System.out.println("PAYLOAD: " + payload);

		// Extract values
        String wtapId = (String) ((Map<String, Object>) ((List<?>) payload.get("contacts")).get(0)).get("wtap_id");
        Map<String, Object> message = (Map<String, Object>) ((List<?>) payload.get("messages")).get(0);
        Map<String, Object> context = (Map<String, Object>) message.get("context");
        Map<String, Object> interactive = (Map<String, Object>) message.get("interactive");
        Map<String, Object> listReply = (Map<String, Object>) interactive.get("list_reply");

        String fromInContext = (String) context.get("from");
        String fromInMessages = (String) message.get("from");
        String interactiveType = (String) interactive.get("type");
        String description = (String) listReply.get("description");
        String id = (String) listReply.get("id");
        String title = (String) listReply.get("title");

        // Print the extracted values
        System.out.println("wtap_id: " + wtapId);
        System.out.println("from in context: " + fromInContext);
        System.out.println("from in messages: " + fromInMessages);
        System.out.println("interactive type: " + interactiveType);
        System.out.println("list_reply description: " + description);
        System.out.println("list_reply id: " + id);
        System.out.println("list_reply title: " + title);

		// Check if the payload is empty and set the appropriate HTTP status
		HttpStatus status = payload != null && !payload.isEmpty() ? HttpStatus.OK : HttpStatus.BAD_REQUEST;

		// Return the response
		return new ResponseEntity<>("Callback received successfully", status);
	}

}

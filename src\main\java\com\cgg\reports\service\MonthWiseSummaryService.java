package com.cgg.reports.service;

import java.util.List;

import com.cgg.reports.model.MonthWiseSummary;

public interface MonthWiseSummaryService 
{
	List<MonthWiseSummary> getMonthWiseSummary() throws Exception;
	
	List<MonthWiseSummary> getMonthWiseSanAmt() throws Exception;
	
	List<MonthWiseSummary> getMonthYearPRLocCount(String monthYear) throws Exception;
	List<MonthWiseSummary> getMonthYearPRLocDetails(String monthYear,String recommedeBy) throws Exception;
	
	List<MonthWiseSummary> getMonthYearPRCmrfCount(String monthYear) throws Exception;
	List<MonthWiseSummary> getMonthYearPRCmrfDetails(String monthYear,String recommedeBy,String otherCons) throws Exception;
	
	List<MonthWiseSummary> getMonthYearPRChqRevalidationsCount(String monthYear) throws Exception;
	List<MonthWiseSummary> getMonthYearPRChqRevalidationDetails(String monthYear,String recommedeBy,String otherCons) throws Exception;
	
	List<MonthWiseSummary> getMonthYearPRChqPaymentCount(String monthYear) throws Exception;
	List<MonthWiseSummary> getMonthYearPRChqPaymentDetails(String monthYear,String recommedeBy,String otherCons) throws Exception;
	
	List<MonthWiseSummary> getMonthYearPRCmrfAckDetails(String monthYear,String recommedeBy,String otherCons, Boolean scan) throws Exception;

	List<MonthWiseSummary> getMonthYearPRLocHospPaidCount(String monthYear) throws Exception;

	List<MonthWiseSummary> getMonthYearPRAckScanCount(String monthYear) throws Exception;
}

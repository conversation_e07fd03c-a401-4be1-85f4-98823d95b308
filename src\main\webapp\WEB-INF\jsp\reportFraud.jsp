<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="en">
  <head>
    <!-- Meta Tags and Title -->
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Report Fraud or Scam</title>
    <link rel="icon" href="images/telangana.png">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="fonts/Regular/NotoSans-Regular.ttf" type="text/css">
    <link rel="stylesheet" href="css/bootstrap5.3.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="css/font-awesome.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Ramabhadra&display=swap" rel="stylesheet">
    <script src="js/common/commonValidations.js"></script>

    <!-- Custom Styles -->
    <style>
        .error {
			color: red !important;
			font-size: 0.875em !important;
			margin-top: 0.25rem !important;
			display: block !important;
		}
		.is-invalid {
			border-color: #dc3545 !important;
		}
		.is-valid {
			border-color: #28a745 !important;
		}
		.invalid-feedback {
			display: none !important;
			color: #dc3545 !important;
		}
		.valid-feedback {
			display: none !important;
			color: #28a745 !important;
		}
		.is-invalid + .invalid-feedback {
			display: block !important;
		}
		.is-valid + .valid-feedback {
			display: block !important;
		}

      /* General Styles */
      .vjs-control-bar {
        font-size: 16px;
      }

      .header-image {
        background: linear-gradient(to bottom, rgba(255, 200, 100, 0.5) 0%, rgba(255, 255, 255, 0.7) 40%, rgba(255, 255, 255, 0.7) 60%, rgba(100, 200, 100, 0.5) 100%);
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .cmbox {
        float: right;
        display: flex;
        align-items: center;
        padding: 0px 10px;
      }

      .cmbox img {
        margin-right: 10px;
        margin-top: 0;
        height: 77px;
        border: 2px solid #795548;
      }

      .cmbox h3 {
        font-size: 12px;
        font-family: 'Roboto Condensed', sans-serif;
        color: #111;
        margin-bottom: 10px;
        padding-top: 0;
        margin-top: 0;
      }

      /* Footer Styles */
      .footer {
        position: relative;
        background-color: #333; 
        color: rgba(255, 255, 255, 0.8);
        overflow: hidden;
      }

      .footer::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.4); /* Black overlay with opacity */
        z-index: 1;
      }

      .footer .row {
        margin: 0 auto;
        position: relative; /* Ensure content is above the background */
        z-index: 2; /* Ensure content is above the pseudo-element overlay */
      }

      .footer p {
        margin: 0;
        font-size: 13px;
        line-height: 1.5; /* Improve readability */
        z-index: 2; /* Ensure text is above the overlay */
      }

      .footer img {
        vertical-align: middle; /* Align the image with text */
        z-index: 2; /* Ensure image is above the overlay */
      }

      @media (max-width: 768px) {
        .footer .row {
          flex-direction: column; /* Stack columns on smaller screens */
        }

        .footer p {
          text-align: center; /* Center text on smaller screens */
          float: none; /* Remove float for smaller screens */
          margin-bottom: 10px; /* Add margin for spacing */
        }
      }

        .form {
            background: linear-gradient(to bottom right, #d7e7f5, #eef2fb, #c3d4e8);
            border: 1px solid #ccc;
            padding: 20px;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
        }

        h3 {
            color: #563d7c;
        }

        .form-row {
            margin-top: 20px;
        }

        .form-group {
            margin-top: 20px;
            display: flex;
            flex-direction: column;
        }

        label {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .required {
            color: red;
        }

        .button {
            padding: 8px 16px;
            background-color: #2e4a7f;
            color: white;
            border: none;
            border-radius: 3px;
            margin-top: 20px;
            cursor: pointer;
        }

        .button:hover {
            background-color: #1e355a;
        }

        .inp{
            min-height: 35px;
            border-radius: 3px;
        }
    </style>

    <!-- Clock Script -->
    <script>
      setInterval(function () {
        var now = new Date();
        var day = String(now.getDate()).padStart(2, "0");
        var month = String(now.getMonth() + 1).padStart(2, "0");
        var year = now.getFullYear();
        var hours = String(now.getHours()).padStart(2, "0");
        var minutes = String(now.getMinutes()).padStart(2, "0");
        var seconds = String(now.getSeconds()).padStart(2, "0");
        var dateTime =
          day +
          "." +
          month +
          "." +
          year +
          " " +
          hours +
          ":" +
          minutes +
          ":" +
          seconds;
        document.getElementById("datetime").textContent = dateTime;
      }, 1000);
    </script>
  </head>

  <body>
    <!-- Header Section -->
    <div class="header">
      <div class="row w-100 m-0" style="background: #e9e8e8;">
        <div class="col-md-2 ps-5">
          <b id="datetime"></b>
        </div>
        <div class="col-md-4">
          <b>
            &#3108;&#3142;&#3122;&#3074;&#3095;&#3134;&#3107;
            &#3114;&#3149;&#3120;&#3117;&#3137;&#3108;&#3149;&#3125;&#3074; ||
            Government of Telangana
          </b>
        </div>
        <div class="col-md-6 text-end px-5" id="sizer">
          <div class="btn-group">
            <a
              class="increase btn btn-secondary btn-sm accessibility"
              id="btn-increase"
              href="javascript:void(0);"
            >
              A+
            </a>
            &nbsp;
            <a
              class="reset btn btn-secondary btn-sm accessibility"
              href="javascript:void(0);"
              id="btn-orig"
            >
              A
            </a>
            &nbsp;
            <a
              class="decrease btn btn-secondary btn-sm accessibility"
              href="javascript:void(0);"
              id="btn-decrease"
            >
              A-
            </a>
          </div>
        </div>
      </div>
      <div class="w-100 container-fluid">
        <div class="row header-image">
          <div class="col-md-8">
            <div class="logo ms-3 my-1 d-flex" style="align-items: center;">
              <img
                src="newtemplate/img/telanganalogo.png"
                alt="telangana logo"
                style="width: 120px; height: 120px"
              />
              <div class="heading text-uppercase">
                <h2 style="color: #d06700" class="ms-3 mt-1 mb-0">
                  Chief Minister's Relief Fund <br />
                </h2>
                <span style="font-size: 24px;color:#2C7865;" class="ms-3">
                  Government of Telangana
                </span>
              </div>
            </div>
          </div>
          <div class="col-md-4 d-flex align-items-center justify-content-end">
            <div class="cmbox text-center">
              <img src="images/CmImageNOH.jpg" alt="Chief Minister" style="width: 110px; height: auto;" />
              <h3 style="color: #701443;font-size: 18px;">
                <b>Sri Anumula Revanth Reddy</b><br>Hon'ble Chief Minister
              </h3>
              <img src="images/rising.jpg" alt="Telangana Rising" style="height: 100px; margin-left: 10px;" />
            </div>
          </div>
        </div>  
      </div>
      <div class="row w-100 m-0" style="background: #e9e8e8;">
        <div class="col-md-8 offset-md-2 text-center my-1">
          <!-- <marquee id="marquee" onmouseover="pauseMarquee()" onmouseleave="resumeMarquee()"> -->
            <b style="font-size: 18px;color: red">
              CMRF &#3078;&#3120;&#3149;&#3111;&#3135;&#3093; &#3128;&#3134;&#3119;&#3074; &#3112;&#3135;&#3120;&#3137; &#3114;&#3143;&#3110;&#3122; &#3093;&#3147;&#3128;&#3074; &#45; &#3077;&#3074;&#3110;&#3137;&#3122;&#3147; &#3077;&#3125;&#3135;&#3112;&#3136;&#3108;&#3135; &#3112;&#3143;&#3120;&#3118;&#3143; &#3093;&#3134;&#3110;&#3137;&#44; &#3114;&#3134;&#3114;&#3074; &#3093;&#3138;&#3105;
            </b>
          <!-- </marquee> -->
        </div>
      </div>
      <%@ include file="homenav.jsp" %>
    </div>
    <!-- End of Header Section -->

    <!-- Content Section -->
    <div class="content min-vh-100">
        <div class="container mt-4" style="width: 30%;">
            <c:if test="${not empty success}">
                <div class="alert alert-success">
                    <p style="color:green;font-size: 15px;font-weight: bold;margin-bottom: 0px;">${success}</p>
                </div>
            </c:if>
			<c:if test="${not empty error}">
                <div class="alert alert-danger">
                    <p style="color:red;font-size: 15px;font-weight: bold;margin-bottom: 0px;">${error}</p>
                </div>
			</c:if>
            <form action="reportFraud" method="post" class="form" id="reportFraudForm">
                <div class="d-flex justify-content-between align-items-center">
                    <img src="<%=basePath%>images/fraud.png" alt="no image" style="width: 80px;">
                    <h3 class="mb-0"><i>Report Fraud</i></h3>
                    <img src="<%=basePath%>images/lensnobg.png" alt="no image" style="width: 80px;">
                </div>
            
                <hr>
                <div class="form-row">
                    <div class="form-group col-sm-12">
                        <label>Name: <span class="required">*</span></label>
                        <input type="text" id="name" name="name" maxlength="50" onkeyup="nameField(this); return uppercaseWithSymbols(this);" onchange="trimSpace(this);" required class="inp">
                    </div>
                    <div class="form-group col-sm-12">
                        <label>Mobile Number: <span class="required">*</span></label>
                        <input type="text" id="mobileNo" name="mobileNo" onkeyup="intNumOnly(this);" onchange="chkvalidNo(this)" minlength="10" maxlength="10" required class="inp">
                    </div>
                    <div class="form-group col-sm-12">
                        <label>Fraud details: <span class="required">*</span></label>
                        <textarea name="fraudDetails" id="fraudDetails" maxlength="500" minlength="10" onkeyup="return uppercaseWithSymbols(this);" onchange="trimSpace(this);" required style="min-height: 200px;" class="inp"></textarea>
                    </div> 
                    <div class="text-center">
                        <button type="submit" class="button">Submit</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- End of Content Section -->

    <!-- Footer Section -->
    <div class="footer">
      <div class="row p-2 mx-5" style="overflow: hidden;opacity: 0.8;">
        <p class="col-sm-6 text-white mt-2" style="float: left; margin-bottom: 5px; font-size: 13px;">
          Copyright
          &#0169;<%=java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)
          %>,Chief Minister's Relief Fund. <br />
          All Rights Reserved
        </p>
        <p class="col-sm-6 text-center" style="float: right; margin-bottom: 5px; font-size: 13px;">
          Designed & Developed by :
          <img src="newtemplate/img/cgg_logo.png" alt="cgg logo" />, Hyderabad
          <br />Content owned, maintained and updated by Chief Minister's Relief
          Fund, Govt. of Telangana
        </p>
      </div>
    </div>
    <!-- End of Footer Section -->

    <script src="<%=basePath%>js/validate/jquery.validate.min.js"></script>
    <script src="<%=basePath %>js/sweetalert2/sweetalert2.js"></script>
    <script>
        $(document).ready(function (){
            // Custom rule for file extension

            $('#reportFraudForm').validate({
                rules: {
                    name: {
                        required: true
                    },
                    mobileNo: {
                        required: true,
                        minlength: 10,
                        maxlength: 10,
                        digits: true
                    },
                    fraudDetails: {
                        required: true,
                        minlength: 10,
                        maxlength: 500
                    }
                },
                messages: {
                    name: {
                        required: 'Please enter Name'
                    },
                    mobileNo: {
                        required: 'Please enter Mobile Number',
                        minlength: 'Mobile Number must be 10 digits',
                        maxlength: 'Mobile Number must be 10 digits'
                    },
                    fraudDetails: {
                        required: 'Please enter Fraud/Scam Details',
                        minlength: 'Enter more than 10 Characters.',
                        maxlength: 'Max limit(500) reached.'
                    }
                },
                errorElement: "span",
                errorClass: "invalid-feedback",
                highlight: function (element, errorClass, validClass) {
                    $(element).addClass("is-invalid").removeClass("is-valid");
                },
                unhighlight: function (element, errorClass, validClass) {
                    $(element).removeClass("is-invalid").addClass("is-valid");
                },
                submitHandler: function (form) {
                    Swal.fire({
                        title: 'Do you want to submit the details now?',
                        text: "",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6', 
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes, submit it!',
                        cancelButtonText: 'No, cancel!'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            form.submit();
                        }else{
                            return false;
                        }
                    });
                },
            });
        });
    </script>
  </body>
</html>

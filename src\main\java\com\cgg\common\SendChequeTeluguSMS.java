package com.cgg.gov.in;

 
import java.net.HttpURLConnection;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

import com.cgg.common.BSNLSMSHttpPostClient;
 
public class SendChequeTeluguSMS {
	 private static final String JDBC_URL = "****************************************";
     private static final String USERNAME = "readonly";
     private static final String PASSWORD = "readonly";
     public static void sendSmsNotifications(String cmrfNo,String mobileNo,String chequeNo) throws Exception {
        String templateIdTel = "1407173470386775360";
        String templateNameTel = "TGCMRF_211224_2";
               
        String msg;
      
        String connection = null;
       
        try {
            // Construct the message
        	msg = "సార్/మేడమ్, మీ CMRF అప్లికేషన్ "+cmrfNo+" కు సంబంధించిన  చెక్కు (చెక్కు నంబర్ "+chequeNo+" )  మీ  ప్రజా ప్రతినిధి కార్యాలయానికి  పంపబడింది. దయచేసి మీ ప్రజా ప్రతినిధి కార్యాలయాన్ని 7  రోజుల తరువాత సంప్రదించండి.TGCMRF";
            
            // Send the SMS
            connection = BSNLSMSHttpPostClient.sendBSNLUnicodeSms(msg, "91" + mobileNo, templateIdTel, templateNameTel);
           
            // Check if the response code is successful
            if (connection != null && "200".equals(connection)) {
                System.out.println("SMSsent successfully.");
            } else {
                System.out.println("SMS Failed."+connection);
            }
           
        } catch (Exception e) {
            e.printStackTrace(); // Handle the exception with logging or a notification
        } finally {
            
        }
    }
 
    public static void main(String[] args) throws Exception {
    	 Class.forName("org.postgresql.Driver");

         // Establishing connection
      Connection    conn = DriverManager.getConnection(JDBC_URL, USERNAME, PASSWORD);
      PreparedStatement pstmt=null;
      ResultSet rs=null;
      String cmrfNo=null;
      String mobileNo=null;
      String chequeNo=null;
      String sql="select b.cmrf_no cmrf_no,b.mobile_no mobile_no,r.cheque_no  cheque_no from mla_cmrf a\r\n" + 
       		"inner join cmrelief b on a.cmrf_no=b.cmrf_no\r\n" + 
       		"inner join rev_san r on b.cmrf_no=r.cmrf_no\r\n" + 
       		"where b.cmrf_no not in('218977/CMRF/2025')  "+
      		"and b.cmp_no in('234001-235000/CMRF/2025') and esigned_date::date=to_date('06/05/2025','dd/mm/yyyy') ";
      
         System.out.println("Reg No"+sql);
         pstmt = conn.prepareStatement(sql);
         rs = pstmt.executeQuery();
         while (rs.next()) {
        	 cmrfNo=rs.getString("cmrf_no");
        	 mobileNo=rs.getString("mobile_no");
        	 chequeNo=rs.getString("cheque_no");
        //	 mobileNo="9390359959";
        sendSmsNotifications(cmrfNo,mobileNo,chequeNo);
         }
    }
}
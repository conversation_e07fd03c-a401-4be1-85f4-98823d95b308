package com.cgg.dataentry.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.cgg.common.CommonFunctions;
import com.cgg.common.Response;
import com.cgg.common.UIDVerfication;
import com.cgg.dataentry.entities.Ifsccodes;
import com.cgg.dataentry.service.AddIfscService;
import com.cgg.hospital.model.InwardCmrf;

@Controller
public class AddIfscController {
	
	@Autowired
	AddIfscService addIfscService;
	
	@GetMapping("/addIfsc")
	public String addIfsc(HttpServletRequest request,Model model) {
		List<String> roles = Arrays.asList("24","25");
		HttpSession session = request.getSession();
		String role = (String) session.getAttribute("rolesStr");
		String userId = (String) session.getAttribute("userid");
		if(role==null && ! roles.contains(role) && userId==null) {
			 return  "redirect:/";
		}
		List districts = addIfscService.getDistricts();
		List banks = addIfscService.getBanks();
		model.addAttribute("districts", districts);
		model.addAttribute("banks", banks);
		System.out.println(districts);
		System.out.println(banks);
		return "addIfsc";
	}
	@PostMapping("/getIfsc")
	public ResponseEntity<Ifsccodes> getIfsc(@RequestParam("ifscCode") String ifscCode) {
		
		System.out.println(ifscCode);
		Optional<Ifsccodes> bankDetails = addIfscService.getBankDetails(ifscCode);
		if(bankDetails.isPresent()) {
			
			return ResponseEntity.ok(bankDetails.get());
			
		}
		return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(null);
		
	}
	@PostMapping("/addIfscCode")
	public String addIfscCode(@ModelAttribute("inwardCmrf") InwardCmrf inwardCmrf,Model model,RedirectAttributes redirect) {
		try {
		List<String> errors = new ArrayList<String>();
		errors = checkErrors(errors, inwardCmrf);
		
		if (errors.size() > 0) {
			redirect.addFlashAttribute("errors", errors);
		}
		System.out.println(inwardCmrf);
		Ifsccodes saveIfscCode = addIfscService.saveIfscCode(inwardCmrf);
		if(saveIfscCode != null) {
			redirect.addFlashAttribute("success", "Successfully added IFSC Code");
		}else {
			redirect.addFlashAttribute("error", "Unable to IFSC Code");
		}
		
		}catch (Exception e) {
			e.printStackTrace();
			redirect.addFlashAttribute("error", "Something Went Wrong");
		}
		
		return "redirect:/addIfsc";
		
	}
public List<String> checkErrors(List<String> errors, InwardCmrf inwardCmrf) throws Exception {
		
		if(!CommonFunctions.validateSelectBox(inwardCmrf.getBankDistrict())) {
			errors.add("Select Bank District");
		}
		if(!CommonFunctions.validateSelectBox(inwardCmrf.getBankName())) {
			errors.add("Select Bank Name");
		}
		if(!CommonFunctions.validateData(inwardCmrf.getBankIfsc())) {
			errors.add("Select Bank IFSC");
		}
		if(!CommonFunctions.validateData(inwardCmrf.getBankBranch())) {
			errors.add("Enter Bank Branch");
		}
		return errors;
		

}}

package com.cgg.dataentry.service;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.dataentry.dao.EditLOCMLACMRFEntryDao;
import com.cgg.dataentry.model.EditLOCMLACMRFEntryModel;

@Service
public class EditLOCMLACMRFEntryServiceImpl implements EditLOCMLACMRFEntryService {
	
	@Autowired
	EditLOCMLACMRFEntryDao Dao;

	@Override
	public List<EditLOCMLACMRFEntryModel> getRecommendedDetails() throws Exception {
		return Dao.getRecommendedDetails();
	}

	@Override
	public List<EditLOCMLACMRFEntryModel> getHospitalDetails() throws Exception {
		return Dao.getHospitalDetails();
	}


	@Override
	public List<EditLOCMLACMRFEntryModel> getLocData(String loc_token) throws Exception {
		return Dao.getLocData(loc_token);
	}

	@Override
	public boolean updateLocDetails(EditLOCMLACMRFEntryModel locEntryForm,HttpServletRequest request) {
		return Dao.updateLocDetails(locEntryForm,request);
	}

	@Override
	public List<EditLOCMLACMRFEntryModel> getDistricts()throws Exception{
		return Dao.getDistricts();
	}
}
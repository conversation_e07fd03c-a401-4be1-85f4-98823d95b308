package com.cgg.dataentry.dao;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.cgg.dataentry.model.UpdateCmrfEntryForm;

public interface UpdateCmrfEntryDao {
	public List<UpdateCmrfEntryForm> getRecommendedDetails() throws Exception;
	public List<UpdateCmrfEntryForm> getHospitalList() throws Exception;
	public List<UpdateCmrfEntryForm> getDistricts() throws Exception;
	
	public List<UpdateCmrfEntryForm> getMandals(String distCode) throws Exception;
	public String getInwardData(String mlaCmrfNo)throws Exception;
	public String getTokenBankData(String mlaCmrfNo)throws Exception;
	public String getCmrfData(String cmrfVal)throws Exception;
	public int  updateCmrfDetails(UpdateCmrfEntryForm updateCmrfForm, Map<String, Object> model,HttpServletRequest request);
	public String getCmrfLocData(String cmrfLocNo)throws Exception;
	public UpdateCmrfEntryForm getCmrfValues(String cmrfNo)throws Exception;
	public List<UpdateCmrfEntryForm> getOtherConsList() throws Exception;


}


package com.cgg.dataentry.dao;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;

import com.cgg.dataentry.model.EnhancementLocDetails;

public interface EnhancementLocDetailsEntryDao {
	public List<EnhancementLocDetails> getRecommendedDetails() throws Exception;
	public List<EnhancementLocDetails> getHospitalList() throws Exception;
	public String saveLocDetails(EnhancementLocDetails locEntryForm, Map<String, Object> model,HttpServletRequest request) throws Exception;
	public String getLocData(String locTokenNo)throws Exception;
	public EnhancementLocDetails getLocLetterData(String locNo,String prevLocNo) throws Exception;
	public String getPreviousLocAmount(String prevLocNo)throws Exception;	

}

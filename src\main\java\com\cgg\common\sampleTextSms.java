package com.cgg.common;

public class sampleTextSms {

    public static void sendSmsNotifications() throws Exception {
        String templateIdTel = "1407174012279696332";
		String templateNameTel = "CGGHYD_21feb25";
				
        String msg;
        String mobileNumber = "9493785014";
        String tokenNo = "123456/TCMRF/2024";
        String hashToken = "123456";
        // HttpURLConnection connection = null;
        String connection = null;
        
        try {
            // Construct the message
            msg = "123456 is Your One Time Password (OTP) for TGIIC login and is valid for <#var#> minutes. Please do not share it with anyone. -TGIIC";
            // Send the SMS
            connection = BSNLSMSHttpPostClient.sendBSNLSms(msg, "91" + mobileNumber, templateIdTel, templateNameTel);
            
            // Check if the response code is successful
            if (connection != null && "200".equals(connection)) {
                System.out.println("SMS sent successfully.");
            } else {
                // System.out.println("Failed to send SMS. Response code: " + (connection != null ? connection.getResponseCode() : "No response"));
                System.out.println("SMS sent successfully.");
            }
            
        } catch (Exception e) {
            e.printStackTrace(); // Handle the exception with logging or a notification
        } finally {
            // Ensure the connection is closed to avoid resource leaks
            // if (connection != null) {
            //     connection.disconnect();
            // }
        }
    }

    public static void main(String[] args) throws Exception {
        sendSmsNotifications();
    }
}
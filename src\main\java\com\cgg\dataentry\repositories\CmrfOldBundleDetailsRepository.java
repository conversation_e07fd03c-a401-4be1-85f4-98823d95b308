package com.cgg.dataentry.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.cgg.dataentry.entities.CmrfOldBundleDetails;

@Repository
public interface CmrfOldBundleDetailsRepository extends JpaRepository<CmrfOldBundleDetails, Integer> {

	CmrfOldBundleDetails findTopByOrderByIdDesc();
    
    @Query(value="select property_value from cmrf_properties where property_name = :propertyName",nativeQuery = true)
    String cmrfPropertyValueByName(@Param("propertyName") String propertyName);
}
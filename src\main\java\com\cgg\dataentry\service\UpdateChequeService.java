package com.cgg.dataentry.service;

import java.util.List;
import java.util.Map;


import javax.servlet.http.HttpServletRequest;

import com.cgg.common.Response;
import com.cgg.dataentry.model.UpdateChequeForm;

public interface UpdateChequeService {
	
	 public Map<String, Object> getTodaysCheckHistory(HttpServletRequest request);
	
	public Response getDetails(UpdateChequeForm request);

	public boolean updateChequeDetails(UpdateChequeForm updateChequeForm,HttpServletRequest request);

	public boolean editChequeDetails(UpdateChequeForm updateChequeForm,HttpServletRequest request);

	public Response getChequeDetailsFromTo(UpdateChequeForm request);

	public Response getUpdChqDtlsForPrint(UpdateChequeForm request, boolean cmrfFlag, boolean dateFlag);

	public List<UpdateChequeForm> generateBeneficiaryLetters(String cmrfNo,String newChequeNo)	throws Exception;
	
	public Map<String, Object> updateProceedings(UpdateChequeForm updateChequeForm) throws Exception;
	
	public List getChequeReprintDetails(UpdateChequeForm updateChequeForm);
	
	public boolean checkChequeNoIsExists(String newChequeNo) throws Exception;
		
}

package com.cgg.dataentry.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.cgg.common.ApplicationConstants;
import com.cgg.common.PRTemplatePDF;
import com.cgg.dataentry.model.LOCMLACMRFEntryModel;
import com.cgg.dataentry.model.LocMlaCmrfPrintDTO;
import com.cgg.dataentry.service.MLALocPrintService;




@Controller
@RequestMapping(value = "/PrintMlaLoc")
public class MLALocPrintController {
	@Autowired
	private MLALocPrintService mlaLocPrintService;
	
	
	
	@RequestMapping(method = RequestMethod.GET)
    public String locDetails(Model model ,HttpSession session)throws Exception {
		String userId=null;
		String roleId = (String) session.getAttribute("rolesStr");
		String consNo = (String) session.getAttribute("consNo");
		if(session!=null && session.getAttribute("userid")!=null) 
			userId=(String)session.getAttribute("userid");
		if (Integer.parseInt(roleId) != ApplicationConstants.MLA_ROLE) {
			 return  "redirect:/";
		}
		
		List<LOCMLACMRFEntryModel> locMLAList = new ArrayList<>();

        try {
        	 locMLAList = mlaLocPrintService.getAllApprovedData(consNo,userId);
        	 if (locMLAList != null && !locMLAList.isEmpty()) { 
        	      model.addAttribute("locData", locMLAList);
        	 } else {
        	      model.addAttribute("msg", "No records found!");
        	 }
		} catch (Exception e) {
			e.printStackTrace();
		    model.addAttribute("msg", "Error fetching records. Please try again.");
		}
		
        return "mlaLOCPrint";
    }
	@GetMapping("/downloadLocPRLetter")
	public String downloadLocPRLetter(Map<String,Object> model,HttpServletRequest req) throws Exception {
		HttpSession session=req.getSession();
		System.out.println("<<<<-------------->>>>");

		String userId = (String)session.getAttribute("userid");
		String locTokenNo = req.getParameter("locTokenNo");
		// String applicationId = inwardService.getApplicationIdByTokenHash(token_hash);
		System.out.println("<<<<-------------->>>>"+locTokenNo);
		LOCMLACMRFEntryModel form = mlaLocPrintService.getLOCCoveringLetterData(locTokenNo);
		req.getSession().setAttribute("formData", form);
		form.setUserId(userId);
		model.put("formData", form);
		model.put("userId", userId);
		return "/reports/cmrfLocPRLetter";
	}
	
	@RequestMapping(value="/downloadPRTemplateLOCPDF",method=RequestMethod.GET)
	public String PRPDFDownload(HttpServletRequest request,HttpServletResponse response,@ModelAttribute("LOCMLACMRFEntryModel") LOCMLACMRFEntryModel inwardForm,Map<String,Object> model) throws Exception {
		System.out.println("PDFDownload");
		LOCMLACMRFEntryModel formData = (LOCMLACMRFEntryModel) request.getSession().getAttribute("formData");
		request.setAttribute("formData", formData);
		PRTemplatePDF prTemplatePDF = new PRTemplatePDF();
		prTemplatePDF.generateLOCPDF(request, response);
         return null;
	}
	
	@RequestMapping(value = "/printEstimationLetter", method = RequestMethod.GET)
	public String printEstimationLetter(@RequestParam("locTokenNo") String locTokenNo, Map<String,Object> model,HttpServletRequest req) throws Exception {
		try {
			System.out.println("loc Token no:"+locTokenNo);
			LocMlaCmrfPrintDTO  details = mlaLocPrintService.getEstimationData(locTokenNo);
			if (details != null) {
				Integer hospCode = details.getHospCode();
				System.out.println("---------->>>>>>>>> HospCode : " + hospCode);
				req.getSession().setAttribute("formData", details);
				//System.out.println(details.toString());
				model.put("formData", details);
				if (hospCode != null && hospCode.equals(411)) {
					return "/reports/GovtEntLocEstimationLetter";
				} else {
					return "/reports/locEstimationLetter";
				}
			} else {
				model.put("msg", "No data found for LOC Token No: " + locTokenNo);
				return "redirect:/PrintMlaLoc"; // fallback error page
        	}
		} catch (Exception e) {
			e.printStackTrace();
			model.put("error", "An unexpected error occurred while generating the letter.");
			return "redirect:/PrintMlaLoc";
		}
		
	}
	
	@RequestMapping(value="/downloadEstimationLetterPDF",method=RequestMethod.GET)
	public String estimationLetterPDFDownload(HttpServletRequest request,HttpServletResponse response,Map<String,Object> model) throws Exception {
		try {
			System.out.println("estimationLetterPDFDownload");
			LocMlaCmrfPrintDTO formData = (LocMlaCmrfPrintDTO) request.getSession().getAttribute("formData");
			request.setAttribute("formData", formData);
			System.out.println("------>>>>>HospCode : " + formData.getHospCode());
			Integer hospCode = formData.getHospCode();
			PRTemplatePDF prTemplatePDF = new PRTemplatePDF();
			if (hospCode != null && hospCode.equals(411)) {
				prTemplatePDF.generateENTEstimationLetterPDF(request, response);
			} else {
				prTemplatePDF.generateEstimationLetterPDF(request, response);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
}

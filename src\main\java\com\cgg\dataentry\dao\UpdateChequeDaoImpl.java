package com.cgg.dataentry.dao;

import java.sql.BatchUpdateException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.cgg.common.ApplicationConstants;
import com.cgg.common.CommonFunctions;
import com.cgg.common.CommonUtils;
import com.cgg.dataentry.model.UpdateChequeForm;

@Repository
public class UpdateChequeDaoImpl implements UpdateChequeDao {

	@Autowired
	DataSource dataSource;

	@Autowired
	JdbcTemplate jdbcTemlate;
	public Map<String, Object> getTodaysCheckHistory(HttpServletRequest request) {
		HttpSession session = request.getSession();
		String userId = (String) session.getAttribute("userid");
		String roleId=(String) session.getAttribute("rolesStr");
		Map<String, Object> response = new HashMap<>();
		String sql = "SELECT cmrf_no, cheque_no, to_char(cheque_date,'dd-mm-yyyy') as cheque_date, to_char(revaildated_date,'dd-mm-yyyy') as revaildated_date, corrected_name,corrected_account_no, " + "CASE "
				+ "   WHEN is_issued_new_cheque = 'true' THEN 'Yes' "
				+ "   WHEN is_issued_new_cheque = 'false' THEN 'No' " + "   ELSE '' "
				+ "END AS is_issued_new_cheque , " + "new_cheque_no, remarks " + "FROM cheque_history_details "
				+ "WHERE date(entered_on) = TO_DATE(?,'yyyy-mm-dd')" + (ApplicationConstants.REV_DEO.equals(roleId) ? " and entered_by = ?" : "");

		List<UpdateChequeForm> chequeHistoryList = new ArrayList<>();
		try (Connection con = dataSource.getConnection(); PreparedStatement pst = con.prepareStatement(sql)) {
			String enteredOnDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
			pst.setString(1, enteredOnDate);
			if (ApplicationConstants.REV_DEO.equals(roleId)) {
				pst.setString(2, userId);
			}
			try (ResultSet rs = pst.executeQuery()) {
				while (rs.next()) {
					UpdateChequeForm form = new UpdateChequeForm();
					form.setCmrfNo(rs.getString("cmrf_no"));
					form.setChequeNo(rs.getString("cheque_no"));
					form.setChequeDate(rs.getString("cheque_date"));
					form.setRevalidatedDate(rs.getString("revaildated_date"));
					form.setCorrectedName(rs.getString("corrected_name"));
					form.setIsNewChequeIssued(rs.getString("is_issued_new_cheque"));
					form.setNewChequeNo(rs.getString("new_cheque_no"));
					form.setRemarks(rs.getString("remarks"));
					form.setCorrectedAccountNo(rs.getString("corrected_account_no"));

					chequeHistoryList.add(form); // Add each object to the list
				}
			}
			response.put("status", "success");
			response.put("data", chequeHistoryList);
		} catch (Exception e) {
			e.printStackTrace();
			response.put("status", "error");
			response.put("message", "Something went wrong: " + e.getMessage());
		}
		return response;
	}
	@Override
	public Map<String, Object> getDetails(UpdateChequeForm request) throws SQLException {

		Connection con = null;
		Statement st = null;
		ResultSet rs = null;
		String sql = null;
		Map<String, Object> result = new HashMap<>();
		UpdateChequeForm updateChequeForm = new UpdateChequeForm();
		List<UpdateChequeForm> updatedChequeDetails = new ArrayList<>();
		try {
			con = dataSource.getConnection();
			if (request.getCmrfNo() != null && request.getCmrfNo()!="") {
				String cmrfNo = request.getCmrfNo();
				String cmrfYear = request.getCmrfYear();
				String totalCmrfNo = cmrfNo+cmrfYear;
				sql = "select c.cmrf_no,c.bank_acc_hol_name,c.father_son_of,r.cheque_no,c.pat_name,c.pat_address||'<br>'|| case when c.pat_mandal is null or c.pat_mandal=0 then '' else mname||'(M)<br>' end||distname||' Dist.' as pat_address,to_char(r.billdate, 'DD/MM/YYYY') as billdate,r.bill_amt  from cmrelief c join rev_san r on c.cmrf_no=r.cmrf_no inner join district d on (c.pat_district = d.distno) left join mandal on int4(c.pat_district)=int4(distcode) and c.pat_mandal=mcode where c.cmrf_no = '"
						+ totalCmrfNo + "'";
				System.out.println("Cheque Details --> "+sql);
				st = con.createStatement();
				rs = st.executeQuery(sql);
				while (rs.next()) {
					updateChequeForm.setCmrfNo(rs.getString("cmrf_no"));
					updateChequeForm.setFatherSonOf(rs.getString("father_son_of"));
					updateChequeForm.setPatName(rs.getString("pat_name"));
					updateChequeForm.setBankAccHolName(rs.getString("bank_acc_hol_name"));
					updateChequeForm.setPatAddress(rs.getString("pat_address"));
					updateChequeForm.setChequeNo(rs.getString("cheque_no"));
					updateChequeForm.setChequeDate(rs.getString("billdate"));
					updateChequeForm.setChequeAmt(rs.getString("bill_amt"));
					result.put("chequeDetails", updateChequeForm);

				}
				sql = "select cmrf_no,cheque_no,to_char(cheque_date,'DD/MM/YYYY') as cheque_date,to_char(revaildated_date,'DD/MM/YYYY') as revaildated_date,corrected_name,corrected_account_no, "
						+ " case"
						+ " when is_issued_new_cheque = 'true' then 'Yes' "
						+ " when is_issued_new_cheque = 'false' then 'No' "
						+ " else '' "
						+ " end as is_issued_new_cheque "
						+ ",new_cheque_no,remarks,to_char(entered_on,'DD/MM/YYYY') as entered_on, so_approved from cheque_history_details where cmrf_no = '"
						+ totalCmrfNo + "'";
				System.out.println("Cheque Details --> "+sql);
				st = con.createStatement();
				rs = st.executeQuery(sql);
				while (rs.next()) {
					UpdateChequeForm form = new UpdateChequeForm();
					form.setCmrfNo(rs.getString("cmrf_no"));
					form.setChequeNo(rs.getString("cheque_no"));
					form.setChequeDate(rs.getString("cheque_date"));
					form.setRevalidatedDate(rs.getString("revaildated_date"));
					form.setCorrectedName(rs.getString("corrected_name"));
					form.setIsNewChequeIssued(rs.getString("is_issued_new_cheque"));
					form.setNewChequeNo(rs.getString("new_cheque_no"));
					form.setRemarks(rs.getString("remarks"));
					form.setChequeUpdatedDate(rs.getString("entered_on"));
					form.setCorrectedAccountNo(rs.getString("corrected_account_no"));
					form.setSoApproved(rs.getBoolean("so_approved"));
					updatedChequeDetails.add(form);
				}
				result.put("chequeHistory", updatedChequeDetails);
			} else if (request.getChequeNo() != null) {
				sql = "select c.cmrf_no,c.bank_acc_hol_name,c.father_son_of,r.cheque_no,c.pat_name,c.pat_address,to_char(r.billdate, 'DD/MM/YYYY') as billdate ,r.bill_amt from cmrelief c join rev_san r on c.cmrf_no=r.cmrf_no left join cheque_history_details chd on chd.cmrf_no = c.cmrf_no where COALESCE(chd.new_cheque_no::text, chd.cheque_no::text, r.cheque_no) = '"
						+ request.getChequeNo() + "'";
				System.out.println("Cheque Details --> "+sql);
				st = con.createStatement();
				rs = st.executeQuery(sql);
				if (rs.next()) {
					String cmrfNo = rs.getString("cmrf_no");
					updateChequeForm.setCmrfNo(rs.getString("cmrf_no"));
					updateChequeForm.setFatherSonOf(rs.getString("father_son_of"));
					updateChequeForm.setPatName(rs.getString("pat_name"));
					updateChequeForm.setBankAccHolName(rs.getString("bank_acc_hol_name"));
					updateChequeForm.setPatAddress(rs.getString("pat_address"));
					updateChequeForm.setChequeNo(rs.getString("cheque_no"));
					updateChequeForm.setChequeDate(rs.getString("billdate"));
					updateChequeForm.setChequeAmt(rs.getString("bill_amt"));
					result.put("chequeDetails", updateChequeForm);

				// }
				//  else {
				// 	sql = "select cmrf_no from cheque_history_details where cheque_no=" + request.getChequeNo() +" or new_cheque_no="+request.getChequeNo();
				// 	System.out.println("Cheque Details --> "+sql);
				// 	st = con.createStatement();
				// 	rs = st.executeQuery(sql);
				// 	if (rs.next()) {
				// 		String cmrfNo = rs.getString("cmrf_no");
				// 		sql = "select c.cmrf_no,r.cheque_no,c.father_son_of,c.pat_name,c.pat_address,to_char(r.billdate, 'DD/MM/YYYY') as billdate,r.bill_amt from cmrelief c join rev_san r on c.cmrf_no=r.cmrf_no where c.cmrf_no = '"
				// 				+ cmrfNo + "'";
				// 		System.out.println("Cheque Details --> "+sql);
				// 		st = con.createStatement();
				// 		rs = st.executeQuery(sql);
				// 		if (rs.next()) {
				// 			updateChequeForm.setCmrfNo(rs.getString("cmrf_no"));
				// 			updateChequeForm.setFatherSonOf(rs.getString("father_son_of"));
				// 			updateChequeForm.setPatName(rs.getString("pat_name"));
				// 			updateChequeForm.setPatAddress(rs.getString("pat_address"));
				// 			updateChequeForm.setChequeNo(rs.getString("cheque_no"));
				// 			updateChequeForm.setChequeDate(rs.getString("billdate"));
				// 			updateChequeForm.setChequeAmt(rs.getString("bill_amt"));
				// 			result.put("chequeDetails", updateChequeForm);

				// 		}
						sql = "select cmrf_no,cheque_no,to_char(cheque_date,'dd/mm/yyyy') as cheque_date,to_char(revaildated_date,'dd/mm/yyyy') as revaildated_date,corrected_name,corrected_account_no,"
								+ " case"
								+ " when is_issued_new_cheque = 'true' then 'Yes'"
								+ " when is_issued_new_cheque = 'false' then 'No'"
								+ " else '' "
								+ " end as is_issued_new_cheque " 
								+ " ,new_cheque_no,remarks,to_char(entered_on,'DD/MM/YYYY') as entered_on, so_approved from cheque_history_details where cmrf_no = '"
								+ cmrfNo + "'";
						System.out.println("Cheque Details --> "+sql);
						st = con.createStatement();
						rs = st.executeQuery(sql);
						while (rs.next()) {
							UpdateChequeForm form = new UpdateChequeForm();
							form.setCmrfNo(rs.getString("cmrf_no"));
							form.setChequeNo(rs.getString("cheque_no"));
							form.setChequeDate(rs.getString("cheque_date"));
							form.setRevalidatedDate(rs.getString("revaildated_date"));
							form.setCorrectedName(rs.getString("corrected_name"));
							form.setIsNewChequeIssued(rs.getString("is_issued_new_cheque"));
							form.setNewChequeNo(rs.getString("new_cheque_no"));
							form.setRemarks(rs.getString("remarks"));
							form.setChequeUpdatedDate(rs.getString("entered_on"));
							form.setCorrectedAccountNo(rs.getString("corrected_account_no"));
							form.setSoApproved(rs.getBoolean("so_approved"));
							updatedChequeDetails.add(form);
						}
				// 	}
				}
				result.put("chequeHistory", updatedChequeDetails);

			}

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (rs != null) {
				rs.close();
				rs = null;
			}
			if (st != null) {
				st.close();
				st = null;
			}
			if (con != null && !con.isClosed()) {
				con.close();
				con = null;
			}
		}

		return result;
	}

	@Override
	public boolean updateChequeDetails(UpdateChequeForm updateChequeForm, HttpServletRequest request) throws SQLException {
		Connection con = null;
		Statement st = null;
		ResultSet rs = null;
		String sql = null;
		String userId = null;
		String chequeDate = null;
		int chequeNo = 0, chequeNum = 0;
		String insertCnd = "";
		String paramCnd = "";
		String insertCnd1 = "";
		String paramCnd1 = "";
		String insertCnd2 = "";
		String paramCnd2 = "";
		String insertCnd3 = "";
		String paramCnd3 = "";
		String ipAddress = request.getRemoteAddr();
		try {
			HttpSession session = request.getSession();
			userId = (String) session.getAttribute("userid");
			con = dataSource.getConnection();
			String cmrfNo = updateChequeForm.getCmrfNo();
			String correctedName = updateChequeForm.getCorrectedName();
			String correctedAccountNo = updateChequeForm.getCorrectedAccountNo();
			String revalidatedDate = updateChequeForm.getRevalidatedDate();
			String newChequeNo = updateChequeForm.getNewChequeNo();
			String remarks = updateChequeForm.getRemarks();
			String othersRemarks = updateChequeForm.getOthersRemarks();
			String isNewChequeIssued = updateChequeForm.getIsNewChequeIssued();

			if(remarks.equals("Others")) {
				if(!othersRemarks.trim().isEmpty()) {
					remarks = remarks + " -- " + othersRemarks;
				} 
			}

			sql = "select cmrf_no,cheque_no,to_char(cheque_date,'dd/mm/yyyy') as cheque_date,new_cheque_no from cheque_history_details where cmrf_no = '"
					+ cmrfNo + "' order by entered_on desc limit 1";
			System.out.println("Updating cheque sql --> "+sql);
			st = con.createStatement();
			rs = st.executeQuery(sql);
			if (rs.next()) {
				chequeNo = rs.getInt("cheque_no");
				chequeNum = rs.getInt("new_cheque_no");
				chequeDate = rs.getString("cheque_date");
			} else {
				sql = "select c.cmrf_no,r.cheque_no, to_char(r.billdate,'dd/mm/yyyy') as billdate,r.bill_amt  from cmrelief c join rev_san r on c.cmrf_no=r.cmrf_no where c.cmrf_no = '"
						+ cmrfNo + "' ";
				System.out.println("Updating cheque sql --> "+sql);
				rs = st.executeQuery(sql);
				if (rs.next()) {
					chequeNo = rs.getInt("cheque_no");
					chequeDate = rs.getString("billdate");
				}
			}
			if(chequeNum!=0) {
				insertCnd = " ,cheque_no ";
				paramCnd = " ," + chequeNum + " ";
			}else {
				insertCnd = " ,cheque_no ";
				paramCnd = " ," + chequeNo + " ";
			}
			if (isNewChequeIssued.equals("yes")) {
					insertCnd1 = " , new_cheque_no, remarks ";
					paramCnd1 = " ,"+ newChequeNo + ", '" + remarks + "' ";
			}
			if(CommonFunctions.validateData(correctedName)){
					insertCnd2 = " , corrected_name ";
					paramCnd2 = " ,'"+ correctedName + "' ";
			}	
			if(CommonFunctions.validateData(correctedAccountNo)){
					insertCnd3 = " , corrected_account_no ";
					paramCnd3 = " ,'"+ correctedAccountNo + "' ";
			}	
				sql = "insert into public.cheque_history_details("
						+ "cmrf_no, cheque_date, revaildated_date"+ insertCnd2 +", is_issued_new_cheque, entered_on, entered_by, ip_address "
						+ insertCnd + insertCnd1 + insertCnd3 +") " + "values ('" + cmrfNo + "', to_date('" + chequeDate + "','dd/mm/yyyy'), to_date('" + revalidatedDate + "','dd/mm/yyyy')"
						+ paramCnd2 + ", '" + isNewChequeIssued + "', now(), '" + userId + "', '" + ipAddress
						+ "' " + paramCnd + paramCnd1 + paramCnd3 +" )";
				System.out.println("Updating cheque sql --> "+sql);
				int count = st.executeUpdate(sql);
				if (count > 0) {
					return true;
				}

		} catch (Exception e) {
			e.printStackTrace();
		}finally {
			if (rs != null) {
				rs.close();
				rs = null;
			}
			if (st != null) {
				st.close();
				st = null;
			}
			if (con != null && !con.isClosed()) {
				con.close();
				con = null;
			}
		}
		return false;
	}

	@Override
	public boolean editChequeDetails(UpdateChequeForm updateChequeForm, HttpServletRequest request) throws SQLException {
		Connection con = null;
		Statement st = null;
		ResultSet rs = null;
		PreparedStatement pst = null;
		int flag=0;
		String sql = null;
		String userId = null;
		String chequeDate = null;
		int chequeNo = 0, chequeNum = 0;
		String insertCnd = "";
		String insertCnd1 = "";
		String insertCnd2 = "";
		String insertCnd3 = "";
		String ipAddress = request.getRemoteAddr();
		try {
			HttpSession session = request.getSession();
			userId = (String) session.getAttribute("userid");
			con = dataSource.getConnection();
			String cmrfNo = updateChequeForm.getCmrfNo();
			String prevChequeNo = updateChequeForm.getChequeNo();
			String correctedName = updateChequeForm.getCorrectedName();
			String correctedAccountNo = updateChequeForm.getCorrectedAccountNo();
			String revalidatedDate = updateChequeForm.getRevalidatedDate();
			String newChequeNo = updateChequeForm.getNewChequeNo();
			String remarks = updateChequeForm.getRemarks();
			String othersRemarks = updateChequeForm.getOthersRemarks();
			String isNewChequeIssued = updateChequeForm.getIsNewChequeIssued();
			System.out.println("CMRF No : " + cmrfNo + "\nCorrected Name : " + correctedName + "\nCorrected Account Number : " + correctedAccountNo + "\nRevalidated Date : " + revalidatedDate + "\nPrevious Cheque Number : " + prevChequeNo + "\nNew Cheque Number : " + newChequeNo + "\nRemarks : " + remarks + "\nOthers Remarks : " + othersRemarks + "\nIs new cheque issued : " + isNewChequeIssued);

			if(remarks.equals("Others")) {
				if(!othersRemarks.trim().isEmpty()) {
					remarks = remarks + " -- " + othersRemarks;
				} 
			}

			sql = "select cmrf_no,cheque_no,to_char(cheque_date,'dd/mm/yyyy') as cheque_date,new_cheque_no from cheque_history_details where cmrf_no = '"
					+ cmrfNo + "' and coalesce(cast(new_cheque_no as text),'') = '"+ prevChequeNo +"' order by entered_on desc";
			System.out.println("Updating cheque sql --> "+sql);
			st = con.createStatement();
			rs = st.executeQuery(sql);
			if (rs.next()) {
				chequeNo = rs.getInt("cheque_no");
				chequeNum = rs.getInt("new_cheque_no");
				chequeDate = rs.getString("cheque_date");
			}
			pst = con.prepareStatement("INSERT INTO public.cheque_history_details_log(cmrf_no, cheque_no, cheque_date, revaildated_date, corrected_name, is_issued_new_cheque, new_cheque_no, remarks, entered_on, "
				+ " entered_by, ip_address, is_esigned, esigned_date, corrected_account_no, so_approved_on, so_approved_ip, so_approved, so_approved_by, is_chq_reprinted, logged_timestamp,logged_remarks,logged_ipaddress,logged_by) select chd.cmrf_no, chd.cheque_no, chd.cheque_date, revaildated_date, corrected_name, is_issued_new_cheque, "
				+ " new_cheque_no, remarks, chd.entered_on, chd.entered_by, chd.ip_address, chd.is_esigned, chd.esigned_date, chd.corrected_account_no, so_approved_on, so_approved_ip, so_approved, so_approved_by, is_chq_reprinted, now(), 'Updating Cheque history details', '" + ipAddress + "', '" + userId + "' from cheque_history_details chd "
				+ " where chd.cmrf_no = ? AND (COALESCE(chd.new_cheque_no::text, '') = ? OR chd.new_cheque_no IS NULL OR chd.new_cheque_no::text = '') and chd.so_approved is false");
			pst.setString(1, cmrfNo);
			pst.setString(2, prevChequeNo);
			
			flag = pst.executeUpdate();
			if(flag>0){
				if (chequeNo != 0) {
					insertCnd = " , cheque_no = " + chequeNo;
				}
	
				if ("yes".equals(isNewChequeIssued)) {
					insertCnd1 = " , new_cheque_no = " + newChequeNo;
				}
				
				if (CommonFunctions.validateData(remarks)) {
					insertCnd1 += " , remarks = '" + remarks.trim() + "'";
				}
	
				if (CommonFunctions.validateData(correctedName)) {
					insertCnd2 = " , corrected_name = '" + correctedName.trim() + "'";
				}
	
				if (CommonFunctions.validateData(correctedAccountNo)) {
					insertCnd3 = " , corrected_account_no = '" + correctedAccountNo.trim() + "'";
				}
	
				sql = "UPDATE cheque_history_details SET "
					+ "cmrf_no = '" + cmrfNo + "', "
					+ "cheque_date = TO_DATE('" + chequeDate + "', 'DD/MM/YYYY'), "
					+ "revaildated_date = TO_DATE('" + revalidatedDate + "', 'DD/MM/YYYY'), "
					+ "is_issued_new_cheque = '" + isNewChequeIssued + "', "
					+ "entered_on = NOW(), "
					+ "entered_by = '" + userId + "', "
					+ "ip_address = '" + ipAddress + "' "
					+ insertCnd + insertCnd1 + insertCnd2 + insertCnd3 + " where cmrf_no = '"+cmrfNo+"' and coalesce(cast(new_cheque_no as text),'') = '"+ prevChequeNo +"';";
	
				System.out.println("Updating cheque SQL --> " + sql);
					int count = st.executeUpdate(sql);
					if (count > 0) {
						return true;
					}
			}

		} catch (Exception e) {
			e.printStackTrace();
		}finally {
			if (rs != null) {
				rs.close();
				rs = null;
			}
			if (st != null) {
				st.close();
				st = null;
			}
			if (con != null && !con.isClosed()) {
				con.close();
				con = null;
			}
		}
		return false;
	}

	@Override
	public Map<String, Object> getChequeDetailsFromTo(UpdateChequeForm request) throws SQLException {
		
		Connection con = null;
		Statement st = null;
		ResultSet rs = null;
		String sql = null;
		Map<String, Object> result = new HashMap<>();
		List<UpdateChequeForm> updatedChequeDetails = new ArrayList<>();
		try {
			con = dataSource.getConnection();
			String dateFrom = request.getDateFrom();
			String dateTo = request.getDateTo();
			System.out.println(dateFrom + " -- "+ dateTo);
			if ((dateFrom != null && dateFrom!="") && (dateTo != null && dateTo!="")) {
				
				sql = "select chd.cmrf_no ,c.father_son_of,c.pat_name,c.pat_address || CASE WHEN c.pat_mandal IS NULL OR c.pat_mandal = 0 THEN ', ' ELSE ', ' || m.mname || ' (M), ' END || d.distname || ' (D).' AS pat_address," 
						+" rs.bill_amt as chequeamt, chd.cheque_no ,to_char(chd.cheque_date,'DD/MM/YYYY') as cheque_date," 
						+" to_char(chd.revaildated_date,'DD/MM/YYYY') as revaildated_date," 
						+" case when chd.corrected_name is null or chd.corrected_name = '' then '-' else chd.corrected_name end as corrected_name,coalesce(chd.corrected_account_no,'-') as corrected_account_no," 
						+" case when chd.is_issued_new_cheque = 'true' then 'Yes' when chd.is_issued_new_cheque = 'false' then 'No' else '' end as is_issued_new_cheque," 
						+" chd.new_cheque_no, chd.remarks ,to_char(chd.entered_on,'DD/MM/YYYY') as updated_on,chd.entered_by,us.name " 
						+" from cheque_history_details chd" 
						+" join cmrelief c on c.cmrf_no=chd.cmrf_no" 
						+" join rev_san rs on rs.cmrf_no=chd.cmrf_no"
						+" join users us on us.userid=chd.entered_by"						
						+" LEFT JOIN district d ON d.distno = c.pat_district"						
						+" LEFT JOIN mandal m ON m.mcode = c.pat_mandal AND m.distcode::int = c.pat_district"						
						+" where date(entered_on) between to_date('"+dateFrom+"','DD/MM/YYYY') and to_date('"+dateTo+"','DD/MM/YYYY') order by chd.cmrf_no desc";
				System.out.println("Cheque Details --> "+sql);
				st = con.createStatement();
				rs = st.executeQuery(sql);
				while (rs.next()) {
					UpdateChequeForm updateChequeForm = new UpdateChequeForm();
					updateChequeForm.setCmrfNo(rs.getString("cmrf_no"));
					updateChequeForm.setFatherSonOf(rs.getString("father_son_of"));
					updateChequeForm.setPatName(rs.getString("pat_name"));
					updateChequeForm.setPatAddress(rs.getString("pat_address"));
					updateChequeForm.setChequeNo(rs.getString("cheque_no"));
					updateChequeForm.setChequeDate(rs.getString("cheque_date"));
					updateChequeForm.setChequeAmt(rs.getString("chequeamt"));
					updateChequeForm.setRevalidatedDate(rs.getString("revaildated_date"));
					updateChequeForm.setCorrectedName(rs.getString("corrected_name"));
					updateChequeForm.setCorrectedAccountNo(rs.getString("corrected_account_no"));
					updateChequeForm.setIsNewChequeIssued(rs.getString("is_issued_new_cheque"));
					updateChequeForm.setNewChequeNo(rs.getString("new_cheque_no"));
					updateChequeForm.setRemarks(rs.getString("remarks"));
					updateChequeForm.setChequeUpdatedDate(rs.getString("updated_on"));
					updateChequeForm.setEnteredBy(rs.getString("entered_by"));
					updateChequeForm.setUserName(rs.getString("name"));
					updatedChequeDetails.add(updateChequeForm);

				}
				result.put("chequeHistory", updatedChequeDetails);
			}

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (rs != null) {
				rs.close();
				rs = null;
			}
			if (st != null) {
				st.close();
				st = null;
			}
			if (con != null && !con.isClosed()) {
				con.close();
				con = null;
			}
		}

		return result;
	}

	@Override
	public Map<String, Object> getUpdChqDtlsForPrint(UpdateChequeForm updChqForm,boolean cmrfFlag, boolean dateFlag) throws SQLException {
		
		Connection con = null;
		Statement st = null;
		ResultSet rs = null;
		String sql = null;
		String cond = "";
		Map<String, Object> result = new HashMap<>();
		List<UpdateChequeForm> updatedChequeDetails = new ArrayList<>();
		try {
			con = dataSource.getConnection();
			String cmrfNo = updChqForm.getCmrfNo().trim();
			String cmrfYear = updChqForm.getCmrfYear().trim();
			String totalCmrfNo = cmrfNo + cmrfYear;
			String chequeNo = updChqForm.getChequeNo();
			String dateFrom = updChqForm.getDateFrom();
			String dateTo = updChqForm.getDateTo();
			System.out.println("CMRF Flag : " + cmrfFlag+"\nDate Flag : "+dateFlag);
			if(cmrfFlag==true) {
					if(totalCmrfNo.equals("/CMRF/202") && chequeNo!=null && !chequeNo.isEmpty()) {
						cond= " chd.new_cheque_no = '" + chequeNo + "' and ";
					}
					else if(totalCmrfNo!=null && !totalCmrfNo.isEmpty()) {
						cond= " chd.cmrf_no = '" + totalCmrfNo + "' and ";
					}
					// else if(totalCmrfNo!=null && !totalCmrfNo.isEmpty() && chequeNo!=null && !chequeNo.isEmpty()) {
					// 	System.out.println("1 executing");
					// 	cond= " chd.cmrf_no = '" + totalCmrfNo + "' and chd.new_cheque_no = '" + chequeNo + "' and ";
					// }
					sql = " select chd.cmrf_no ,c.father_son_of,c.pat_name,c.pat_address || CASE WHEN c.pat_mandal IS NULL OR c.pat_mandal = 0 THEN ', ' ELSE ', ' || m.mname || ' (M), ' END || d.distname || ' (D).' AS pat_address,rs.bill_amt as chequeamt, chd.cheque_no ,"
						+ " to_char(chd.cheque_date,'DD/MM/YYYY') as cheque_date,to_char(chd.revaildated_date,'DD/MM/YYYY') as revaildated_date,"
						+ " chd.corrected_name,chd.corrected_account_no,case when chd.is_issued_new_cheque = 'true' then 'Yes' "
						+ " else '' end as is_issued_new_cheque,chd.new_cheque_no, chd.remarks ,to_char(chd.entered_on,'DD/MM/YYYY')"
						+ " as updated_on from cheque_history_details chd "
						+ " join cmrelief c on c.cmrf_no=chd.cmrf_no "
						+ " join rev_san rs on rs.cmrf_no=chd.cmrf_no "
						+ " LEFT JOIN district d ON d.distno = c.pat_district"						
						+ " LEFT JOIN mandal m ON m.mcode = c.pat_mandal AND m.distcode::int = c.pat_district"
						+ " left join cheque_reprint cr ON cr.cmrf_no = chd.cmrf_no AND cr.new_cheque_no = cast(chd.new_cheque_no as varchar) "
						+ " where " + cond + " is_issued_new_cheque = 'true' and chd.is_chq_reprinted = 'false' and chd.is_esigned = 'true' order by chd.cmrf_no desc ";
			            
			            System.out.println("--->>Cheque to be printed details : " + sql);
					
				}else if(dateFlag==true) {
					if(dateFrom!=null && dateTo!=null) {
						cond= " date(entered_on) between to_date('"+dateFrom+"','DD/MM/YYYY') and to_date('"+dateTo+"','DD/MM/YYYY') and ";
					}
					sql = " select chd.cmrf_no ,c.father_son_of,c.pat_name,c.pat_address || CASE WHEN c.pat_mandal IS NULL OR c.pat_mandal = 0 THEN ', ' ELSE ', ' || m.mname || ' (M), ' END || d.distname || ' (D).' AS pat_address,rs.bill_amt as chequeamt, chd.cheque_no ,"
						+ " to_char(chd.cheque_date,'DD/MM/YYYY') as cheque_date,to_char(chd.revaildated_date,'DD/MM/YYYY') as revaildated_date,"
						+ " chd.corrected_name,chd.corrected_account_no,case when chd.is_issued_new_cheque = 'true' then 'Yes' "
						+ " else '' end as is_issued_new_cheque,chd.new_cheque_no, chd.remarks ,to_char(chd.entered_on,'DD/MM/YYYY')"
						+ " as updated_on from cheque_history_details chd "
						+ " join cmrelief c on c.cmrf_no=chd.cmrf_no "
						+ " join rev_san rs on rs.cmrf_no=chd.cmrf_no "
						+ " LEFT JOIN district d ON d.distno = c.pat_district"						
						+ " LEFT JOIN mandal m ON m.mcode = c.pat_mandal AND m.distcode::int = c.pat_district"
						+ " left join cheque_reprint cr ON cr.cmrf_no = chd.cmrf_no AND cr.new_cheque_no = cast(chd.new_cheque_no as varchar) "
						+ " where " + cond + " is_issued_new_cheque = 'true' and chd.is_chq_reprinted = 'false' and chd.is_esigned = 'true' order by chd.cmrf_no desc ";
			            
			            System.out.println("--->>Cheques to be printed details : " + sql);
				
				}
				st = con.createStatement();
				rs = st.executeQuery(sql);
				while (rs.next()) {
					UpdateChequeForm updateChequeForm = new UpdateChequeForm();
					updateChequeForm.setCmrfNo(rs.getString("cmrf_no"));
					updateChequeForm.setFatherSonOf(rs.getString("father_son_of"));
					updateChequeForm.setPatName(rs.getString("pat_name"));
					updateChequeForm.setPatAddress(rs.getString("pat_address"));
					updateChequeForm.setChequeNo(rs.getString("cheque_no"));
					updateChequeForm.setChequeDate(rs.getString("cheque_date"));
					updateChequeForm.setChequeAmt(rs.getString("chequeamt"));
					updateChequeForm.setRevalidatedDate(rs.getString("revaildated_date"));
					updateChequeForm.setCorrectedName(rs.getString("corrected_name"));
					updateChequeForm.setCorrectedAccountNo(rs.getString("corrected_account_no"));
					updateChequeForm.setIsNewChequeIssued(rs.getString("is_issued_new_cheque"));
					updateChequeForm.setNewChequeNo(rs.getString("new_cheque_no"));
					updateChequeForm.setRemarks(rs.getString("remarks"));
					updateChequeForm.setChequeUpdatedDate(rs.getString("updated_on"));
					updatedChequeDetails.add(updateChequeForm);

				}
				result.put("chequeHistory", updatedChequeDetails);

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (rs != null) {
				rs.close();
				rs = null;
			}
			if (st != null) {
				st.close();
				st = null;
			}
			if (con != null && !con.isClosed()) {
				con.close();
				con = null;
			}
		}

		return result;
	}

	@Override
	public List<UpdateChequeForm> generateBeneficiaryLetters(String cmrfNo,String newChequeNo)	throws Exception {
				Connection con=null;
				Statement st=null;
				ResultSet rs=null;
				String sql=null;
				List<UpdateChequeForm> chequeList = new ArrayList<UpdateChequeForm>();	

				if(cmrfNo!=null && newChequeNo != null) {
					sql = " select chd.cmrf_no, coalesce(nullif(chd.corrected_name,''),nullif(c.pat_name,''),mc.patient_name) as pat_name," +
			        " coalesce(nullif(chd.corrected_name,''),c.pat_name)||'<br>'||case when length(c.father_son_of)=0 then '' else c.father_son_of||'<br>' end ||c.pat_address||case when mname is null or length(mname)=0  then '<br>' else '<br>'||mname||'(M),' end ||distname||' District'  as Name," +
			        " to_char(cmp_date,'dd-Mon-YYYY') as cmp_date, rs.bill_amt as sanc_amt, to_char(chd.revaildated_date,'ddmmYYYY') as letter_date, chd.new_cheque_no, coalesce(nullif(chd.corrected_account_no,''),nullif(c.bank_account_no,''),nullif(mc.bank_acc_no,''),null) as bank_acc_no, mc.bank_ifsc"
					+ " from cheque_history_details chd " 
					+ " join cmrelief c on c.cmrf_no=chd.cmrf_no " 
					+ " left join district d  on d.distno=c.pat_district" 
					+ " left join mandal m on int4(m.distcode)=c.pat_district and m.mcode=c.pat_mandal"
			        + " join rev_san rs on rs.cmrf_no=chd.cmrf_no"
					+ " join mla_cmrf mc on mc.cmrf_no = chd.cmrf_no "
					+ " where chd.cmrf_no = '" + cmrfNo + "' and new_cheque_no = '" + newChequeNo + "' and chd.is_issued_new_cheque = 'true' and chd.so_approved = 'true' and chd.is_esigned = 'true'";
			            
			    	System.out.println("***********letters-----" + sql);
				}
				try {
					con = dataSource.getConnection();
					st=con.createStatement();
					rs=st.executeQuery(sql);
					while(rs.next()) {
						UpdateChequeForm updateChequeForm = new UpdateChequeForm();
						updateChequeForm.setCmrfNo(rs.getString("cmrf_no"));//cmrfno
						updateChequeForm.setBenfName(CommonFunctions.capitalizeWords(rs.getString("pat_name")));//patient name
						updateChequeForm.setPatAddress(rs.getString("Name")); //address
						updateChequeForm.setCmpDate(rs.getString("cmp_date"));//sanction date
						updateChequeForm.setAmount(rs.getString("sanc_amt"));//amount
						updateChequeForm.setFormatAmt(CommonUtils.convert(rs.getString("sanc_amt")));
						updateChequeForm.setAmtInWords(CommonUtils.inWords(rs.getString("sanc_amt")));
						updateChequeForm.setLetterDate(rs.getString("letter_date").replace("", " &nbsp;"));//cdate
						Date d = (new SimpleDateFormat("ddMMYYYY")).parse(rs.getString("letter_date"));
					    SimpleDateFormat format = new SimpleDateFormat("dd-MMM-yyyy");  
					    d = (new SimpleDateFormat("ddMMyyyy")).parse(rs.getString("letter_date"));
					    updateChequeForm.setFormatDate(format.format(d));
						updateChequeForm.setChequeNo(rs.getString("new_cheque_no"));
						updateChequeForm.setBank_acc_no(rs.getString("bank_acc_no"));
						updateChequeForm.setBank_ifsc(rs.getString("bank_ifsc"));
						chequeList.add(updateChequeForm);
					}
					
				}catch(Exception e) {
					e.printStackTrace();
				}
				finally {
					if(rs!=null) {rs.close();rs=null;}
					if(st!=null) {st.close();st=null;}
					if(con!=null && !con.isClosed()) {con.close();con=null;}
				}

				return chequeList;
			}

	@Override
	public Map<String,Object> updateProceedings(UpdateChequeForm updateChequeForm) throws Exception {
			Map<String, Object> response = new HashMap<>();
			Connection con=null;
			boolean flag=false;
			try {
				con = dataSource.getConnection();
				if (updateChequeForm.getCmrfNo()!=null && updateChequeForm.getNewChequeNo()!=null) {
					System.out.println("cmrfno : "+updateChequeForm.getCmrfNo());
					System.out.println("chequeno : "+updateChequeForm.getNewChequeNo());
					response=updateRecords(updateChequeForm,updateChequeForm.getCmrfNo(),updateChequeForm.getNewChequeNo(),con);
				}
			}catch(Exception e) {
				e.printStackTrace();
			}
		return response;
	}

		private Map<String,Object> updateRecords(UpdateChequeForm updateChequeForm,String cmrfNo,String newChequeNo,Connection conn) {
			Map<String, Object> response = new HashMap<>();
			String message = "";
			boolean flag=false;
			String sql1=null,sql2=null,sql3=null,sql4=null,sql5=null,sql6=null,sql7=null,sql8=null,sql9=null,sql10=null,sql11=null;String amount=null;
			String patname=null;
			String revalidated_date = null;
			String letter_no = null;
			int chq_reprint_count;
			//String cmrfNo=null;
			String sql=null;
			ResultSet rs=null;
			try {
				Statement stmt=conn.createStatement();
				sql="select sanc_amt,coalesce(nullif(chd.corrected_name,''),c.pat_name) as pat_name,to_char(chd.revaildated_date, 'dd-mm-yyyy') AS revaildated_date,c.cmp_no,c.chq_reprint_cnt from cmrelief c join cheque_history_details chd on chd.cmrf_no = c.cmrf_no where c.cmrf_no='"+cmrfNo+"'";
				System.out.println("benifitiary letters query"+sql);
				rs=stmt.executeQuery(sql);
				if(rs.next()) {
					amount=rs.getString(1);
					patname=rs.getString(2);
					revalidated_date=rs.getString(3);
					System.out.println("%%%%% "+revalidated_date);
					letter_no = rs.getString(4);
					chq_reprint_count = rs.getInt(5);

			if(!isExisted("cheque_reprint", "new_cheque_no", newChequeNo)){

				if (isExisted("cheque_reprint", "cmrf_no", cmrfNo)) {
					sql1 = "insert into cheque_reprint_log(cmrf_no,new_cheque_no,cheque_dated,letter_no,cheque_amount,letter_dated,cheque_issue_time,logged_timestamp,logged_ipaddress,logged_by,logged_remarks) select cmrf_no, new_cheque_no, cheque_dated,letter_no,cheque_amount,letter_dated,cheque_issue_time, now(), '" + updateChequeForm.getIpAddr() + "', '" + updateChequeForm.getUserId() + "', 'Cheque Revalidation and Reprinting' from cheque_reprint where cmrf_no = '" + cmrfNo + "' order by cheque_issue_time desc limit 1";
					System.out.println("sql1.1::"+sql1);
					stmt.addBatch(sql1);

					sql2 = "update cheque_reprint set new_cheque_no = '"+ newChequeNo +"', cheque_dated = to_date('"+revalidated_date+"','dd-mm-yyyy'), letter_dated = to_date('"+revalidated_date+"','dd-mm-yyyy'), cheque_issue_time = now() where cmrf_no = '"+  cmrfNo +"' ";
					System.out.println("sql1.2::"+sql2);
					stmt.addBatch(sql2);
				}else{
					sql3="insert into cheque_reprint(cmrf_no,new_cheque_no,cheque_dated,letter_no,cheque_amount,letter_dated,cheque_issue_time) values('"+cmrfNo+"','"+newChequeNo+"',to_date('"+revalidated_date+"','dd-mm-yyyy'),'"+letter_no+"',"+amount+",to_date('"+revalidated_date+"','dd-mm-yyyy'),now())";
					System.out.println("sql1.3::"+sql3);

					stmt.addBatch(sql3);
				}
				
			if(!(isExisted("cheque_details","cheque_no",newChequeNo)||isExisted("cmrelief","cheque_no",
			newChequeNo))){
					sql4="insert into cheque_details(cheque_no,cheque_dated,letter_no,cheque_amount,letter_dated,cheque_issue_time) values('"+newChequeNo+"',to_date('"+revalidated_date+"','dd-mm-yyyy'),'"+letter_no+"',"+amount+",to_date('"+revalidated_date+"','dd-mm-yyyy'),now())";
					System.out.println("sql2::"+sql4);

				stmt.addBatch(sql4);
				System.out.println("cheque_details::"+updateChequeForm.getCmrfNo());

			
			if(!isExisted("cir","chequeno",newChequeNo)){
				sql5="insert into cir(chequeno,chequedate,chequeamount,to_whom_or_pay_to,issuedto,entrytime) values('"+newChequeNo+"',to_date('"+revalidated_date+"','dd-mm-yyyy'),"+amount+",'"+patname+"(Direct Beneficiary)',9,now())";				
			 System.out.println("sql3::"+sql5);
              stmt.addBatch(sql5);
 			 System.out.println("cir::"+flag);


			if(isExisted("rev_san","cmrf_no",cmrfNo)){
				sql6="insert into rev_san_log(cmrf_no,billdate,sanc_amt,cheque_no,bill_amt,billdate_updated_time,is_chq_reprinted,logged_timestamp,logged_ipaddress,logged_by,logged_remarks) select cmrf_no,billdate,sanc_amt,cheque_no,bill_amt,billdate_updated_time,is_chq_reprinted,now(), '" + updateChequeForm.getIpAddr() + "', '" + updateChequeForm.getUserId() + "', 'Cheque Revalidation and Reprinting' from rev_san where cmrf_no = '" + cmrfNo + "' ";
				System.out.println("sql4.1::"+sql6);
				stmt.addBatch(sql6);
				sql7="update rev_san set billdate = to_date('" + revalidated_date + "','dd-mm-yyyy'),cheque_no = '" + newChequeNo + "',billdate_updated_time = now(), bill_amt = '"+amount+"', sanc_amt = '"+amount+"', is_chq_reprinted = true  where cmrf_no = '"+cmrfNo+"' ";
			 	System.out.println("sql4.2::"+sql7);
                stmt.addBatch(sql7);

				if (isExisted("cmrelief", "cmrf_no", cmrfNo)) {
					sql8 = "insert into cmrelief_log (cmrf_dt, pat_name, father_son_of, pat_address, pat_mandal, pat_district, pat_caste, white_cardno, hosp_code, rec, endorsement_old, req_amt, sanc_date, payment_to, ex_gratia, rejected, cmp_date, revenue_no, revenue_date, prevsanc, prevdate, year001, prevapplnos, det_diseases, doc_name, doc_design, type_est, letter_date, proceedings, cheque_no, cheque_dt, ref_tocm, cmp_no, endorsement, sms_status, email_status, cmrf_no, is_canceled, canceled_amount, proceedings_date, is_verified, is_reimbursement, age, prevcmpno, letter_no, economy, userid, enteredon, purpose, diseasetype, percentage, cmapproved, cmapprovedtime, colpurpose, psapproved1, psapproved2, loagenerated, chequeissued, recommended_by, rec_from, govt, sanc_amt, sined, priority, arogya_sri, remote_addr, time_stamp, inward_id, logged_timestamp, logged_ipaddress, logged_by, logged_remarks, is_chq_reprinted, chq_reprint_cnt)"
								 + " select cmrf_dt, pat_name, father_son_of, pat_address, pat_mandal, pat_district, pat_caste, white_cardno, hosp_code, rec, endorsement_old, req_amt, sanc_date, payment_to, ex_gratia, rejected, cmp_date, revenue_no, revenue_date, prevsanc, prevdate, year001, prevapplnos, det_diseases, doc_name, doc_design, type_est, letter_date, proceedings, cheque_no, cheque_dt, ref_tocm, cmp_no, endorsement, sms_status, email_status, cmrf_no, is_canceled, canceled_amount, proceedings_date, is_verified, is_reimbursement, age, prevcmpno, letter_no, economy, userid, enteredon, purpose, diseasetype, percentage, cmapproved, cmapprovedtime, colpurpose, psapproved1, psapproved2, loagenerated, chequeissued, recommended_by, rec_from, govt, sanc_amt, sined, priority, arogya_sri, '" + updateChequeForm.getIpAddr() + "', now(), inward_id, now(), '" + updateChequeForm.getIpAddr() + "', '" + updateChequeForm.getUserId() + "', 'Generate Letter to Hospital', is_chq_reprinted, chq_reprint_cnt "
								 + " from cmrelief where cmrf_no='"+cmrfNo+"'  and payment_to='B' and proceedings='Y' and (letter_no is not null)";
					  System.out.println("sql5.1::"+sql8);
						 stmt.addBatch(sql8);    
					
						chq_reprint_count += 1;
					 sql9="update cmrelief set loagenerated='t',letter_no='"+letter_no+"'," +"letter_date=to_date('"+revalidated_date+"','dd-mm-yyyy'), is_chq_reprinted = true, chq_reprint_cnt = "+chq_reprint_count+" where cmrf_no='"+cmrfNo+"'  and payment_to='B' and proceedings='Y' and (letter_no is not null) ";
										 System.out.println("sql5.2::"+sql9);
					 stmt.addBatch(sql9);

						if (isExisted("cheque_history_details", "new_cheque_no::text", newChequeNo)) {
						sql10 = "INSERT INTO public.cheque_history_details_log(cmrf_no, cheque_no, cheque_date, revaildated_date, corrected_name, is_issued_new_cheque, new_cheque_no, remarks, entered_on, "
							+ " entered_by, ip_address, is_esigned, esigned_date, corrected_account_no, so_approved_on, so_approved_ip, so_approved, so_approved_by, is_chq_reprinted, logged_timestamp,logged_remarks,logged_ipaddress,logged_by) select chd.cmrf_no, chd.cheque_no, chd.cheque_date, revaildated_date, corrected_name, is_issued_new_cheque, "
							+ " new_cheque_no, remarks, chd.entered_on, chd.entered_by, chd.ip_address, chd.is_esigned, chd.esigned_date, chd.corrected_account_no, chd.so_approved_on, chd.so_approved_ip, chd.so_approved, chd.so_approved_by, chd.is_chq_reprinted, now(), 'Final Cheque Reprint - Update is_chq_reprinted flag', '" + updateChequeForm.getIpAddr() + "', '" + updateChequeForm.getUserId() + "' from cheque_history_details chd "
							+ " join cmrelief c on c.cmrf_no = chd.cmrf_no "
							+ " where chd.new_cheque_no = "+newChequeNo+" and chd.so_approved is true and is_issued_new_cheque = 'true' and chd.is_chq_reprinted = 'false' and chd.is_esigned = 'true'";
						System.out.println("sql6.1::"+sql10);
							stmt.addBatch(sql10);    
						
						sql11="update cheque_history_details set is_chq_reprinted = true, chq_reptinted_date = CURRENT_TIMESTAMP where new_cheque_no="+newChequeNo+" and so_approved is true and is_issued_new_cheque = 'true' and is_chq_reprinted = 'false' and is_esigned = 'true'";
							System.out.println("sql6.2::"+sql11);
						stmt.addBatch(sql11);
						
					}else{
						response.put("flag", false);
						response.put("message", "Record Not Found in cheque history details to update");
						// flag = false;
						return response;
					}
					
				}else{
					response.put("flag", false);
					response.put("message", "Record not found with CMRF No.");
					// flag = false;
					return response;
				}
            }
			else
			{
				response.put("flag", false);
				response.put("message", "Record not found with CMRF No.");
			// flag=false;
				return response;
			}
			            
			}
			else
			{
				response.put("flag", false);
				response.put("message", "Cheque already exists with another record.");
			// flag=false;
				return response;
			}
			}
			else
			{
				response.put("flag", false);
				response.put("message", "Cheque already exists with another record.");
			// flag=false;
				return response;
			}
			}
			else
			{
				response.put("flag", false);
				response.put("message", "New Cheque Number already exists.");
			// flag=false;
				return response;
			}

			int[] updateCounts=stmt.executeBatch();
            System.out.println("updateCounts::"+updateCounts);
			// flag=true;
			for (int i = 0; i < updateCounts.length; i++) {
				String status = (updateCounts[i] >= 0) 
								? "Executed successfully (" + updateCounts[i] + " rows affected)"
								: "Execution failed";
				System.out.println("Statement " + (i + 1) + ": " + status);
			}
			// for (int result : updateCounts) {
            //     if (result == Statement.EXECUTE_FAILED) {
            //         flag = false;
            //         break;
            //     }
            // }
			flag = Arrays.stream(updateCounts).allMatch(count -> count >= 0);
			message = flag ? "Records updated successfully" : "Some updates failed";
			System.out.println("After all updates Flag is : "+flag);
			//conn.commit();
			}else{
				response.put("flag", false);
				response.put("message", "Record not found with CMRF No.");
				// flag=false;
				return response;
			}
			}
			catch (BatchUpdateException e) {
				e.printStackTrace();
				System.err.println("Batch update failed: " + e.getMessage());
				response.put("flag", false);
				response.put("message", "Batch update failed: " + e.getMessage());
				return response;
			}
			catch (SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			finally {
				try {
					conn.close();
					rs.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
			}
			response.put("flag", flag);
			response.put("message", message);
			return  response;
		}
			public boolean isExisted(String table, String column, String search_string) throws Exception {
		        String SQL = "select count(*) from " + table + " where " + column + " like '" + search_string + "'";
		        Connection conn=null;
		        Statement stmt=null;
		        boolean flag=false;
		        conn = dataSource.getConnection();
				stmt=conn.createStatement();

		        String temp=CommonUtils.getStringfromQuery(conn, SQL);
		       // System.out.println(temp+":::isExisted SQL:::::"+SQL);
		        if(!temp.equals("0")) {
		        	flag=true;
		        }
		            System.out.println("isExisted SQL********************************************************************************:::::"+SQL);
		        return flag;
		    }
		
		@Override
		public boolean checkChequeNoIsExists(String newChequeNo) throws Exception {
			Connection con = null;
			PreparedStatement pst = null;
			ResultSet rs = null;
			boolean exists = false;

			try {
				con = dataSource.getConnection();
				String query = "SELECT COUNT(*) AS count FROM cheque_details WHERE cheque_no = ?";
				pst = con.prepareStatement(query);
				pst.setString(1, newChequeNo);
				rs = pst.executeQuery();
				System.out.println("QUERY : " + query);
				if (rs.next()) {
					exists = rs.getInt("count") > 0;
				}
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				try {
					if (rs != null) rs.close();
					if (pst != null) pst.close();
					if (con != null) con.close();
				} catch (Exception ex) {
					ex.printStackTrace();
				}
			}

        	return exists;
		}


}

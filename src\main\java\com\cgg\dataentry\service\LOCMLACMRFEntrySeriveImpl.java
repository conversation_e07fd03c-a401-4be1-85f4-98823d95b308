package com.cgg.dataentry.service;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.dataentry.dao.LOCMLACMRFEntryDao;
import com.cgg.dataentry.model.LOCMLACMRFEntryModel;
import com.cgg.dataentry.model.Treatments;

@Service
public class LOCMLACMRFEntrySeriveImpl implements LOCMLACMRFEntryService {
	
	@Autowired
	LOCMLACMRFEntryDao Dao;

	@Override
	public List<LOCMLACMRFEntryModel> getRecommendedDetails(HttpSession session) throws Exception {
		return Dao.getRecommendedDetails(session);
	}

	@Override
	public List<LOCMLACMRFEntryModel> getHospitalDetails(HttpSession session) throws Exception {
		return Dao.getHospitalDetails(session);
	}

	@Override
	public String saveLocMlaCmrfEntry(LOCMLACMRFEntryModel locEntryForm,HttpServletRequest request) throws Exception {
		return Dao.saveLocMlaCmrfEntry(locEntryForm,request);
	}
	
	@Override
	public String saveMlaLocCmrfEntry(LOCMLACMRFEntryModel locEntryForm,HttpServletRequest request) throws Exception {
		return Dao.saveMlaLocCmrfEntry(locEntryForm,request);
	}

	@Override
	public List<LOCMLACMRFEntryModel> getLocData(String loc_token) throws Exception {
		return Dao.getLocData(loc_token);
	}

	@Override
	public boolean updateLocDetails(LOCMLACMRFEntryModel locEntryForm) {
		return Dao.updateLocDetails(locEntryForm);
	}
	public List<LOCMLACMRFEntryModel> getDistricts()throws Exception{
		
		return Dao.getDistricts();
	}
	
	public List<LOCMLACMRFEntryModel> getMandals(String distCode)throws Exception{
			
			return Dao.getMandals(distCode);
		}

	@Override
	public List<Treatments> getHealthCareServices() throws Exception {
		return Dao.getHealthCareServices();
	}

	@Override
	public List<Treatments> getSubTreatmentsByHcsId(Integer treatParId) throws Exception {
		return Dao.getSubTreatmentsByHcsId(treatParId);
	}

	@Override
	public List<Treatments> getProceduresBySubId(Integer treatParId,Integer treatSubId) throws Exception {
		return Dao.getProceduresBySubId(treatParId,treatSubId);
	}
}

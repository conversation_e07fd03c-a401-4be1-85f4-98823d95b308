package com.cgg.common.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.common.dao.CommonDao;
import com.cgg.reports.model.CmpReport;

@Service
public class CommonServiceImpl implements CommonService{

	@Autowired	
	private CommonDao commonDao;
	
	public List<CmpReport> getHospitalList() throws Exception{
		return commonDao.getHospitalList();		
	}
	
	public List<CmpReport> getRecommendedByList() throws Exception{
		return commonDao.getRecommendedByList();		
	}
	
	public List<CmpReport> getDistrictList() throws Exception{
		return commonDao.getDistrictList();
	}
	
	public List<CmpReport> getMandals(String cmrf_no) throws Exception{
		return commonDao.getMandals(cmrf_no);
	}
	
	public String getDistName(int distcode) throws Exception{
		return commonDao.getDistName(distcode);
	}
	
	//ajax mandal
	public String getAjaxMandals(String distCode) throws Exception{
		return commonDao.getAjaxMandals(distCode);
	}
}

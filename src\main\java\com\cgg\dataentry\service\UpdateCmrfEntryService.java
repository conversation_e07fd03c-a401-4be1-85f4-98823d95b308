package com.cgg.dataentry.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.cgg.dataentry.model.UpdateCmrfEntryForm;

public interface UpdateCmrfEntryService {
	public List<UpdateCmrfEntryForm> getRecommendedDetails() throws Exception;
	public List<UpdateCmrfEntryForm> getHospitalList() throws Exception;
	public List<UpdateCmrfEntryForm> getDistricts() throws Exception;

	public int  updateCmrfDetails(UpdateCmrfEntryForm updateCmrfForm, Map<String, Object> model,HttpServletRequest request);
	public List<UpdateCmrfEntryForm> getMandals(String distCode) throws Exception;
	public String getInwardData(String distCode) throws Exception;
	public String getTokenBankData(String tokenNo) throws Exception;
	public String getCmrfLocData(String cmrfLocNo) throws Exception;
	public String getCmrfData(String cmrfVal) throws Exception;
	public UpdateCmrfEntryForm getCmrfValues(String cmrfNo) throws Exception;
	public List<UpdateCmrfEntryForm> getOtherConsList() throws Exception;


}

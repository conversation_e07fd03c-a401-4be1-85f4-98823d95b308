package com.cgg.dataentry.dao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.cgg.common.CommonUtils;
import com.cgg.dataentry.model.SendData;

@Repository
public class SendDataDaoImpl implements SendDataDao{

	@Autowired
	private DataSource dataSource;
	
	@Autowired
	private JdbcTemplate jdbcTemplate;
	
	
	public List<SendData> getCmpNos() throws Exception
	{
		String sql="";
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		List<SendData> CmpNosList = new ArrayList<SendData>(); 
		try
		{
			con = dataSource.getConnection();
			st = con.createStatement();
			sql = "select distinct cmp_no,cmp_date from cmrelief where (proceedings is null or proceedings='') and cmp_date is not null and cmp_no is not null and cmp_no!=''  and cmp_date>=to_date('01-10-2005','dd-mm-yyyy') order by cmp_date desc";
			rs = st.executeQuery(sql);
			while(rs.next())
			{
				SendData sd = new SendData();
				sd.setCmpNo(rs.getString("cmp_no"));
				sd.setCmpDate(rs.getString("cmp_date"));
				CmpNosList.add(sd);
			}
		}
		catch (Exception e) 
		{
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		
		
		return CmpNosList;
	}


	@Override
	public int update(String controlNo,HttpServletRequest request) throws Exception {
		int result = 0;
		String ipAddress = request.getRemoteAddr();
		HttpSession session=request.getSession();
		String userId=(String)session.getAttribute("userid");
		String sql = "";
		
		sql = "update cmrelief set proceedings='Y',proceedings_date=current_date ," + "updated_by = '" + userId
				+ "', updated_on = '" + LocalDateTime.now() + "', " + "ip_address = '" + ipAddress + "' "
				+ "where cmp_no=? and sanc_amt!=0";
		result = jdbcTemplate.update(sql, controlNo);
		
		return result;
	}
}

package com.cgg.reports.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.cgg.reports.model.CMRFEntryDetailsReport;

@Repository
public class CmrfFraudDetailsReportDao {

	@Autowired
	private DataSource dataSource;

	@Autowired
	JdbcTemplate jdbcTemlate;

	public List<CMRFEntryDetailsReport> getCmrfFraudDetailsReport(CMRFEntryDetailsReport cmrfEntryDetailsReport)throws Exception {
		
		Connection con = null;
		PreparedStatement pst = null;
		ResultSet rs = null;
		List<CMRFEntryDetailsReport> cmrfEntryDetailsReportList = new ArrayList<CMRFEntryDetailsReport>();
		String sql = null;
		
		try {
			con = dataSource.getConnection();
			String cond = "";
			
			String cmrfDate = cmrfEntryDetailsReport.getCmrfDate();
			String cmrfToDate = cmrfEntryDetailsReport.getCmrfToDate();
			
			if (cmrfToDate != null && !cmrfToDate.isEmpty()) {
				cond = cond + " and entered_timestamp::date<=to_date('" + cmrfToDate + "','dd/mm/yyyy') ";				
			}

			sql = " SELECT name,mobile_no,fraud_details,"
					+ " TO_CHAR(entered_timestamp, 'DD-MM-YYYY HH:MI:SS AM') AS formatted_timestamp "
					+ " FROM report_fraud WHERE entered_timestamp::date>=to_date('" + cmrfDate + "','dd/mm/yyyy') "
					+ cond + " order by id ";

			System.out.println("getCmrfFraudDetailsReport -- sql :: " + sql);

			pst = con.prepareStatement(sql);
			rs = pst.executeQuery();
			while (rs.next()) {
				CMRFEntryDetailsReport cmrfEntryDetails = new CMRFEntryDetailsReport();
				cmrfEntryDetails.setName(rs.getString("name"));
				cmrfEntryDetails.setMobileNo(rs.getString("mobile_no"));
				cmrfEntryDetails.setFraudDetails(rs.getString("fraud_details"));
				cmrfEntryDetails.setEnteredDateTime(rs.getString("formatted_timestamp"));
				cmrfEntryDetailsReportList.add(cmrfEntryDetails);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (pst != null) {
				pst.close();
				pst = null;
			}
			if (rs != null) {
				rs.close();
				rs = null;
			}
			if (con != null && !con.isClosed()) {
				con.close();
				con = null;
			}
		}
		return cmrfEntryDetailsReportList;
	}
}
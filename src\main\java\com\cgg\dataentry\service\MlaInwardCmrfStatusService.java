package com.cgg.dataentry.service;

import java.util.Optional;

import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.cgg.common.Response;
import com.cgg.dataentry.entities.MlaCmrfM;
import com.cgg.dataentry.repositories.MlaInwardCmrfStatusRepository;

@Service
public class MlaInwardCmrfStatusService {
	@Autowired
	MlaInwardCmrfStatusRepository mlaInwardCmrfEditRepository;
	
	
	public Response getAllStagesStatus(String tokenHash,HttpServletRequest request) {
 		Response response = new Response();
 		
 		
  		try {
	 		Optional<MlaCmrfM> cmrfRecord=mlaInwardCmrfEditRepository.findBytokenHash(tokenHash);
	 		
			 if(!cmrfRecord.isPresent()) {
				response.setStatus(HttpStatus.EXPECTATION_FAILED);
				response.setMessage("No Records Found");
				return response;
			 }
			 
			 response.setData(cmrfRecord.get());
			 response.setStatus(HttpStatus.OK);
			 
			} catch (Exception e) {
				e.printStackTrace();
				response.setStatus(HttpStatus.EXPECTATION_FAILED);
				response.setMessage("Something went wrong");
				return response;
			} 
		return response;
	}
}

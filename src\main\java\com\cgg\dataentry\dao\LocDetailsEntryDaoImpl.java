package com.cgg.dataentry.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.cgg.dataentry.model.LocDetailsEntryForm;
import com.cgg.common.CommonFunctions;
import com.cgg.common.CommonUtils;

@Repository
public class LocDetailsEntryDaoImpl implements LocDetailsEntryDao{
	@Autowired
	private DataSource dataSource;
	
	@Autowired
	JdbcTemplate jdbcTemlate;
	
	public List<LocDetailsEntryForm> getRecommendedDetails() throws Exception{
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<LocDetailsEntryForm> locEntryDtls = new ArrayList<LocDetailsEntryForm>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			//sql="select cno,mlamp||design||'('||party||'), '||cname as cname from  constituency  order by mlamp,cno";
			sql=" select cno,case when  party is not null then  mlamp||design||'('||party||'), '||cname else "+ 
				 " mlamp||' '||design||', '|| cname end as cname from  constituency  where active_con=true order by mlamp,cno ";
			rs=st.executeQuery(sql);
			while(rs.next()) {
				LocDetailsEntryForm locEntry = new LocDetailsEntryForm();
				locEntry.setConstNo(rs.getString("cno"));
				locEntry.setConstName(rs.getString("cname"));
				locEntryDtls.add(locEntry);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return locEntryDtls;
	}
	public List<LocDetailsEntryForm> getHospitalList() throws Exception{
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		String cond="";
		List<LocDetailsEntryForm> hospDetails = new ArrayList<LocDetailsEntryForm>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			cond = cond+" and hospcode in('2','411','56','3','214','548') ";
			sql="select hospcode, hospname||'('||recog||')' as hospname from  hospital where delete_flag='false' "+cond+" order by hospname";
			rs=st.executeQuery(sql);
			while(rs.next()) {
				LocDetailsEntryForm locEntry = new LocDetailsEntryForm();
				locEntry.setHospCode(rs.getString("hospcode"));
				locEntry.setHospName(rs.getString("hospname"));
				hospDetails.add(locEntry);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return hospDetails;
	}
	@Override
	public String saveLocDetails(LocDetailsEntryForm formbean, Map<String, Object> model)throws Exception {
		// TODO Auto-generated method stub
		System.out.println("dap");
		Connection	con=null;
		Statement	st=null;
		boolean insFlag=false;
		String msg=null;
		ResultSet rs=null;
		int seqNo = 0;
		String locNo=null;
		try {
		con = dataSource.getConnection();
		st=con.createStatement();
		String userId=null;	
		String updQry=null;
		String qry=null;
		userId=(String)formbean.getUserId();
		int year = 0;
		Map<String,Object> insertMap = new HashMap<String,Object>();
		String sql=null;	
		sql ="select coalesce(max(int4(split_part(loc_no,'/',1))+1),1) from loc_cmrf where split_part(loc_no,'/',3)=to_char(current_date,'yyyy')";
        System.out.println("1---"+sql);
        
//String var1 = getOne(con,sql);
        String var1 = CommonUtils.getStringfromQuery(con,sql);
		System.out.println("var1----"+var1);
		String currYear = new SimpleDateFormat("yyyy").format(new Date());
		System.out.println("currYear--------"+currYear);
		 locNo=var1+"/CMRF-LOC/"+currYear;
		System.out.println("loc--"+ locNo);
		formbean.setLocNo(locNo);
		String recommdBy=null;
		String cmcoReferredBySqlCol="";
//var1 = var1 + 6;
//String loc_no=var1+1+currYear;
 		insertMap.put("loc_no","'"+ locNo+"'");
		
	//	insertMap.put("cmrf_dt","to_date('"+formbean.getCmrfDate()+"','dd/mm/yyyy')");
		
		
		if(CommonFunctions.validateData(formbean.getPatientName()))
			insertMap.put("patient_name","'"+formbean.getPatientName()+"'");
		
		if(CommonFunctions.validateData(formbean.getFatherName()))
			insertMap.put("father_name","'"+formbean.getFatherName()+"'");

		if(CommonFunctions.validateData(formbean.getAadharNo()))
			insertMap.put("aadhaar_no","'"+formbean.getAadharNo()+"'");

		if(CommonFunctions.validateData(formbean.getMobileNo()))
			insertMap.put("mobile_no","'"+formbean.getMobileNo()+"'");
		
		if(CommonFunctions.validateSelectBox(formbean.getPatDistrict()))
			insertMap.put("pat_district","'"+formbean.getPatDistrict()+"'");
		
		if(CommonFunctions.validateSelectBox(formbean.getAddress()))
			insertMap.put("address","'"+formbean.getAddress()+"'");
		
		if(CommonFunctions.validateSelectBox(formbean.getAssuredAmt()))
			insertMap.put("assured_amt","'"+formbean.getAssuredAmt()+"'");
		
		if(CommonFunctions.validateData(formbean.getHospCode()))
			insertMap.put("hosp_code","'"+formbean.getHospCode()+"'");
		
		if(CommonFunctions.validateData(formbean.getPurpose()))
			insertMap.put("purpose","'"+formbean.getPurpose()+"'");	
		
			/*
			 * if(CommonFunctions.validateData(formbean.getU))
			 * insertMap.put("user_id","'"+formbean.getPatientIpNo()+"'");
			 */
		
		if(CommonFunctions.validateData(formbean.getHospCode()))
			insertMap.put("hospital_name","'"+formbean.getTxthospname()+"'");
		
		if(CommonFunctions.validateData(formbean.getRecommendedBy()))
			insertMap.put("recommended_by","'"+formbean.getRecommendedBy()+"'");
		
		recommdBy=formbean.getRecommendedBy();		
		if ("999".equals(recommdBy)) {
		    if (CommonFunctions.validateData(formbean.getCmcoReferredBy())) {
		        insertMap.put("cmco_referred_by", "'" + formbean.getCmcoReferredBy() + "'");
		        cmcoReferredBySqlCol = " ,cmco_referred_by='" + formbean.getCmcoReferredBy() + "' ";
		    }
		}
		
		if(CommonFunctions.validateData(userId))
			insertMap.put("user_id","'"+userId+"'");
		
		insertMap.put("vip_letter_dt","to_date('"+formbean.getVipLetterDate()+"','dd/mm/yyyy')");
		insertMap.put("ipaddress","'"+formbean.getIpAddress()+"'");
		String insquery=null;
		synchronized (this) {
			Map<String,Object> tmap = CommonFunctions.insertQuery(insertMap);
			if(tmap!=null && !tmap.isEmpty())
				 insquery = "insert into loc_cmrf ("+(tmap.get("colNames"))+") values ("+tmap.get("colValues")+")";
			System.out.println("insquery---"+insquery);

			 insFlag = CommonUtils.insertQuery(con, insquery);
		}
	    if(insFlag) {
	    	//update
        	qry = "insert into loc_mla_cmrf_log(mla_loc_no, patient_name, "
    				+ "father_son_of,aadhaar_no,mobile_no, pat_district, purpose, assured_amt, recommended_by, hosp_code, status, loc_no, entered_on,"
    				+ " delete_flag, updated_on, rej_reasons, address, vip_letter_dt,ip_address,logged_timestamp,logged_ipaddress,logged_by,"
    				+ "logged_remarks,updated_by,cmco_referred_by)"
    				+ "select mla_loc_no, patient_name, "
    				+ "father_son_of,aadhaar_no,mobile_no, pat_district, purpose, assured_amt, recommended_by, hosp_code, status, loc_no, entered_on,"
    				+ " delete_flag, updated_on, rej_reasons, address, vip_letter_dt,ip_address,now(),'"+formbean.getIpAddress()+"','"+userId+"'"
    						+ ",'Edit Loc',updated_by,cmco_referred_by " 
    						+ " from loc_mla_cmrf where mla_loc_no = '"+formbean.getLocTokenSno()+ formbean.getLocToken()+"'" ;
          
            System.out.println("In updateDB upd"+qry);
            int res = st.executeUpdate(qry);           
            if(res>0)
            {
	    	 updQry="update loc_mla_cmrf set status='3',loc_no='" +locNo+"',updated_by='"+userId+"' ,updated_on=now() "+cmcoReferredBySqlCol+" where mla_loc_no='" +formbean.getLocTokenSno()+ formbean.getLocToken()+ "' and status in ('1','9')";
            }
            System.out.println("query is sql2" + updQry);
		 boolean updFlag = CommonUtils.updateQuery(con, updQry);
            
	    }else {
	    	//msg="Insertion Failed";
	    }
		System.out.println("qry---"+qry);
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		
		return locNo;
	}
	
	
	public String getLocData(String locTokenNo) throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		String locStr=null;
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			 sql = "select patient_name||':#'||father_son_of||':#'||aadhaar_no ||':#'||mobile_no ||':#'||coalesce(pat_district,'0') ||':#'||address||':#'||purpose||':#'||coalesce(assured_amt,0)||':#'||recommended_by||':#'||to_char(vip_letter_dt,'dd/mm/yyyy')||':#'||hosp_code||':#'||COALESCE(loc_no, '0')||':#'||status ||':#'||COALESCE(cmco_referred_by, '')  as locStr from loc_mla_cmrf  "
	            		+ "where mla_loc_no='" + locTokenNo + "' "
						// + "and status in ('1','9') "
						+ "and  delete_flag=false";
			System.out.println("sql---"+sql);
			rs=st.executeQuery(sql);
			while(rs.next()) {
				locStr=rs.getString("locStr");
				
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return locStr;

}
	public LocDetailsEntryForm getLocLetterData(String locNo) throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		String locStr=null;
		LocDetailsEntryForm locEntry = new LocDetailsEntryForm();
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
            sql = " SELECT user_id,loc_no, patient_name, father_name, address, assured_amt, case when hospital_name is null then hospname else hospital_name end as hospname,  "
            		+ " to_char(time_stamp,'dd-mm-yyyy') as letterDate,mlamp , case when minister='Y' then 'Hon''ble ' else '' end ||cname as vipdesig,to_char(vip_letter_dt,'dd-mm-yyyy') as vipletterdate,"
            		+ "prefix , case when purpose is not null then purpose else null end as purpose,L.recommended_by,L.cmco_referred_by from  public.loc_cmrf  L inner join hospital h on l.hosp_code=h.hospcode left join constituency c on c.cno=recommended_by::int2 "
            		+ " where loc_no='"+locNo+"'";
			System.out.println("sql---"+sql);
			rs=st.executeQuery(sql);
			if(rs.next()) {
				locEntry.setLocNo(rs.getString("loc_no"));
				locEntry.setPatientName(rs.getString("patient_name"));
				locEntry.setFatherName(rs.getString("father_name"));
				locEntry.setAddress(rs.getString("address"));
				locEntry.setAssuredAmt(rs.getString("assured_amt"));
				locEntry.setHospName(rs.getString("hospname"));
				locEntry.setLetterDate(rs.getString("letterDate"));
				locEntry.setVipName(rs.getString("mlamp"));
				locEntry.setVipDesg(rs.getString("vipdesig"));
				locEntry.setVipLetterDate(rs.getString("vipletterdate"));
				locEntry.setPrefix(rs.getString("prefix"));
				locEntry.setHospName(rs.getString("hospname"));
				locEntry.setFormatAmt(CommonUtils.convert(rs.getString("assured_amt")));
				locEntry.setAmtInWords(CommonUtils.inWords(rs.getString("assured_amt")));
				locEntry.setPurpose(rs.getString("purpose"));
				locEntry.setRecommendedBy(rs.getString("recommended_by"));
				
				if ("999".equals(locEntry.getRecommendedBy())) {
					locEntry.setCmcoReferredBy(rs.getString("cmco_referred_by"));
				}	
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return locEntry;

	}

	@Override
	public List<LocDetailsEntryForm> getDistricts() throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<LocDetailsEntryForm> districts = new ArrayList<LocDetailsEntryForm>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			sql="select distno, distname from  district  order by distname";
			System.out.println("sql---"+sql);
			rs=st.executeQuery(sql);
			while(rs.next()) {
				LocDetailsEntryForm mlaInwardLoc = new LocDetailsEntryForm();
				mlaInwardLoc.setDistNo(rs.getString("distno"));
				mlaInwardLoc.setDistName(rs.getString("distname"));
				districts.add(mlaInwardLoc);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return districts;
	}
	
	@Override
	public List<Map<String, Object>> getCmrfLocDetailsByAadhar(String uid, String locNo) throws Exception {
		//System.out.println("uid--"+uid+"---locNo--"+locNo);
		 List<Map<String, Object>> entries = new ArrayList<>();
		 String countQuery = "SELECT COUNT(*) FROM loc_cmrf WHERE assured_amt !=0 AND aadhaar_no = ? AND loc_no <> ? AND time_stamp >= CURRENT_DATE - INTERVAL '6 months' ";
	     
		 try (Connection con = dataSource.getConnection();
		        PreparedStatement countStmt = con.prepareStatement(countQuery)) {
		        countStmt.setString(1, uid);
		        countStmt.setString(2, locNo);
		        try (ResultSet rs = countStmt.executeQuery()) {
		            if (rs.next() && rs.getInt(1) == 0) {
		                return entries;
		            }
		        }
		   		   
		        String dataQuery = " SELECT a.loc_no, a.patient_name, a.father_name,a.assured_amt "
		        		+ " FROM  loc_cmrf a "
		        		+ " INNER JOIN loc_mla_cmrf b ON b.loc_no = a.loc_no "
		        		+ " WHERE  a.aadhaar_no = ? AND a.loc_no <> ? AND a.assured_amt !=0 AND a.time_stamp >= CURRENT_DATE - INTERVAL '6 months' ORDER BY a.time_stamp ";		        		
                //System.out.println("dataQuery----"+dataQuery);
		        
		        try (PreparedStatement dataStmt = con.prepareStatement(dataQuery)) {
		            dataStmt.setString(1, uid);
		            dataStmt.setString(2, locNo);
		            try (ResultSet dataRs = dataStmt.executeQuery()) {
		                while (dataRs.next()) {
		                    Map<String, Object> entry = new HashMap<>();
		                    entry.put("locNo", dataRs.getString("loc_no"));
		                    entry.put("patientName", dataRs.getString("patient_name"));
		                    entry.put("fatherSonOf", dataRs.getString("father_name"));
		                    entry.put("assuredAmt", dataRs.getString("assured_amt"));
		                    entries.add(entry);
		                }
		            }
		        }
		 }catch (SQLException e) {
		     e.printStackTrace();
		 }
	   return entries; // Return the list of entries (could be empty)
	}
	
		
}

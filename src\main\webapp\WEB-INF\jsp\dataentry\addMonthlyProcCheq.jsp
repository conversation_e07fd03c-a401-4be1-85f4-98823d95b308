<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<title>Monthly Processed CMRF Cheques Entry</title>

<script type="text/javascript" src="js/jquery-1.12.4.js"></script>
<script type="text/javascript" src="js/common/commonValidations.js"></script>
<script type="text/javascript" src="js/jquery-ui.js"></script>
<link rel="stylesheet" href="css/jquery-ui.css">
 <style>
        body { font-family: Arial, sans-serif;}
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input[type="file"], input[type="text"], select { width: 100%; padding: 8px; }
        input[type="submit"] { background: #4CAF50; color: white; padding: 10px 15px; border: none; cursor: pointer; }
        input[type="submit"]:hover { background: #45a049; }
        .form-row { display: flex; gap: 1rem; }
        .form-row .form-group { flex: 1; }
        .error { color: red; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container complete_wrap" style="background: white; border-top: 2px solid #0b4574; margin: 1% auto;">
        <div class="container text-center">
            <h3>Monthly Processed CMRF Cheques Entry</h3>
        </div>

        <div class="container">
            <p style="color: red; font-size: 20px; font-weight: bold;">${success}</p>
            <p style="color: green; font-size: 20px; font-weight: bold;">${error}</p>
            <form id="uploadForm" action="uploadMonthlyCheque" method="post" enctype="multipart/form-data" onsubmit="return validateForm()">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="month">Month:</label>
                        <select id="month" name="month" required>
                            <option value="">-- Select Month --</option>
                            <option value="01">January</option>
                            <option value="02">February</option>
                            <option value="03">March</option>
                            <option value="04">April</option>
                            <option value="05">May</option>
                            <option value="06">June</option>
                            <option value="07">July</option>
                            <option value="08">August</option>
                            <option value="09">September</option>
                            <option value="10">October</option>
                            <option value="11">November</option>
                            <option value="12">December</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="year">Year:</label>
                        <select name="year" id="year" required>
                            <option value="">-- Select Year --</option>
                            <option value="2025">2025</option>
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                            <option value="2021">2021</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="excelFile">Excel File:</label>
                    <input type="file" id="excelFile" name="excelFile" accept=".xlsx,.xls" required>
                </div>

                <p id="errorMessage" class="error"></p>

                <input type="submit" value="Upload and Process">
            </form>
        </div>
    </div>

    <script>
        // Pre-fill year with current year
        const currentYear = new Date().getFullYear();
        document.getElementById("year").value = currentYear;

        function validateForm() {
            const year = document.getElementById("year").value.trim();
            const fileInput = document.getElementById("excelFile");
            const error = document.getElementById("errorMessage");

            error.textContent = "";

            // Year must be 4 digits and current year
            if (!/^\d{4}$/.test(year)) {
                error.textContent = "Year must be a 4-digit number.";
                return false;
            }

            // const currentYear = new Date().getFullYear();
            // if (parseInt(year) !== currentYear) {
            //     error.textContent = "Year must be the current year (" + currentYear + ").";
            //     return false;
            // }

            // File checks
            const file = fileInput.files[0];
            if (!file) {
                error.textContent = "Please select a file.";
                return false;
            }

            // File extension check
            const ext = file.name.split('.').pop().toLowerCase();
            if (ext !== "xlsx" && ext !== "xls") {
                error.textContent = "Only .xlsx or .xls files are allowed.";
                return false;
            }

            // File size check (max 5MB)
            const maxSizeMB = 5;
            const maxSizeBytes = maxSizeMB * 1024 * 1024;
            if (file.size > maxSizeBytes) {
                error.textContent = "File size must not exceed 5MB.";
                return false;
            }
            Swal.fire({
                title: 'Processing Excel File',
                html: 'Please wait while we process your file...',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                willOpen: () => {
                    Swal.showLoading();
                }
            });
            return true;
        }
        window.addEventListener('load', function() {
            Swal.close();
        });
    </script>
<script src="js/sweetalert2/sweetalert2.js"></script>
</body>
</html>
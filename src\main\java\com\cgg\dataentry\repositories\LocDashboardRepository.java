package com.cgg.dataentry.repositories;

import java.util.List;

import javax.persistence.Tuple;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.cgg.dataentry.entities.LocMlaCmrf;

@Repository
public interface LocDashboardRepository extends JpaRepository<LocMlaCmrf, String> {

	@Query(value = "SELECT COUNT(1) AS total_count, " + "COUNT(CASE WHEN status = '1' THEN 1 END) AS loc_entered, "
			+ "COUNT(CASE WHEN status = '2' THEN 1 END) AS loc_pending, "
			+ "COUNT(CASE WHEN status = '4' THEN 1 END) AS loc_returned " + "FROM loc_mla_cmrf "
			+ "WHERE delete_flag = false and entered_on >= TO_DATE('07/12/2023', 'DD/MM/YYYY')", nativeQuery = true)
	List<Tuple> getLocPenRecRetCounts();

	@Query(value = "SELECT COUNT(1) FROM loc_cmrf l WHERE l.time_stamp >= TO_DATE('07/12/2023', 'DD/MM/YYYY') "
			+ " AND EXISTS (SELECT 1 FROM cmrelief c WHERE c.cmrf_loc = l.loc_no "
			+ " AND c.cmrf_loc IS NOT NULL AND c.cmrf_loc != '')", nativeQuery = true)
	long countLocReceived();
	
	@Query(value = "SELECT COUNT(1)  FROM loc_cmrf l  WHERE l.time_stamp >= TO_DATE('07/12/2023', 'DD/MM/YYYY')  and is_qr_scanned is true", nativeQuery = true)
	long countLocReceivedByHosp();

	@Query(value = "SELECT COUNT(l.loc_no) FROM loc_cmrf l JOIN cmrelief c ON l.loc_no = c.cmrf_loc "
			+ "JOIN rev_san r ON c.cmrf_no = r.cmrf_no WHERE c.cmrf_loc IS NOT NULL AND c.cmrf_loc != '' "
			+ "AND  l.time_stamp > TO_DATE('07/12/2023', 'DD/MM/YYYY') and c.esigned_date > to_date('07/12/2023', 'DD/MM/YYYY')", nativeQuery = true)
	long countLocCheckPrinted();

	@Query(value = "select  case when cno in(997,999,194) then mlamp else mlamp||'('||cname||')' end as recommendedby ,cno,count(1) count,sum(assured_amt) sanc_amt from loc_cmrf a "
			+ " left JOIN constituency c ON int4(c.cno) = int4(a.recommended_by) "
			+ " WHERE a.time_stamp >= TO_DATE('07/12/2023', 'DD/MM/YYYY') "
			+ " group by mlamp , cname, cno "
			+ " order by mlamp asc", nativeQuery = true)
    List<Tuple> getPRWiseApplications();

	@Query(value = "SELECT  case when cno in(997,999,194) then mlamp else mlamp||'('||cname||')' end as recommendedby, cno, COUNT(1) AS count, SUM(cm.sanc_amt) AS sanc_amt FROM loc_cmrf a " + 
			" LEFT JOIN constituency c ON CAST(c.cno AS INT) = CAST(a.recommended_by AS INT)  " + 
			"LEFT JOIN cmrelief cm ON cm.cmrf_loc = a.loc_no " + 
			"WHERE a.time_stamp > TO_DATE('07/12/2023', 'DD/MM/YYYY') AND " + 
			" cm.cmrf_loc IS NOT NULL AND cm.cmrf_loc != '' and cm.cmrf_loc !='0'  GROUP BY mlamp, cname, cno ORDER BY mlamp ASC;", nativeQuery = true)
    List<Tuple> getPRWiseReceivedApplications();

	@Query(value = "select case when cno in(997,999,194) then mlamp else mlamp||'('||cname||')' end as recommendedby ,cno,count(1) count,SUM(cm.sanc_amt) AS sanc_amt from loc_cmrf lc "
			+ " left join cmrelief cm on lc.loc_no = cm.cmrf_loc "
			+ " left join rev_san r on cm.cmrf_no = r.cmrf_no "
			+ " left JOIN constituency c ON int4(c.cno) = int4(lc.recommended_by) "
			+ " WHERE cm.cmrf_loc IS NOT NULL AND cm.cmrf_loc != '' "
			+ " AND lc.time_stamp >= TO_DATE('07/12/2023', 'DD/MM/YYYY') and cm.cmrf_no in (select cmrf_no from rev_san) "
			+ " group by mlamp , cname, cno "
			+ " order by mlamp asc", nativeQuery = true)
    List<Tuple> getPRWiseChqPrintedApplications();

	@Query(value = "select count(1) from loc_cmrf lc where lc.time_stamp >= to_date('07/12/2023', 'DD/MM/YYYY')",nativeQuery = true)
	long getTotalLocsCount();
	
	@Query(value = " SELECT CASE WHEN cno IN (997, 999, 194) THEN mlamp ELSE mlamp || '(' || cname || ')'  END AS recommendedby,  cno, "
			+ " COUNT(1) AS count, "
			+ " SUM(a.assured_amt) AS sanc_amt "
			+ " FROM loc_cmrf a "
			+ " LEFT JOIN constituency c ON CAST(c.cno AS INT) = CAST(a.recommended_by AS INT) "
			+ " LEFT join loc_mla_cmrf lmc on lmc.loc_no=a.loc_no "
			+ " WHERE "
			+ " a.time_stamp > TO_DATE('07/12/2023', 'DD/MM/YYYY') "
			+ " and lmc.status='10' "
			+ " GROUP BY mlamp, cname, cno  ORDER BY mlamp ASC;", nativeQuery = true)
    List<Tuple> getPRWiseReceivedByHospApplications();

}

package com.cgg.dataentry.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import com.cgg.dataentry.model.EnhancementLocDetails;
import com.cgg.dataentry.service.EnhancementLocDetailsEntryService;

@Controller
@RequestMapping(value = "/enhancementLocDetailsEntry")
public class EnhancementLocDetailsEntryController {
	@Autowired
	private EnhancementLocDetailsEntryService enhancelocEntryService;
	
	@RequestMapping(method = RequestMethod.GET)
    public String enhancementlocDetails(Map<String, Object> model,HttpSession session)throws Exception {
		String userId=null;
		if(session!=null && session.getAttribute("userid")!=null) 
			userId=(String)session.getAttribute("userid");
		 if(userId==null || userId.equals("null") ) {
				System.out.println("enhancementlocDetails"+userId);
			 return  "redirect:/enhancementlocDetails";
		  }
		List<EnhancementLocDetails> recommendedList = new ArrayList<EnhancementLocDetails>();
		List<EnhancementLocDetails> hospitalList = new ArrayList<EnhancementLocDetails>();
		EnhancementLocDetails locEntryForm = new EnhancementLocDetails();  
		recommendedList = enhancelocEntryService.getRecommendedDetails();
		hospitalList = enhancelocEntryService.getHospitalList();
		model.put("recommendedList",recommendedList);
		model.put("hospitalList",hospitalList);
        model.put("locEntryForm", locEntryForm);
         
        return "enhancementLocDetailsEntry";
    }
	@RequestMapping(method = RequestMethod.POST)
    public String saveLocDetails(@ModelAttribute("locEntryForm") EnhancementLocDetails locEntryForm,
            Map<String, Object> model,HttpSession session,HttpServletRequest request) throws Exception {
		System.out.println("ttttttttttttt");
		String userId=null;
		if(session!=null && session.getAttribute("userid")!=null) 
			userId=(String)session.getAttribute("userid");
//		System.out.println("ttttttttttttt"+userId);
		 if(userId==null || userId.equals("null") ) {
			 return  "redirect:/enhancementlocDetails";
		  }
		 locEntryForm.setUserId(userId);
		 locEntryForm.setIpAddress(request.getRemoteAddr());

			if(locEntryForm.getLocTokenSno()!=null) {
				if(locEntryForm.getHidHospCode()!=null)
				 locEntryForm.setHospCode(locEntryForm.getHidHospCode());
				if(locEntryForm.getHidrecommendedBy()!=null)
			     locEntryForm.setRecommendedBy(locEntryForm.getHidrecommendedBy());
			}
		
			String locNo=enhancelocEntryService.saveCmrfDetails(locEntryForm, model,request);
		  List<EnhancementLocDetails> recommendedList = new ArrayList<EnhancementLocDetails>();
		  List<EnhancementLocDetails> hospitalList = new ArrayList<EnhancementLocDetails>();
		  
		  recommendedList = enhancelocEntryService.getRecommendedDetails();
			hospitalList = enhancelocEntryService.getHospitalList();
		  model.put("recommendedList",recommendedList);
		  model.put("hospitalList",hospitalList);
		  locEntryForm=enhancelocEntryService.getLocLetterData(locNo,locEntryForm.getPrevLocNo());
		 // model.put("msg", msg); 
		  model.put("locEntryForm", locEntryForm);
		
         
        return "/dataentry/enhancementLocLetter";
    }
	@RequestMapping(value = "getLocData", method = RequestMethod.POST)
    public @ResponseBody String getLocData(HttpServletRequest request,@RequestParam("locTokenVal")String locTokenVal,HttpServletResponse response) {
		System.out.println("getLocData");
		String locData=null;
        try{
            if(locTokenVal!=null) {
            	locData=enhancelocEntryService.getLocData(locTokenVal);
                
            }

        } catch (Exception exception) {
            exception.printStackTrace();
        }

        return locData;
    }
	@RequestMapping(value = "getPreviousLocAmount", method = RequestMethod.POST)
    public @ResponseBody String getPreviousLocAmount(HttpServletRequest request,@RequestParam("prevLocNo")String prevLocNo,HttpServletResponse response) {
		System.out.println("getPreviousLocAmount");
		String locData=null;
        try{
            if(prevLocNo!=null) {
            	locData=enhancelocEntryService.getPreviousLocAmount(prevLocNo);
                
            }

        } catch (Exception exception) {
            exception.printStackTrace();
        }

        return locData;
    }
	
}

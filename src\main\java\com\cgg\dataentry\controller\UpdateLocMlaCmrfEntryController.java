package com.cgg.dataentry.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.cgg.dataentry.model.MlaCmrfEntryForm;
import com.cgg.dataentry.service.UpdateLocMlaCmrfService;

@Controller
@RequestMapping(value = "/updateLocMlaCmrfEntry")
public class UpdateLocMlaCmrfEntryController {
	
    @Autowired
	private UpdateLocMlaCmrfService updateLocMlaService;
	
	@RequestMapping(method = RequestMethod.GET)
    public String getLocMlaCmrf(Map<String, Object> model)throws Exception {
		
		MlaCmrfEntryForm updatemlaCmrfForm = new MlaCmrfEntryForm();  
	
        model.put("updatemlaCmrfForm", updatemlaCmrfForm);
         
        return "updateLocMlaCmrfEntry";
    }
	
	@RequestMapping(method = RequestMethod.POST)
    public String updateLocMlaCmrfDetails(@ModelAttribute("updatemlaCmrfForm") MlaCmrfEntryForm updatemlaCmrfForm,
            Map<String, Object> model,HttpServletRequest request) throws Exception {
		System.out.println("ttttttttttttt");
		
		  String mesg=updateLocMlaService.updateLocMlaCmrfDetails(updatemlaCmrfForm, model,request);
		
	     model.put("mesg", mesg);
        model.put("updatemlaCmrfForm", updatemlaCmrfForm);
         
        return "updateLocMlaCmrfEntry";
    }
	

}


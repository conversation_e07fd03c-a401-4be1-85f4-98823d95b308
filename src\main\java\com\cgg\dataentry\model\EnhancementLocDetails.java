package com.cgg.dataentry.model;

public class EnhancementLocDetails {
private String patientName;
private String fatherName;
private String aadharNo;
private String mobileNo;
private String address;
private String purpose;
private String assuredAmt;
private String Recommended;
private String vipLetterDate;
private String hospCode;
private String hospName;

private String txthospname;
private String locTokenSno;
private String hidrecommendedBy;
private String hidHospCode;
private String userId;
private String ipAddress;


public String getIpAddress() {
	return ipAddress;
}
public void setIpAddress(String ipAddress) {
	this.ipAddress = ipAddress;
}
public String getUserId() {
	return userId;
}
public void setUserId(String userId) {
	this.userId = userId;
}
public String getHidrecommendedBy() {
	return hidrecommendedBy;
}
public void setHidrecommendedBy(String hidrecommendedBy) {
	this.hidrecommendedBy = hidrecommendedBy;
}
public String getHidHospCode() {
	return hidHospCode;
}
public void setHidHospCode(String hidHospCode) {
	this.hidHospCode = hidHospCode;
}
public String getPrevLocAmt() {
	return prevLocAmt;
}
public void setPrevLocAmt(String prevLocAmt) {
	this.prevLocAmt = prevLocAmt;
}
private String letterDate;
private String prefix;
private String prevLocAmt;

public String getPrevLocLetterDate() {
	return prevLocLetterDate;
}
public void setPrevLocLetterDate(String prevLocLetterDate) {
	this.prevLocLetterDate = prevLocLetterDate;
}
private String vipName;
private String preFormatAmt;
private String preAmtInWords;

public String getPreAmtInWords() {
	return preAmtInWords;
}
public void setPreAmtInWords(String preAmtInWords) {
	this.preAmtInWords = preAmtInWords;
}
public String getPreFormatAmt() {
	return preFormatAmt;
}
public void setPreFormatAmt(String preFormatAmt) {
	this.preFormatAmt = preFormatAmt;
}
private String prevLocNo;
private String prevLocLetterDate;
public String getFormatAmt() {
	return formatAmt;
}
public void setFormatAmt(String formatAmt) {
	this.formatAmt = formatAmt;
}
public String getAmtInWords() {
	return amtInWords;
}
public void setAmtInWords(String amtInWords) {
	this.amtInWords = amtInWords;
}
private String formatAmt;
private String amtInWords;

public String getPrevLocNo() {
	return prevLocNo;
}
public void setPrevLocNo(String prevLocNo) {
	this.prevLocNo = prevLocNo;
}
public String getPrefix() {
	return prefix;
}
public void setPrefix(String prefix) {
	this.prefix = prefix;
}
public String getVipName() {
	return vipName;
}
public void setVipName(String vipName) {
	this.vipName = vipName;
}
private String vipDesg;


public String getVipDesg() {
	return vipDesg;
}
public void setVipDesg(String vipDesg) {
	this.vipDesg = vipDesg;
}
public String getLetterDate() {
	return letterDate;
}
public void setLetterDate(String letterDate) {
	this.letterDate = letterDate;
}
public String getLocNo() {
	return locNo;
}
public void setLocNo(String locNo) {
	this.locNo = locNo;
}
private String locToken;
private String locNo;
public String getLocToken() {
	return locToken;
}
public void setLocToken(String locToken) {
	this.locToken = locToken;
}
public String getLocTokenSno() {
	return locTokenSno;
}
public void setLocTokenSno(String locTokenSno) {
	this.locTokenSno = locTokenSno;
}
public String getTxthospname() {
	return txthospname;
}
public void setTxthospname(String txthospname) {
	this.txthospname = txthospname;
}
private String recommendedBy,constName,constNo;
public String getPatientName() {
	return patientName;
}
public void setPatientName(String patientName) {
	this.patientName = patientName;
}
public String getAddress() {
	return address;
}
public void setAddress(String address) {
	this.address = address;
}
public String getPurpose() {
	return purpose;
}
public void setPurpose(String purpose) {
	this.purpose = purpose;
}
public String getAssuredAmt() {
	return assuredAmt;
}
public void setAssuredAmt(String assuredAmt) {
	this.assuredAmt = assuredAmt;
}
public String getRecommended() {
	return Recommended;
}
public void setRecommended(String recommended) {
	Recommended = recommended;
}
public String getVipLetterDate() {
	return vipLetterDate;
}
public void setVipLetterDate(String vipLetterDate) {
	this.vipLetterDate = vipLetterDate;
}
public String getHospCode() {
	return hospCode;
}
public void setHospCode(String hospCode) {
	this.hospCode = hospCode;
}
public String getHospName() {
	return hospName;
}
public void setHospName(String hospName) {
	this.hospName = hospName;
}
public String getFatherName() {
	return fatherName;
}
public void setFatherName(String fatherName) {
	this.fatherName = fatherName;
}
public String getRecommendedBy() {
	return recommendedBy;
}
public void setRecommendedBy(String recommendedBy) {
	this.recommendedBy = recommendedBy;
}
public String getConstName() {
	return constName;
}
public void setConstName(String constName) {
	this.constName = constName;
}
public String getConstNo() {
	return constNo;
}
public void setConstNo(String constNo) {
	this.constNo = constNo;
}
public String getAadharNo() {
	return aadharNo;
}
public void setAadharNo(String aadharNo) {
	this.aadharNo = aadharNo;
}
public String getMobileNo() {
	return mobileNo;
}
public void setMobileNo(String mobileNo) {
	this.mobileNo = mobileNo;
}

}

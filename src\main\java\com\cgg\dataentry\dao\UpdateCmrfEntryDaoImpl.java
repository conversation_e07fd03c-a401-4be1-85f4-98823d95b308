
package com.cgg.dataentry.dao;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Connection;
import java.util.ArrayList;
import com.cgg.dataentry.model.UpdateCmrfEntryForm;
import com.cgg.dataentry.repositories.MlaInwardCmrfEditRepository;

import java.util.List;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import javax.sql.DataSource;
import org.springframework.stereotype.Repository;

@Repository
public class UpdateCmrfEntryDaoImpl implements UpdateCmrfEntryDao
{
    @Autowired
    private DataSource dataSource;
    @Autowired
    JdbcTemplate jdbcTemlate;
    
	@Autowired
	MlaInwardCmrfEditRepository mlaInwardCmrfEditRepository;
    
    public List<UpdateCmrfEntryForm> getRecommendedDetails() throws Exception {
        Connection con = null;
        Statement st = null;
        ResultSet rs = null;
        String sql = null;
        final List<UpdateCmrfEntryForm> updateCmrfDetails = new ArrayList<UpdateCmrfEntryForm>();
        try {
            con = this.dataSource.getConnection();
            st = con.createStatement();
        //    sql = "select cno, cno||','||mlamp||coalesce(design,'')||coalesce('('||case when minister='Y' then 'Minister' else  party end||')','')|| ', '||cname as cname from  constituency  order by cno";
			 sql="select cno, cno||','||coalesce(mlamp,'')||coalesce(design,'')||coalesce('('||case when minister='Y' then 'Minister' else  party end||')','')|| ', '||coalesce(cname,'') as cname from  constituency where  active_con=true  order by cno";
            rs = st.executeQuery(sql);
            while (rs.next()) {
                final UpdateCmrfEntryForm updateCmrfEntry = new UpdateCmrfEntryForm();
                updateCmrfEntry.setConstNo(rs.getString("cno"));
                updateCmrfEntry.setConstName(rs.getString("cname"));
                updateCmrfDetails.add(updateCmrfEntry);
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            if (rs != null) {
                rs.close();
                rs = null;
            }
            if (st != null) {
                st.close();
                st = null;
            }
            if (con != null && !con.isClosed()) {
                con.close();
                con = null;
            }
        }
        return updateCmrfDetails;
    }
    
    public List<UpdateCmrfEntryForm> getHospitalList() throws Exception {
        Connection con = null;
        Statement st = null;
        ResultSet rs = null;
        String sql = null;
        final List<UpdateCmrfEntryForm> hospDetails = new ArrayList<UpdateCmrfEntryForm>();
        try {
            con = this.dataSource.getConnection();
            st = con.createStatement();
            sql = "select hospcode, hospname||'('||recog||')' as hospname from  hospital where delete_flag='false' order by hospname";
            rs = st.executeQuery(sql);
            while (rs.next()) {
                final UpdateCmrfEntryForm updateCmrfEntry = new UpdateCmrfEntryForm();
                updateCmrfEntry.setHospCode(rs.getString("hospcode"));
                updateCmrfEntry.setHospName(rs.getString("hospname"));
                hospDetails.add(updateCmrfEntry);
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            if (rs != null) {
                rs.close();
                rs = null;
            }
            if (st != null) {
                st.close();
                st = null;
            }
            if (con != null && !con.isClosed()) {
                con.close();
                con = null;
            }
        }
        return hospDetails;
    }
    
    public List<UpdateCmrfEntryForm> getDistricts() throws Exception {
        Connection con = null;
        Statement st = null;
        ResultSet rs = null;
        String sql = null;
        final List<UpdateCmrfEntryForm> updateCmrfDetails = new ArrayList<UpdateCmrfEntryForm>();
        try {
            con = this.dataSource.getConnection();
            st = con.createStatement();
            sql = "select distno, distname from  district  order by distname";
            System.out.println("sql---" + sql);
            rs = st.executeQuery(sql);
            while (rs.next()) {
                final UpdateCmrfEntryForm updateCmrfEntry = new UpdateCmrfEntryForm();
                updateCmrfEntry.setDistNo(rs.getString("distno"));
                updateCmrfEntry.setDistName(rs.getString("distname"));
                updateCmrfDetails.add(updateCmrfEntry);
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            if (rs != null) {
                rs.close();
                rs = null;
            }
            if (st != null) {
                st.close();
                st = null;
            }
            if (con != null && !con.isClosed()) {
                con.close();
                con = null;
            }
        }
        return updateCmrfDetails;
    }
    
    public List<UpdateCmrfEntryForm> getMandals(final String distCode) throws Exception {
        Connection con = null;
        Statement st = null;
        ResultSet rs = null;
        String sql = null;
        final List<UpdateCmrfEntryForm> updateCmrfDetails = new ArrayList<UpdateCmrfEntryForm>();
        try {
            con = this.dataSource.getConnection();
            st = con.createStatement();
            sql = "select mcode, mname from  mandal where distcode='" + distCode + "' order by mname";
            System.out.println("sql---" + sql);
            rs = st.executeQuery(sql);
            while (rs.next()) {
                final UpdateCmrfEntryForm updateCmrfEntry = new UpdateCmrfEntryForm();
                updateCmrfEntry.setMandalNo(rs.getString("mcode"));
                updateCmrfEntry.setMandalName(rs.getString("mname"));
                updateCmrfDetails.add(updateCmrfEntry);
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            if (rs != null) {
                rs.close();
                rs = null;
            }
            if (st != null) {
                st.close();
                st = null;
            }
            if (con != null && !con.isClosed()) {
                con.close();
                con = null;
            }
        }
        return updateCmrfDetails;
    }
    
    public String getInwardData(final String mlaCmrfNo) throws Exception {
        Connection con = null;
        Statement st = null;
        ResultSet rs = null;
        String sql = null;
        String inwardStr = null;
        try {
            con = this.dataSource.getConnection();
            st = con.createStatement();
            sql = "select recommended_by||':#'||patient_name||':#'||father_son_of||':#'||aadhar_no||':#'||mla_cmrf_no||':#'||hosp_code  as inwardStr from mla_cmrf  where mla_cmrf_no='" + mlaCmrfNo + "' and status='1' and delete_flag=false";
            System.out.println("sql---" + sql);
            rs = st.executeQuery(sql);
            while (rs.next()) {
                inwardStr = rs.getString("inwardStr");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            if (rs != null) {
                rs.close();
                rs = null;
            }
            if (st != null) {
                st.close();
                st = null;
            }
            if (con != null && !con.isClosed()) {
                con.close();
                con = null;
            }
        }
        return inwardStr;
    }
    
    public String getCmrfData(final String cmrfVal) throws Exception {
        Connection con = null;
        Statement st = null;
        ResultSet rs = null;
        String sql = null;
        String updateStr = null;
        try {
            con = this.dataSource.getConnection();
            st = con.createStatement();
            sql = "select ex_gratia||':#'||to_char(cmrf_dt,'dd/mm/yyyy')||':#'||year001||':#'||coalesce(aadhar_no,'0')||':#'||pat_name||':#'"
            		+ "||hosp_code||':#'||age ||':#'||recommended_by||':#'||father_son_of||':#'||pat_address||':#'||pat_district||':#'||coalesce(pat_mandal,'0')||':#'||req_amt||':#'||purpose||':#'|| sanc_amt ||':#'|| payment_to||':#'||sined||':#'||coalesce(cmp_no,'0')||':#'||coalesce(other_const,'0')||':#'||coalesce(inward_id,'0')||':#'||coalesce(nullif(cmrf_loc,''),'0')||':#'||coalesce(bank_account_no,'0')||':#'||coalesce(bank_acc_hol_name,'null')||':#'||coalesce(mobile_no,0) as updateStr from cmrelief  where cmrf_no='" + cmrfVal + "'";
            System.out.println("sql---" + sql);
            rs = st.executeQuery(sql);
            while (rs.next()) {
                updateStr = rs.getString("updateStr");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            if (rs != null) {
                rs.close();
                rs = null;
            }
            if (st != null) {
                st.close();
                st = null;
            }
            if (con != null && !con.isClosed()) {
                con.close();
                con = null;
            }
        }
        return updateStr;
    }
    
    public int updateCmrfDetails(final UpdateCmrfEntryForm formBean, final Map<String, Object> model, final HttpServletRequest request) {
        String qry = null;
        int cnt = 0;
        String userId = "";
        boolean success = false;
        try (Connection con = this.dataSource.getConnection(); Statement stmt = con.createStatement()){
            con.setAutoCommit(false);
            userId = formBean.getUserId();

            int res = mlaInwardCmrfEditRepository.insertCmreliefLog(formBean.getCmrfno(),"Updating the data in CMRF Entry",userId,request.getRemoteAddr());
            System.out.println("cmrfno in " + formBean.getCmrfno());
            System.out.println("vvv" + formBean.getSigned());
            System.out.println("vvv" + formBean.getExgratia());
            String bankAccNo = formBean.getBankAccNo().trim();
            String bankAccHolName = formBean.getBankAccHolName().trim();
            String mobileNo = formBean.getMobileNo().trim();
            if (res > 0) {
                qry = "update cmrelief set cmrf_dt=to_date('" + formBean.getCmrfDate() + "','dd-mm-yyyy') , pat_name='" + formBean.getPatientName().trim() + "' , father_son_of='" + formBean.getFatherName().trim() + "' ,"
                		+ " pat_address ='" + formBean.getPatAddress().trim() + "', pat_district ='" + formBean.getPatDistrict() + "' , purpose ='" + formBean.getPurpose().trim() + "', hosp_code ='" + formBean.getHospCode() + "', recommended_by ='" + formBean.getRecommendedBy() + "', req_amt =" + formBean.getReqAmt() + ", sanc_amt =" + formBean.getSancAmt() + ", payment_to ='" + formBean.getPaymentTo() + "', sined='" + formBean.getSigned() + "',ex_gratia ='" + formBean.getExgratia() + "', ref_tocm ='N', ip_address='" + request.getRemoteAddr() + "', updated_by='" + formBean.getUserId() + "',updated_on=now(),";

                if (formBean.getInwardId().trim() != null && !formBean.getInwardId().trim().isEmpty()) {
                    qry += "inward_id='" + formBean.getInwardId().trim() + "', ";
                }
                if (formBean.getCmrfLoc().trim() != null && !formBean.getCmrfLoc().trim().isEmpty()) {
                    qry += "cmrf_loc='" + formBean.getCmrfLoc().trim() + "', ";
                }
                if (formBean.getAadharNo() != null) {
                    qry = qry + "aadhar_no='" + formBean.getAadharNo().trim() + "', ";
                }
                if (mobileNo != null && !"".equals(mobileNo)) {
                    qry = qry + "mobile_no=" + formBean.getMobileNo().trim() + ", ";
                }
                if (!"null".equals(bankAccNo) && !"".equals(bankAccNo) && !"0".equals(bankAccNo) && bankAccNo != null && !bankAccNo.isEmpty()) {
                    qry = qry + "bank_account_no='" + bankAccNo + "', "; 
                }else{
                    qry = qry + "bank_account_no=null, "; 
                }
                if (!"null".equals(bankAccHolName) && !"".equals(bankAccHolName) && bankAccHolName != null && !bankAccHolName.isEmpty()) {
                    qry = qry + "bank_acc_hol_name='" + bankAccHolName + "', "; 
                }else{
                    qry = qry + "bank_acc_hol_name=null, ";
                }
                if (formBean.getRecommendedBy().equals("998")) {
                    qry = qry + "other_const='" + formBean.getOtherConst() + "', ";
                }else {
                	qry += "other_const=null,";
                }

                /*    if (formBean.getPatMandal().isEmpty()) {
                   qry += "pat_mandal=null,";
                }
                else {
                    qry = qry + "pat_mandal='" + formBean.getPatMandal() + "',";
                }*/
                 if (formBean.getPatMandal()!=null && !formBean.getPatMandal().isEmpty()) {
                     	qry = qry + "pat_mandal='" + formBean.getPatMandal() + "',";
                         
                     }
                     else {
                     	qry += "pat_mandal=null,";
                     }

                qry = qry + " age='" + formBean.getAge() + "',rec_from=" + formBean.getRecFrom() + "";
                qry = qry + " where cmrf_no ='" + formBean.getCmrfno()+"' and cmp_no is null";
                System.out.println("qry---" + qry);
                // cnt = this.jdbcTemlate.update(qry);
                cnt = stmt.executeUpdate(qry);
                final String ipAddress = formBean.getIpAddress();
                String mlaupd = null;
              /*  if (formBean.getValueck().equals("y")) {
                    sql = "insert into sanc_amt_verification(userid,ip,proxy,appid) values('" + userId + "','" + ipAddress + "','" + ipAddress + "','" + formBean.getCmrfno() + formBean.getCmrfYr() + "')";
                    sancFlag = CommonUtils.insertQuery(con, sql);
                }*/
                if (cnt == 1 && formBean.getInwardId() != null && !formBean.getInwardId().isEmpty()) {
                    cnt = 0;
                    cnt = mlaInwardCmrfEditRepository.insertMlaCmrfLog(formBean.getInwardId(), "Updating the data in MLA_CMRF for change in CMRF Entry", userId, request.getRemoteAddr());
                    if (cnt == 1) {
                        mlaupd = "update mla_cmrf set mla_cmrf_no= '" + formBean.getInwardId() + "',cmrf_no= '" + formBean.getCmrfno() + "',status='3',updated_on=now(),updated_by='" + userId + "',";
                          
                        if (!"null".equals(bankAccNo) && !"".equals(bankAccNo) && !"0".equals(bankAccNo) && bankAccNo != null && !bankAccNo.isEmpty()) {
                            mlaupd = mlaupd + "bank_acc_no='" + bankAccNo + "', "; 
                        }else{
                            mlaupd = mlaupd + "bank_acc_no=null, "; 
                        }
                        if (!"null".equals(bankAccHolName) && !"".equals(bankAccHolName) && bankAccHolName != null && !bankAccHolName.isEmpty()) {
                            mlaupd = mlaupd + "bank_acc_holder_name='" + bankAccHolName + "', "; 
                        }else {
                            mlaupd += "bank_acc_holder_name=null,";
                        }
                            
                        mlaupd = mlaupd + "ip_address='" + request.getRemoteAddr() + "' where mla_cmrf_no ='" + formBean.getInwardId() + "'";
                        System.out.println("mla qry--" + mlaupd);
                        // cnt = this.jdbcTemlate.update(mlaupd);
                        cnt = stmt.executeUpdate(mlaupd);
                    }
                }

                if (cnt > 0) {
                    con.commit();
                    success = true;
                }
                
            }
            if (!success) {
                con.rollback();
            }
        }
        catch (Exception e) {
            e.printStackTrace();
            try (Connection con = this.dataSource.getConnection()) {
                con.rollback();
            } catch (SQLException rollbackEx) {
                rollbackEx.printStackTrace();
            }
        }
        return cnt;
    }
    
    public String getCmrfLocData(final String cmrfLocNo) throws Exception {
        Connection con = null;
        Statement st = null;
        ResultSet rs = null;
        String sql = null;
        String cmrfLocStr = null;
        try {
            con = this.dataSource.getConnection();
            st = con.createStatement();
            sql = "select recommended_by||':#'||patient_name||':#'||father_name||':#'||address||':#'||assured_amt||':#'||hosp_code||':#'||purpose||':#'||loc_no as cmrfLocStr from loc_cmrf  where loc_no='" + cmrfLocNo + "'";
            System.out.println("sql---" + sql);
            rs = st.executeQuery(sql);
            while (rs.next()) {
                cmrfLocStr = rs.getString("cmrfLocStr");
            }
            if (cmrfLocStr == null || cmrfLocStr.equals("")) {
                cmrfLocStr = "Invalid LOC No.";
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            if (rs != null) {
                rs.close();
                rs = null;
            }
            if (st != null) {
                st.close();
                st = null;
            }
            if (con != null && !con.isClosed()) {
                con.close();
                con = null;
            }
        }
        return cmrfLocStr;
    }
    
    public UpdateCmrfEntryForm getCmrfValues(final String cmrfNo) throws Exception {
        Connection con = null;
        Statement st = null;
        ResultSet rs = null;
        String sql = null;
        String cmrfLocStr = null;
        final UpdateCmrfEntryForm updateCmrfEntryForm = new UpdateCmrfEntryForm();
        try {
            con = this.dataSource.getConnection();
            st = con.createStatement();
            sql = "select to_char(cmrf_dt,'dd-mm-yyyy') as cmrfDate , pat_name , father_son_of , pat_address , pat_district ,pat_mandal, purpose , hosp_code , recommended_by , endorsement , req_amt , sanc_amt , to_char(sanc_date,'dd-mm-yyyy') , payment_to ,ex_gratia , rejected ,  prevsanc , prevdate , year001 , prevcmpno , prevapplnos , det_diseases , doc_name , doc_design , type_est , ref_tocm, white_cardno, revenue_no, to_char(revenue_date,'dd-mm-yyyy') , cmp_no, to_char(cmp_date,'dd-mm-yyyy'),pat_mandal, prev_hosp_names(cmrf_no),age,is_reimbursement,economy,diseasetype,sined,Arogya_sri,inward_id,coalesce(cmrf_loc,'')cmrf_loc,split_part(cmrf_no,'/',2)||'/'||split_part(cmrf_no,'/',3),patient_ip_no,aadhar_no,other_const from cmrelief where cmrf_no = '" + cmrfNo + "' ";
            System.out.println("sql---" + sql);
            rs = st.executeQuery(sql);
            if (rs.next()) {
                updateCmrfEntryForm.setCmrfDate(rs.getString("cmrfDate"));
                updateCmrfEntryForm.setPatientName(rs.getString("pat_name"));
                updateCmrfEntryForm.setFatherName(rs.getString("father_son_of"));
                updateCmrfEntryForm.setPatAddress(rs.getString("pat_address"));
                updateCmrfEntryForm.setPurpose(rs.getString("purpose"));
                updateCmrfEntryForm.setHospCode(rs.getString("hosp_code"));
                updateCmrfEntryForm.setRecommendedBy(rs.getString("recommended_by"));
                updateCmrfEntryForm.setReqAmt(rs.getString("req_amt"));
                updateCmrfEntryForm.setSancAmt(rs.getString("sanc_amt"));
                updateCmrfEntryForm.setPatDistrict(rs.getString("pat_district"));
                updateCmrfEntryForm.setAge(rs.getString("age"));
                updateCmrfEntryForm.setPatMandal(rs.getString("pat_mandal"));
                updateCmrfEntryForm.setAadharNo(rs.getString("aadhar_no"));
                updateCmrfEntryForm.setPatientIpNo(rs.getString("patient_ip_no"));
                updateCmrfEntryForm.setSigned(rs.getString("sined"));
                updateCmrfEntryForm.setExgratia(rs.getString("ex_gratia"));
                updateCmrfEntryForm.setCmrfLoc(rs.getString("cmrf_loc"));
                updateCmrfEntryForm.setInwardId(rs.getString("inward_id"));
                updateCmrfEntryForm.setOtherConst(rs.getString("other_const"));
                updateCmrfEntryForm.setOtherConHidval(rs.getString("other_const"));
                updateCmrfEntryForm.setCmpNo(rs.getString("cmp_no"));
                System.out.println(updateCmrfEntryForm.getSigned());
                System.out.println(updateCmrfEntryForm.getExgratia());
            }
            if (cmrfLocStr == null || cmrfLocStr.equals("")) {
                cmrfLocStr = "Invalid LOC No.";
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            if (rs != null) {
                rs.close();
                rs = null;
            }
            if (st != null) {
                st.close();
                st = null;
            }
            if (con != null && !con.isClosed()) {
                con.close();
                con = null;
            }
        }
        return updateCmrfEntryForm;
    }
    
    public List<UpdateCmrfEntryForm> getOtherConsList() throws Exception {
        Connection con = null;
        Statement st = null;
        ResultSet rs = null;
        String sql = null;
        final List<UpdateCmrfEntryForm> updateCmrfDetails = new ArrayList<UpdateCmrfEntryForm>();
        try {
            con = this.dataSource.getConnection();
            st = con.createStatement();
            sql = "select cname||'-'||cno as cnocode, cname||'-'||cno  as cname from  other_constituency_list order by cno";
            System.out.println("sql---" + sql);
            rs = st.executeQuery(sql);
            while (rs.next()) {
                final UpdateCmrfEntryForm updateCmrfEntry = new UpdateCmrfEntryForm();
                updateCmrfEntry.setConstNo(rs.getString("cnocode"));
                updateCmrfEntry.setConstName(rs.getString("cname"));
                updateCmrfDetails.add(updateCmrfEntry);
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            if (rs != null) {
                rs.close();
                rs = null;
            }
            if (st != null) {
                st.close();
                st = null;
            }
            if (con != null && !con.isClosed()) {
                con.close();
                con = null;
            }
        }
        return updateCmrfDetails;
    }

    public String getTokenBankData(final String mlaCmrfNo) throws Exception {
        Connection con = null;
        Statement st = null;
        ResultSet rs = null;
        String sql = null;
        String inwardStr = null;
        try {
            con = this.dataSource.getConnection();
            st = con.createStatement();
            sql = "select coalesce(bank_acc_no,'0')||':#'||coalesce(bank_acc_holder_name,'')||':#'||to_char(entered_on, 'dd/MM/yyyy') as inwardStr from mla_cmrf  where mla_cmrf_no='"+ mlaCmrfNo +"' and delete_flag=false";
            System.out.println("sql---" + sql);
            rs = st.executeQuery(sql);
            while (rs.next()) {
                inwardStr = rs.getString("inwardStr");
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            if (rs != null) {
                rs.close();
                rs = null;
            }
            if (st != null) {
                st.close();
                st = null;
            }
            if (con != null && !con.isClosed()) {
                con.close();
                con = null;
            }
        }
        return inwardStr;
    }
}

package com.cgg.reports.service;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.reports.dao.MonthWiseChequesReceivedByPRDao;
import com.cgg.reports.model.MonthWiseSummary;

@Service
public class MonthWiseChequesReceivedByPRService {

	@Autowired
	MonthWiseChequesReceivedByPRDao monthWiseChequesReceivedByPRDao;

	public List<MonthWiseSummary> getChequesReceivedByPRMonthReport(String dateFrom, String dateTo) throws Exception {
		return monthWiseChequesReceivedByPRDao.getChequesReceivedByPRMonthReport(dateFrom, dateTo);
	}

}

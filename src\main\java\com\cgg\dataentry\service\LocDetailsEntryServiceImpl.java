package com.cgg.dataentry.service;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.dataentry.dao.LocDetailsEntryDao;
import com.cgg.dataentry.model.LocDetailsEntryForm;

@Service
public class LocDetailsEntryServiceImpl implements LocDetailsEntryService{
	@Autowired
	private LocDetailsEntryDao locEntryDao;
	
	public List<LocDetailsEntryForm> getRecommendedDetails()throws Exception{
		
		return locEntryDao.getRecommendedDetails();
	}
public List<LocDetailsEntryForm> getHospitalList()throws Exception{
		
		return locEntryDao.getHospitalList();
	}
public String saveCmrfDetails(LocDetailsEntryForm locEntryForm,
        Map<String, Object> model) throws Exception{
	System.out.println("service");
	return locEntryDao.saveLocDetails(locEntryForm,model);
}
public LocDetailsEntryForm getLocLetterData(String locNo) throws Exception{
	System.out.println("service");
	return locEntryDao.getLocLetterData(locNo);
}

public String getLocData(String locTokenNo) throws Exception{
	System.out.println("service");
	return locEntryDao.getLocData(locTokenNo);
}

	@Override
	public List<LocDetailsEntryForm> getDistricts()throws Exception{
		return locEntryDao.getDistricts();
	}
	
	@Override
	public List<Map<String, Object>> getCmrfLocDetailsByAadhar(String uidNo, String locTokenNo) throws Exception {
		return  locEntryDao.getCmrfLocDetailsByAadhar(uidNo,locTokenNo);
	}
	
	
}

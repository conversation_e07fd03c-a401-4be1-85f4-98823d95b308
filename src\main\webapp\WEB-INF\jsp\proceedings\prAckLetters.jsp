<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ page import="java.util.*, java.text.*" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<%
	String basePath = request.getContextPath();
%>
<!DOCTYPE html>
<html>
<head>
    <title>PR Wise Acknowledgement Details</title>
    <style>
        #myTable thead tr th {
            text-align: center;
        }

        .form {
            background-color: white;
            border: 1px solid #ccc;
            padding: 20px;
            box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
        }

        h3 {
            color: #563d7c;
        }

        .form-row {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        label {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .required {
            color: red;
        }

        select {
            padding: 6px;
            border-radius: 3px;
            border: 1px solid #ccc;
        }

        .button {
            padding: 8px 16px;
            background-color: #2e4a7f;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            margin-top: 22px;
        }

        .button:hover {
            background-color: #1e355a;
        }

        .custom-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.5); /* Dim background */
            display: none; /* Initially hidden */
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .custom-modal-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh; /* prevent modal from exceeding screen */
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
            animation: fadeInScale 0.3s ease-in-out;
        }

        @keyframes fadeInScale {
            from {
                transform: scale(0.9);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        .error {
			color: red !important;
			font-size: 0.875em !important;
			margin-top: 0.25rem !important;
			display: block !important;
		}
		.is-invalid {
			border-color: #dc3545 !important;
		}
		.is-valid {
			border-color: #28a745 !important;
		}
		.invalid-feedback {
			display: none !important;
			color: #dc3545 !important;
		}
		.valid-feedback {
			display: none !important;
			color: #28a745 !important;
		}
		.is-invalid + .invalid-feedback {
			display: block !important;
		}
		.is-valid + .valid-feedback {
			display: block !important;
		}	
    </style>
    <jsp:include page="/WEB-INF/jsp/include_DT.jsp" />
	<script src="js/common/commonValidations.js"></script>
    <script type="text/javascript">
        $(document).ready(function() {
            $('#myTable').DataTable({
                dom : 'Bfrtip',
                paging : true,
                lengthMenu: [
                    [10, 20, 50, 100, -1],
                    [10, 20, 50, 100, 'All']
                ],
                // processing : true,
                pageLength : -1,
                language : {
                    "loadingRecords" : "Please wait - loading..."
                },
                buttons : [
                {
                    extend : 'print',
                    text: '<i class="fa fa-print"></i>',
                    title : 'PR Wise Acknowledgement Details',
                    titleAttr: 'Print',
                    orientation : 'landscape',
                    pageSize : 'A4',
                },
                {
                    extend : 'excelHtml5',
                    text: '<i class="fa fa-file-excel-o"></i>',
                    title : 'PR Wise Acknowledgement Details',
                    titleAttr: 'Export to Excel',
                    orientation : 'landscape',
                    pageSize : 'A4',
                }, {
                    extend : 'pdfHtml5',
                    text: '<i class="fa fa-file-pdf-o"></i>',
                    title : 'PR Wise Acknowledgement Details',
                    titleAttr: 'Export to PDF',
                    orientation : 'landscape',
                    pageSize : 'A4',
                    customize: function(doc) {
                        doc.styles.tableBodyEven.alignment = 'left';
                        doc.styles.tableBodyOdd.alignment = 'left';
                        doc.styles.tableHeader.alignment = 'left';
                        doc.styles.tableHeader.fillColor = '#563d7c';
                        doc.styles.tableHeader.color = 'white';
                        doc.styles.tableBodyEven.fontSize = 9; // Decrease font size for body
                        doc.styles.tableBodyOdd.fontSize = 9; // Decrease font size for body
                        doc.styles.tableHeader.fontSize = 10; // Adjust header font size
                        
                    }
                },	
                {
                    extend: 'colvis',
                    text: '<i class="fa fa-list"></i>',
                    titleAttr: 'Show/Hide Columns'
                },
                {
                    extend: 'pageLength',
                    titleAttr: 'Page Length',
                }
            ],
            "footerCallback": function (row, data, start, end, display) {
                var api = this.api();
                var parseValue = function(val) {
                    return typeof val === 'string' ?
                        parseFloat(val.replace(/,/g, '')) : 
                        (typeof val === 'number' ? val : 0);
                };
                var total = api.column(2, { page: 'current' }).data().reduce(function (acc, curr) {
                    return parseValue(acc) + parseValue(curr);
                }, 0);
                $(api.column(2).footer()).html(total.toLocaleString('en-IN'));
            },
                initComplete: function () {
                // Focus on the search text box
                $('#myTable_filter input').focus();
            }
            })
        });
    </script>
    <script>
        const basePath = "<%= basePath %>";
        const dateFrom = "${dateFrom}";
        const dateTo = "${dateTo}";

        function printAllAckLetters() {
            const url = basePath + '/prAckLetters/printAllAckLetters?dateFrom=' + encodeURIComponent(dateFrom) + '&dateTo=' + encodeURIComponent(dateTo);
            window.location.href = url;
        }
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const today = new Date().toISOString().split('T')[0];
            const minDate = "2025-05-01";

            const fromDateInput = document.getElementById('fromDate');
            const toDateInput = document.getElementById('toDate');

            fromDateInput.setAttribute('min', minDate);
            fromDateInput.setAttribute('max', today);

            toDateInput.setAttribute('min', minDate);
            toDateInput.setAttribute('max', today);

            fromDateInput.addEventListener('input', function () {
                const fromVal = fromDateInput.value;

                // Clear toDate if it's earlier than new fromDate
                if (toDateInput.value && toDateInput.value < fromVal) {
                    toDateInput.value = "";
                }

                // Also adjust toDate's min based on fromDate
                toDateInput.setAttribute('min', fromVal);
                
                // Prevent future date
                if (fromVal > today) {
                    fromDateInput.value = today;
                }
            });

            toDateInput.addEventListener('input', function () {
                const fromVal = fromDateInput.value;
                const toVal = toDateInput.value;

                if (toVal > today) {
                    toDateInput.value = today;
                }

                if (fromVal && toVal < fromVal) {
                    toDateInput.value = "";
                }
            });
        });
    </script>
</head>
<body>
    <div class="complete_wrap">
        <div class="container form" style="width: 90%;">
            <c:if test="${not empty success}">
                <div class="alert alert-success">
                    <p style="color:green;font-size: 15px;font-weight: bold;">${success}</p>
                </div>
            </c:if>
			<c:if test="${not empty error}">
                <div class="alert alert-danger">
                    <p style="color:red;font-size: 15px;font-weight: bold;">${error}</p>
                </div>
			</c:if>
            <h3><i>Acknowledgement Letters</i></h3>
            <hr>
            <form action="prAckLetters" method="post">
                <div class="form-row">
                    <div class="form-group col-sm-2">
                        <label>Esigned From: <span class="required">*</span></label>
                        <input type="date" id="fromDate" name="fromDate" required>
                    </select>
                    </div>
    
                    <div class="form-group col-sm-2">
                        <label>Esigned To: <span class="required">*</span></label>
                        <input type="date" id="toDate" name="toDate" required>
                    </select>
                    </div>
    
                    <div>
                        <button type="submit" class="button">Submit</button>
                    </div>
                </div>
            </form>

            <br>
            <c:if test="${not empty prAckList}">
                <div class="container mt-4">
                    <h3 class="text-center">Print Acknowledgement Letters</h3>
                    <h4 class="text-danger">From : ${dateFrom} To : ${dateTo}</h4>
				    <hr style="margin: 10px 0px;">
                    <div class="row" style="text-align: end; margin-bottom: 10px;">
                        <button type="button" class="btn btn-primary button" style="margin: 0px;" onclick="printAllAckLetters()">Print All</button>
                    </div>
                    <table border="1" align="center" class="display" id="myTable">
                        <thead class="table-dark">
                            <tr style="background: #563d7c; color: white;">
                                <th>Sl No.</th>
                                <th>People Representative</th>
                                <th>Total Cheques</th>
                                <th>Print</th>
                                <th>Upload</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="data" items="${prAckList}" varStatus="row">
                                <tr>
                                    <td>${row.index + 1}</td>
                                    <td>
                                        <a href="#" class="openCmpModal"
                                            data-cname="${data.cname}"
                                            data-cno="${data.cno}"
                                            data-otherconst="${data.other_const}"
                                            data-datefrom="${dateFrom}"
                                            data-dateto="${dateTo}">
                                            ${data.cname}
                                        </a>
                                    </td>
                                    <td align="end">${data.total_cnt}</td>
                                    <td align="center"><a href="prAckLetters/printAckLetter?cno=${data.cno}&otherConst=${data.other_const}&dateFrom=${dateFrom}&dateTo=${dateTo}"><i class="fa fa-lg fa-print"></i></a></td>
                                    <td align="center">
                                        <c:if test="${data.is_qr_scanned and empty data.ack_letter}">
                                            <button type="button" class="text-primary upload-btn"
                                                    data-toggle="modal"
                                                    data-target="#prAckModal"
                                                    data-cno="${data.cno}"
                                                    data-other-const="${data.other_const}"
                                                    data-pr-name="${data.cname}"
                                                    data-total-cheques="${data.total_cnt}"
                                                    data-esigned-from-date="${dateFrom}"
                                                    data-esigned-to-date="${dateTo}">
                                                <i class="fa fa-solid fa-upload"></i>
                                            </button>
                                        </c:if>

                                        <c:if test="${data.is_qr_scanned == null}">
                                            -
                                        </c:if>

                                        <c:if test="${data.is_qr_scanned != null and !data.is_qr_scanned}">
                                            <span style="color: red"><b>-</b></span>
                                        </c:if>

                                        <c:if test="${data.is_qr_scanned and not empty data.ack_letter}">
                                            <a href="preview/${data.ack_letter}" target="_blank">
                                                <i class="fa fa-eye fa-lg"></i>
                                            </a>
                                        </c:if>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                        <!-- <tfoot>
                            <tr>
                                <th colspan="2">Total</th>
                                <th style="text-align: end;"></th>
                                <th colspan="2"></th>
                            </tr>
                        </tfoot> -->
                    </table>
                </div>
            </c:if>
            
            <div id="cmpDetailsModal" class="custom-modal-overlay">
                <div class="custom-modal-content">
                    <h4 id="cmpModalLabel" class="mb-3 text-center">CMP Details</h4>
                    <div style="max-height: 60vh; overflow-y: auto;">
                        <table class="table table-primary text-center table-bordered">
                            <thead>
                                <tr>
                                    <th>Control Number Series</th>
                                    <th>Total Cheques</th>
                                </tr>
                            </thead>
                            <tbody id="cmpDetailsBody">
                                <!-- Dynamic content will be injected here -->
                            </tbody>
                        </table>
                    </div>
                    <!-- Align Close button to bottom-right -->
                    <div style="text-align: right; margin-top: 20px;">
                        <button id="closeCmpDetails" class="btn btn-danger">Close</button>
                    </div>
                </div>
            </div>

            <!-- Modal -->
            <div class="modal fade" id="prAckModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
                <div class="modal-dialog" style="width: 30%;" role="document">
                    <form method="post" action="prAckLetters/uploadAcknowledgementLetter" enctype="multipart/form-data" id="uploadAckLetterForm">
                        <div class="modal-content">
                            <div class="modal-header">
                            <h4 class="modal-title" id="exampleModalLabel">Upload Acknowledgement Letter</h4>
                            <button type="button" class="close" style="font-size: xx-large;position: absolute;top: 15px;right: 15px;" data-dismiss="modal" onclick="resetForm()" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            </div>
                            
                            <div class="modal-body">
                                <input type="hidden" name="cno" id="modalCno">
                                <input type="hidden" name="otherConst" id="modalOtherConst">
                                <input type="hidden" name="totalCheques" id="modalTotalCheques">
                                <input type="hidden" name="prName" id="modalPrName">
                                <input type="hidden" name="esignedFromDate" id="modalEsignedFromDate">
                                <input type="hidden" name="esignedToDate" id="modalEsignedToDate">

                                <div class="form-group">
                                    <label for="modalCmpNo">Esigned Date From : <span id="modalEsignFromDateSpan" style="color: green;"></span> To : <span id="modalEsignToDateSpan" style="color: green;"></span></label>
                                </div>

                                <div class="form-group">
                                    <label for="modalTotalCheques">Total Cheques : <span id="modalTotalChequesSpan" style="color: green;"></span></label>
                                </div>

                                <div class="form-group">
                                    <label for="modalPrName">People Representative : <span id="modalPrNameSpan" style="color: green;"></span></label>
                                </div>

                                <div class="form-group">
                                    <label for="modalPaName">PA Name : <span style="color: red;">*</span></label>
                                    <input type="text" name="paName" id="modalPaName" maxlength="50" onkeyup="nameField(this); return uppercaseWithSymbols(this);" onchange="trimSpace(this);" autocomplete="off">
                                </div>

                                <div class="form-group">
                                    <label for="modalPaMobileNo">Mobile Number : <span style="color: red;">*</span></label>
                                    <input type="text" name="paMobileNo" id="modalPaMobileNo" onkeyup="intNumOnly(this);" onchange="chkvalidNo(this)" minlength="10" maxlength="10">
                                </div>

                                <div class="form-group">
                                    <label for="file">Upload Acknowledgement : <span style="color: red;">*</span></label>
                                    <input type="file" name="ackLetter" id="file" class="form-control-file">
                                    <span style="color: red;font-size: 10px;">Only JPG, JPEG, PNG, PDF files are allowed <br>(File Size Max: 2MB)</span>
                                </div>
                            </div>
                            
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="resetForm()">Close</button>
                                <button type="submit" class="btn btn-primary">Save</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script>
        window.onload = function () {
            filterMonths();
        }
        function filterMonths() {
            const presentYear = ${currentYear};
            const presentMonth = ${currentMonth};
            const yearSelect = document.getElementById("yearSelect");
            const monthSelect = document.getElementById("monthSelect");

            const selectedYear = parseInt(yearSelect.value);
            for (let i = 0; i < monthSelect.options.length; i++) {
                let opt = monthSelect.options[i];
                if (i === 0) continue; // skip '--Select--'

                if (selectedYear === presentYear) {
                    opt.style.display = (i - 1 <= presentMonth) ? 'block' : 'none';
                } else  if (selectedYear < presentYear){
                    opt.style.display = 'block';
                } else {
                    opt.style.display = 'none';
                }
            }
            monthSelect.value = '';
        }
    </script>
    <script>
        $(document).ready(function () {
            var basePath = "<%= basePath %>";

            $('.openCmpModal').click(function (e) {
                e.preventDefault();

                const container = Swal.getContainer();
                const backgroundElements = document.querySelectorAll('body > *:not(.swal2-container)');
                backgroundElements.forEach(element => {
                    element.style.filter = 'blur(4px)';
                });

                Swal.fire({
                    title: 'Loading...',
                    text: 'Please wait while we process your request.',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                const cname = $(this).data('cname');
                const cno = $(this).data('cno');
                const otherConst = $(this).data('otherconst');
                const dateFrom = $(this).data('datefrom');
                const dateTo = $(this).data('dateto');

                $('#cmpModalLabel').text("People Representative : " + cname);
                $('#cmpDetailsBody').html("<tr><td colspan='2'>Loading...</td></tr>");

                $.ajax({
                    url: basePath + '/prAckLetters/getCmpDetails',
                    method: 'GET',
                    data: {
                        dateFrom: dateFrom,
                        dateTo: dateTo,
                        cno: cno,
                        otherConst: otherConst
                    },
                    success: function(response) {
                        Swal.close();

                        backgroundElements.forEach(element => {
                            element.style.filter = 'none';
                        });

                        let html = '';

                        if (response.length > 0) {
                            response.forEach(function(item) {
                                html += '<tr>' +
                                            '<td>' + item.cmp_no + '</td>' +
                                            '<td>' + item.cheques_cnt + '</td>' +
                                        '</tr>';
                            });
                        } else {
                            html = "<tr><td colspan='2'>No data available</td></tr>";
                        }

                        document.getElementById('cmpDetailsBody').innerHTML = html;
                        document.getElementById('cmpDetailsModal').style.display = 'flex';
                    },
                    error: function() {
                        Swal.close();
                        backgroundElements.forEach(element => {
                            element.style.filter = 'none';
                        });
                        alert("Failed to fetch CMP details.");
                    }
                });
            });

            // Close button handler
            $('#closeCmpDetails').click(function () {
                $('#cmpDetailsModal').hide();
            });
        });
    </script>
    <script>
        $(document).on('click', '.upload-btn', function () {
            var cno = $(this).data('cno');
            var otherConst = $(this).data('other-const');
            var prName = $(this).data('pr-name');
            var totalCheques = $(this).data('total-cheques');
            var esignedFromDate = $(this).data('esigned-from-date');
            var esignedToDate = $(this).data('esigned-to-date');

            $('#modalCno').val(cno);
            $('#modalOtherConst').val(otherConst);
            $('#modalPrName').val(prName);
            $('#modalTotalCheques').val(totalCheques);
            $('#modalEsignedFromDate').val(esignedFromDate);
            $('#modalEsignedToDate').val(esignedToDate);

            $('#modalEsignFromDateSpan').text(esignedFromDate);
            $('#modalEsignToDateSpan').text(esignedToDate);
            $('#modalPrNameSpan').text(prName);
            $('#modalTotalChequesSpan').text(totalCheques);
        });

        function resetForm() {
            const form = $('#uploadAckLetterForm');
            form[0].reset();

            form.find('.is-invalid, .is-valid').removeClass('is-invalid is-valid');
            form.find('span.invalid-feedback').remove();

            $('#modalEsignFromDateSpan').text('');
            $('#modalEsignToDateSpan').text('');
            $('#modalTotalChequesSpan').text('');
            $('#modalPrNameSpan').text('');
        }

        $(document).ready(function (){
            // Custom rule for file extension
            $.validator.addMethod("fileextension", function (value, element, param) {
                if (element.files.length === 0) return true;
                var extension = value.split('.').pop().toLowerCase();
                return $.inArray(extension, param) !== -1;
            }, "Invalid file type.");

            // Custom rule for file size
            $.validator.addMethod("filesize", function (value, element, param) {
                if (element.files.length === 0) return true;
                return element.files[0].size <= param;
            }, "File size is too large.");

            $('#uploadAckLetterForm').validate({
                rules: {
                    paName: {
                        required: true
                    },
                    paMobileNo: {
                        required: true,
                        minlength: 10,
                        maxlength: 10
                    },
                    ackLetter: {
                        required: true,
                        fileextension: ["jpeg", "jpg", "png", "pdf"],
                        filesize: 2097152,
                    }
                },
                messages: {
                    paName: {
                        required: 'Please enter PA Name'
                    },
                    paMobileNo: {
                        required: 'Please enter Mobile Number',
                        minlength: 'Mobile Number must be 10 digits',
                        maxlength: 'Mobile Number must be 10 digits'
                    },
                    ackLetter: {
                        required: 'Please select Acknowledgement Letter',
                        fileextension: "Only JPG, PNG, PDF files are allowed",
                        filesize: "File size must not exceed 2 MB",
                    }
                },
                errorElement: "span",
                errorClass: "invalid-feedback",
                highlight: function (element, errorClass, validClass) {
                    $(element).addClass("is-invalid").removeClass("is-valid");
                },
                unhighlight: function (element, errorClass, validClass) {
                    $(element).removeClass("is-invalid").addClass("is-valid");
                },
                submitHandler: function (form) {
                    Swal.fire({
                        title: 'Do you want to submit the details now?',
                        text: "",
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6', 
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes, submit it!',
                        cancelButtonText: 'No, cancel!'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            form.submit();
                        }else{
                            return false;
                        }
                    });
                },
            });
        });
    </script>
</body>
</html>

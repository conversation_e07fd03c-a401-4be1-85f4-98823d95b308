package com.cgg.dataentry.repositories;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.cgg.dataentry.entities.MlaCmrf;
import com.cgg.dataentry.model.MlaCmrfEntityModel;


@Repository
public interface UpdateMlaCmrfSpecilaCaseRepository extends JpaRepository<MlaCmrf, String> {
	
	
	@Query("SELECT new com.cgg.dataentry.model.MlaCmrfEntityModel(m.mlaCmrfNo, m.cmrfNo, m.patientName, m.fatherSonOf,  m.patAddress,m.purpose, h.hospname, c.cname,c.mlamp, m.isSpecialFlag ) " 
		     + " FROM MlaCmrf m " 
		     + " INNER JOIN Hospital h ON m.hospCode = h.hospcode " 
		     + " INNER JOIN Constituency c ON c.cno =CAST(m.recommendedBy AS integer) "
		     + " WHERE m.mlaCmrfNo = :mlaCmrfNo AND m.status != '11'")
	MlaCmrfEntityModel getMlaCmrfDetails(@Param("mlaCmrfNo") String mlaCmrfNo);

	@Transactional
	@Modifying
	@Query(" UPDATE  MlaCmrf  set isSpecialFlag=:isSpecialFlag,specialFlgUpdatedOn=now(),specialFlgUpdatedBy=:userId,specialFlgReferredBy=:referredBy  "
			+ " WHERE mlaCmrfNo = :mlaCmrfNo AND status != '11' ")
	int updateMlaCmrfSpecialCase(@Param("mlaCmrfNo") String mlaCmrfNo, @Param("isSpecialFlag") boolean isSpecialFlag,@Param("userId") String userId,@Param("referredBy") String referredBy);

}

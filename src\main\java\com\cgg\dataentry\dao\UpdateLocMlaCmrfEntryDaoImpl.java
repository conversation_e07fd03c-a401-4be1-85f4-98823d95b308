package com.cgg.dataentry.dao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.cgg.common.CommonUtils;
import com.cgg.dataentry.model.MlaCmrfEntryForm;

@Repository
public class UpdateLocMlaCmrfEntryDaoImpl implements UpdateLocMlaCmrfEntryDao{
	@Autowired
	private DataSource dataSource;
	
	@Autowired
	JdbcTemplate jdbcTemlate;

	@Override
	public String updateLocMlaCmrfDetails(MlaCmrfEntryForm formBean, Map<String, Object> model,HttpServletRequest request)throws Exception {
		// TODO Auto-generated method stub
		Connection	con=null;
		Statement	st=null;
		
		String mesg=null;
		ResultSet rs=null;
		
		try {
			con = dataSource.getConnection();
			 con.setAutoCommit(false);
			st=con.createStatement();
			
			//String userId="cmrfdeo";	
			String qry=null;
	
			 int upd=0;
			 String rasf_string=null;
			 HttpSession session=request.getSession();
			 String userId=(String)session.getAttribute("userid");
             
             String mlaCmrfNo=formBean.getHidmlaCmrfNo();
			if(mlaCmrfNo!=null && !"".equals(mlaCmrfNo)) {
				String sql = "select mla_loc_no from loc_mla_cmrf  where mla_loc_no='" + mlaCmrfNo + "'  and delete_flag=false";
                System.out.println("sql::::::::::::::::::::::::::::::::::: " + sql);
                rasf_string = getStringfromQuery(sql);
                System.out.println("rasf_string::::::::::::::::::::::::::::::::::: " + rasf_string.length());
                if(rasf_string==null || rasf_string.equals("")){
                   
                    mesg="Invalid MlA Inward Id";
                  formBean.setMlaCmrfSno("");
                  formBean.setStatus("0");
                    

                }else {
                	//update
                	qry = "insert into loc_mla_cmrf_log(mla_loc_no, patient_name, "
            				+ "father_son_of, purpose, assured_amt, recommended_by, hosp_code, status, loc_no, entered_on,"
            				+ " delete_flag, updated_on, rej_reasons, address, vip_letter_dt,ip_address,logged_timestamp,logged_ipaddress,logged_by,"
            				+ "logged_remarks,updated_by)"
            				+ "select mla_loc_no, patient_name, "
            				+ "father_son_of, purpose, assured_amt, recommended_by, hosp_code, status, loc_no, entered_on,"
            				+ " delete_flag, updated_on, rej_reasons, address, vip_letter_dt,ip_address,now(),'"+request.getRemoteAddr()+"','"+userId+"'"
            						+ ",'Edit Loc',updated_by " 
            						+ " from loc_mla_cmrf where mla_loc_no = '"+formBean.getHidmlaCmrfNo()+"'" ;
  	            
  	              System.out.println("In updateDB upd"+qry);
  	              int res = st.executeUpdate(qry);           
  	              if(res>0)
  	              {
  	             qry = "update loc_mla_cmrf set status='"+formBean.getStatus()+"',rej_reasons='"+formBean.getRejReasons()+"'"
  	                   +" ,updated_by = '" + userId + "', updated_on = '" + LocalDateTime.now() + "', " + "ip_address = '" + request.getRemoteAddr() + "' "
  	                    + " where mla_loc_no ='"+formBean.getHidmlaCmrfNo()+"'";
  	                   System.out.println("In updateDB qry"+qry);
  		
  	              upd = st.executeUpdate(qry);
  	              System.out.println("In updateDB upd"+upd);

  	              if(upd==1)
  	              {
  	            	  mesg="Status Updated Successfully";
  	                  con.commit();
  	              }else{
  	            	  mesg="Updation Failed";
  	                  con.rollback();
  	              }
                	
                }
				
			}
			
			
			}
			}
			catch(Exception e) {
				e.printStackTrace();
			}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
			
		return mesg;
	}
	public String getStringfromQuery(String sql) {
		String matrix = "";
		Connection con = null;
		Statement st = null;
		ResultSet rs = null;
		try {

			con = dataSource.getConnection();
			st=con.createStatement();
			rs = st.executeQuery(sql);
                        System.out.println("--hello-");
			if (rs.next()){
				matrix = rs.getString(1);
                                System.out.println("--matrix-"+matrix);
                        }
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				if(con!=null && !con.isClosed()) {con.close();con=null;}
			} catch (SQLException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
                System.out.println("--matrix-end"+matrix);
		return matrix;
	}
	}
	

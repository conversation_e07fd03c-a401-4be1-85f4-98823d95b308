package com.cgg.Schedulers;

import java.util.List;
import java.util.Map;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.cgg.dataentry.entities.MlaCmrf;

@Repository
public interface SchedulerRepo extends JpaRepository<MlaCmrf, String> {
    
    @Query(value = "SELECT hospname, mobile_no, SUM(pending_count) AS pending_count FROM ( "
            + " SELECT a.hosp_code,hospname, d.mobile_no, dt.distname, COUNT(1) AS pending_count ,CASE WHEN d.is_hosp_online = 'true' THEN 'Online' ELSE 'Offline' END AS hosp_status "
            + " FROM mla_cmrf a INNER JOIN hospital d ON d.hospcode = a.hosp_code "
            + " LEFT JOIN  district dt ON dt.distno = d.dist_code "
            + " WHERE (patient_ip != '0' OR patient_ip IS NOT NULL) AND cmrf_no IS NULL AND verified_by_deo IS NULL AND hos_verified_date IS NULL AND d.delete_flag='false'  AND status='1' "
            + " AND patient_ip_status IS NULL AND user_id IN (SELECT userid FROM users WHERE role_id != 24) "
            + " GROUP BY hospname, d.mobile_no, dt.distname, d.is_hosp_online,hosp_code "
            + " UNION ALL "
            + " SELECT a.hosp_code,hospname, d.mobile_no, dt.distname, COUNT(1) AS pending_count,CASE WHEN d.is_hosp_online = 'true' THEN 'Online' ELSE 'Offline' END AS hosp_status "
            + " FROM mla_cmrf a INNER JOIN hospital d ON d.hospcode = a.hosp_code "
            + " LEFT JOIN  district dt ON dt.distno = d.dist_code "
            + " WHERE (patient_ip != '0' OR patient_ip IS NOT NULL) AND cmrf_no IS NULL AND verified_by_deo IS NULL AND hos_verified_date IS NULL AND d.delete_flag='false'  AND status='7' "
            + " AND patient_ip_status IS NULL AND user_id IN (SELECT userid FROM users WHERE role_id = 24) "
            + " GROUP BY hospname, d.mobile_no, dt.distname, d.is_hosp_online,hosp_code )a "
            + " GROUP BY hospname, mobile_no, hosp_status, distname,hosp_code ORDER BY pending_count DESC;", nativeQuery = true)
    List<Map<String, String>> getHospitalPendingCount();

    @Modifying
    @Transactional
    @Query(value = "Insert into hosp_pend_cnt_sms (mobile_no, msg, sent_date, sms_response) "
            + "  values (?1, ?2, CURRENT_TIMESTAMP, ?3)", nativeQuery = true)
    void insertIntoHospPendSms(Long mobile_no, String message, String sms_response);

}

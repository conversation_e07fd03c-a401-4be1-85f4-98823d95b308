package com.cgg.dataentry.service;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.dataentry.dao.SendDataDao;
import com.cgg.dataentry.model.SendData;

@Service
public class SendDataServiceImpl implements SendDataService{
	
	@Autowired
	private SendDataDao sendDataDao;

	@Override
	public List<SendData> getCmpNos(){

		List<SendData> cmpNos = new ArrayList<SendData>();
		try
		{
			cmpNos = sendDataDao.getCmpNos();
		}
		catch (Exception e) {
			e.printStackTrace();
		}
		return cmpNos;
	}

	@Override
	public int update(String controlNo,HttpServletRequest request) throws Exception{
		return sendDataDao.update(controlNo,request);
	}

}

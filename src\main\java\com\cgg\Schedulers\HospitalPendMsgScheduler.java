package com.cgg.Schedulers;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.cgg.common.BSNLSMSHttpPostClient;

@Component
@Profile("prod")
public class HospitalPendMsgScheduler {

    private static final String TEMPLATE_NAME_HOSP_PEND = "TSCMRF_17may2025_1";
    private static final String TEMPLATE_ID_HOSP_PEND = "1407174746036635228";

    private static final Logger logger = LoggerFactory.getLogger(HospitalPendMsgScheduler.class);

    @Autowired
    SchedulerRepo repo;

    @Scheduled(cron = "0 0 11 * * *")
    public void hospPendingCntMSGScheduler() {

        String hospName = "", mobileNo = "", pendingCount = "";

        List<Map<String, String>> hospitalPendingCount = repo.getHospitalPendingCount();

        if (hospitalPendingCount != null && !hospitalPendingCount.isEmpty()) {

            //send first sms to SOMASHEKAR.
            try {
                logger.info("-----> Sending SMS to SOMASHEKAR <-----");
                sendHospPendCntSmsNotifications("SAKSHATH", "9390359959", "1");
                sendHospPendCntSmsNotifications("SOMASHEKAR", "9182757255", "1");
            } catch (Exception e) {
                e.printStackTrace();
            }

            for (Map<String,String> map : hospitalPendingCount) {
                
                // System.out.println(map.get("hospname") + " - " + map.get("mobile_no") + " - " + String.valueOf(map.get("pending_count")));

                hospName = map.get("hospname");
                if (hospName != null && !hospName.isEmpty() && hospName.length() > 40) {
                    hospName = hospName.substring(0, 40);
                }
                mobileNo = map.get("mobile_no");
                pendingCount = String.valueOf(map.get("pending_count"));

                try {
                    sendHospPendCntSmsNotifications(hospName, mobileNo, pendingCount);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void sendHospPendCntSmsNotifications(String hospName, String mobileNo, String pendingCount) throws Exception {

		String msg = "Dear Sir/Madam, " + pendingCount + " CMRF applications are pending in your " + hospName + " login. Pls clear the pendency as early as possible in order to process CMRF applications pertaining to the patients who have taken the treatment in your hospital. For any queries,pls send the mail to following email:<EMAIL> - TGCMRF";
					
		if (mobileNo != null && !mobileNo.isEmpty()) {
		    try {
                String connection = BSNLSMSHttpPostClient.sendBSNLSms(msg, "91" + mobileNo, TEMPLATE_ID_HOSP_PEND, TEMPLATE_NAME_HOSP_PEND);
				if ("200".equals(connection)) {
					repo.insertIntoHospPendSms(Long.parseLong(mobileNo), msg, connection);
				} else {
                    repo.insertIntoHospPendSms(Long.parseLong(mobileNo), msg, connection);
                }
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
    }

}

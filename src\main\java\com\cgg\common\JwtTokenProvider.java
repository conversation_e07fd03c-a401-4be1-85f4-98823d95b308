package com.cgg.common;

import org.springframework.stereotype.Component;

import com.auth0.jwt.JWT;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;

@Component
public class JwtTokenProvider {
    // Validate JWT token expiration
	  public static boolean isTokenExpired(String token) {
	        try {
	            DecodedJWT jwt = JWT.decode(token);
	            long expirationTime = jwt.getExpiresAt().getTime();
	            long currentTime = System.currentTimeMillis();
	            return expirationTime < currentTime;
	        } catch (JWTDecodeException exception) {
	            return true;
	        }
	    }
	  
	  
	  
	  
	  public static void main(String[] args) {
	        String token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.****************************************************************.-TD5JkgdM1d52UaA3vYffjyPIA8Obm_9p5vZvLzaz_i-C6gljRAsPNB7hFT1TP0gj2Mtn-ZZMlJ3RSyxzH8DzA";
	        boolean isExpired = isTokenExpired(token);
	        if (isExpired) {
	            System.out.println("Token is expired.");
	        } else {
	            System.out.println("Token is not expired.");
	        }
	    }
}

package com.cgg.common;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
@Controller
public class CommonFunctions {

	@Autowired
	private DataSource dataSource;

	public static boolean validateSelectBox(String data) {
		if (validateData(data) && !"0".equals(data))
			return true;

		return false;
	}
	public static boolean validateDateRange(String fromDate, String toDate) {
	    // First validate both dates are not null or empty
	    if (!validateData(fromDate) || !validateData(toDate)) {
	        return false;
	    }

	    // Parse the dates and compare them
	    try {
	        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd"); // Adjust pattern to your date format
	        LocalDate from = LocalDate.parse(fromDate, formatter);
	        LocalDate to = LocalDate.parse(toDate, formatter);

	        // Check if toDate is greater than or equal to fromDate
	        return !to.isBefore(from);
	    } catch (DateTimeParseException e) {
	        // Handle invalid date format
	        return false;
	    }
	}
	public static boolean validateData(String data) {
		if (data != null && !"".equals(data.trim()))
			return true;
		else
			return false;
	}
	public static boolean validate(String data) {
		if (data != null && !"".equals(data.trim()) && !"0".equals(data.trim()))
			return true;
		else
			return false;
	}

	// keys as db columns and used for insert query.
	public static Map<String, Object> insertQuery(Map<String, Object> hmap) {

		StringBuilder colName = new StringBuilder();
		StringBuilder colValues = new StringBuilder();
		Map<String, Object> queryMap = new HashMap<String, Object>();
		String key = "";

		Iterator<String> iter = hmap.keySet().iterator();
		while (iter.hasNext()) {
			key = iter.next();
			if (validateData(key) && validateData((String) hmap.get(key))) {
				colName.append(key + ",");
				colValues.append(hmap.get(key) + ",");
			}
		}
		queryMap.put("colNames", colName.substring(0, colName.lastIndexOf(",")));
		queryMap.put("colValues", colValues.substring(0, colValues.lastIndexOf(",")));

		return queryMap;
	}

	public int getCaptchaCount(String sql) {
		Integer count=null;
		try(Connection con = dataSource.getConnection()) {
			 try (PreparedStatement pstmt = con.prepareStatement(sql)) {
	                
	                try (ResultSet rs = pstmt.executeQuery()) {
	                    if (rs.next()) {
	                        count = rs.getInt("count");
	                    }
	                }
	            }
		} catch (Exception e) {
			e.printStackTrace();
		}
		return count;
	}
	
	@PostMapping("/saveCaptcha")
    public void saveCaptcha(@RequestBody CaptchaRequest captchaRequest, HttpServletRequest request, HttpServletResponse response) throws SQLException {
        String user_id = captchaRequest.getUserId();
        String captcha = captchaRequest.getCaptcha();

        if (!(user_id == null && captcha == null)) {
        String recordExitsSql = "SELECT COUNT(*) AS cnt FROM tgcmrf_captcha WHERE inserted_by = ?";
        String insertSql = "INSERT INTO tgcmrf_captcha (main_captcha, inserted_by, inserted_date_time, inserted_ip_address) VALUES (?, ?, NOW(), ?)";
        String updateSql = "UPDATE tgcmrf_captcha SET main_captcha = ?, updated_by = ?, updated_date_time = NOW(), updated_ip_address = ? WHERE main_captcha IS NOT NULL AND inserted_by = ?";

        try (Connection con = dataSource.getConnection()) {
            // Check if record exists
            int count = 0;
            try (PreparedStatement pstmt = con.prepareStatement(recordExitsSql)) {
                pstmt.setString(1, user_id);
                try (ResultSet rs = pstmt.executeQuery()) {
                    if (rs.next()) {
                        count = rs.getInt("cnt");
                    }
                }
            }

            // Insert or update based on record existence
            if (count <= 0) {
                try (PreparedStatement pstmt = con.prepareStatement(insertSql)) {
                    pstmt.setString(1, captcha);
                    pstmt.setString(2, user_id);
                    pstmt.setString(3, request.getRemoteAddr());
                    pstmt.executeUpdate();
                }
            } else {
                try (PreparedStatement pstmt = con.prepareStatement(updateSql)) {
                    pstmt.setString(1, captcha);
                    pstmt.setString(2, user_id);
                    pstmt.setString(3, request.getRemoteAddr());
                    pstmt.setString(4, user_id);
                    pstmt.executeUpdate();
                }
            }
        
        } catch (SQLException e) {
            e.printStackTrace();
            throw new SQLException("Error processing captcha", e);
        }}
    }

    public static class CaptchaRequest {
        private String userId;
        private String captcha;

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getCaptcha() {
            return captcha;
        }

        public void setCaptcha(String captcha) {
            this.captcha = captcha;
        }
    }
    
    public String insertUserAuditLog(HttpServletRequest request,String userId,String serviceType) throws Exception{
    	
    	boolean insertLogFlag = false;
    	String result=null;
    	String sql=null;
    	
    	try {
    		 Connection con = dataSource.getConnection();
    
    		 sql= " INSERT INTO public.cmrf_users_audit_log(user_id,ip_address,time_stamp,service_type)  VALUES (?, ?, now(),?) ";
    		 
    		 PreparedStatement pstmtInsertAuditLog = con.prepareStatement(sql);
    		 
    		 pstmtInsertAuditLog.setString(1, userId);
    		 pstmtInsertAuditLog.setString(2, request.getRemoteAddr());
    		 pstmtInsertAuditLog.setString(3, serviceType);
    		 
    		 insertLogFlag= pstmtInsertAuditLog.executeUpdate()>0;

    	     if (insertLogFlag) {
    	         result="User audit log inserted and using service --"+serviceType;
    	     }
    	  } catch (SQLException e) {
    	       e.printStackTrace();  
    	       throw new Exception("Error occurred while inserting user audit log", e);
    	  }
    	return result;
    }
    
    public static String capitalizeWords(String input) {
		if (input == null || input.isEmpty()) {
			return input;
		}

		// Split the input by spaces and periods, retaining them in the split result
		String[] segments = input.split("(?=[\\s.])|(?<=[\\s.])");
		StringBuilder capitalized = new StringBuilder();

		for (String segment : segments) {
			if (segment.trim().isEmpty()) {
				capitalized.append(segment); // Preserve spaces
			} else {
				// Capitalize the first character if it's a letter
				char firstChar = segment.charAt(0);
				String restOfSegment = segment.substring(1).toLowerCase();

				if (Character.isLetter(firstChar)) {
					capitalized.append(Character.toUpperCase(firstChar)).append(restOfSegment);
				} else {
					// Preserve non-letter segments as is
					capitalized.append(segment);
				}
			}
		}

		return capitalized.toString();
	}
    
    
}


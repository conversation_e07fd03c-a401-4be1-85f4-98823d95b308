package com.cgg.dataentry.service;

import java.util.List;
import java.util.Optional;

import javax.persistence.Tuple;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.common.CommonUtils;
import com.cgg.common.Response;
import com.cgg.dataentry.entities.Ifsccodes;
import com.cgg.dataentry.repositories.AddIfscRepository;
import com.cgg.hospital.model.InwardCmrf;

@Service
public class AddIfscService {
	
	@Autowired
	AddIfscRepository addIfscRepo;
	
	public  Optional<Ifsccodes> getBankDetails(String ifsc) {
		return addIfscRepo.findById(ifsc);
	}
	public List getDistricts() {
		List<Tuple> districts = addIfscRepo.getDistricts();
		return CommonUtils.convertTuplesToListMap(districts);
	}
	public List getBanks() {
		List<Tuple> banks = addIfscRepo.getBanks();
		return CommonUtils.convertTuplesToListMap(banks);
	}
	public Ifsccodes saveIfscCode(InwardCmrf inwardCmrf) {
		Ifsccodes details = null;
		try {
	    Ifsccodes ifsccodes = new Ifsccodes();
	    ifsccodes.setDistrict(inwardCmrf.getBankDistrict());
	    ifsccodes.setBankName(inwardCmrf.getBankName());
	    ifsccodes.setIfscCode(inwardCmrf.getBankIfsc());
	    ifsccodes.setBranch(inwardCmrf.getBankBranch());
	    ifsccodes.setState("TELANGANA");
	    details = addIfscRepo.save(ifsccodes);
		
		}catch (Exception e) {
			e.printStackTrace();
		}
		return details;
		
	}

}

package com.cgg.reports.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.persistence.Tuple;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.common.CommonUtils;
import com.cgg.common.Encryptor;
import com.cgg.reports.model.DeoVerifiedPatientStatusReport;
import com.cgg.reports.repositories.CMRFEnhancementReportRepository;

@Service
public class CMRFEnhancementReportService {
	
	@Autowired
	private CMRFEnhancementReportRepository cmrfEnhancementReportRepository;
	
	public List<?> getCMRFEnhancementReport(DeoVerifiedPatientStatusReport deoVerifiedStatusForm) throws Exception {
		String reportType = deoVerifiedStatusForm.getReportType();
		List<Tuple> tuples = null;

		if ("TotalCases".equals(reportType)) {
			tuples = cmrfEnhancementReportRepository.getCmrfEnhancementTotalCasesReport(
					deoVerifiedStatusForm.getDateFrom(),
					deoVerifiedStatusForm.getDateTo()
			);
			return CommonUtils.convertTuplesToListMap(tuples);
		} else if ("PRWise".equals(reportType)) {
			tuples = cmrfEnhancementReportRepository.getCmrfEnhancementPRWiseReportCnt(
					deoVerifiedStatusForm.getDateFrom(),
					deoVerifiedStatusForm.getDateTo()
			);

			List<DeoVerifiedPatientStatusReport> prList = new ArrayList<>();
			for (Tuple tuple : tuples) {
				DeoVerifiedPatientStatusReport report = new DeoVerifiedPatientStatusReport();

				String cno = tuple.get("cno", Integer.class).toString();
				String otherConst = tuple.get("other_const", String.class);
				String mlamp = tuple.get("mlamp", String.class);
				String cases = tuple.get("cases", String.class);

				report.setConstNo(Encryptor.encrypt(cno));
				report.setVipName(mlamp);
				report.setConstName(mlamp);
				report.setCases(cases);
				report.setOtherConst(Encryptor.encrypt("998".equals(cno) ? otherConst : "-"));
				report.setDateFrom(deoVerifiedStatusForm.getDateFrom());
				report.setDateTo(deoVerifiedStatusForm.getDateTo());
				report.setReportType("PRWise");

				prList.add(report);
			}
			return prList;
		}
		return Collections.emptyList();
	}
	
	
	public List<?> getPRWiseEnhancementDetails(DeoVerifiedPatientStatusReport deoVerifiedStatusForm) throws Exception {
		String constNo = deoVerifiedStatusForm.getConstNo();
		String otherConst = deoVerifiedStatusForm.getOtherConst();
		List<Tuple> tuples = null;

		if (constNo != null && "998".equals(constNo)) {
			tuples = cmrfEnhancementReportRepository.getPRWiseEnhancementDetailsWithOtherConst(
				constNo,
				otherConst,
				deoVerifiedStatusForm.getDateFrom(),
				deoVerifiedStatusForm.getDateTo()
			);
		} else {
			tuples = cmrfEnhancementReportRepository.getPRWiseEnhancementDetails(
				constNo,
				deoVerifiedStatusForm.getDateFrom(),
				deoVerifiedStatusForm.getDateTo()
			);
		}
		return CommonUtils.convertTuplesToListMap(tuples);
	}

}

package com.cgg.dataentry.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.cgg.dataentry.model.AddHospitalForm;
import com.cgg.dataentry.service.AddHospitalService;

@Controller
public class AddHospitalController {
	
	@Autowired
	AddHospitalService addHospitalService;
	
	@GetMapping("/addHospital")
	public String addHospital(Map<String, Object> model,HttpSession session) throws Exception {
		String userId = null;
		String roleId = null;
		List<String> validRoleIds = Arrays.asList("2");
		userId = (String) session.getAttribute("userid");
		roleId=(String) session.getAttribute("rolesStr");
		if(roleId==null || ! validRoleIds.contains(roleId) || userId==null) {
			 return  "redirect:/";
		}
		List<AddHospitalForm> districts = new ArrayList<AddHospitalForm>();
		districts = addHospitalService.getDistricts();
		model.put("districts",districts);
		return "addHospital";
	}
	@PostMapping("/addHospitalDetails")
	public String addHospitalDetails(@ModelAttribute AddHospitalForm addHospitalForm,HttpServletRequest request,Model model, RedirectAttributes redirect,HttpSession session) {
		String userId = (String) session.getAttribute("userid");
		try {
		List<String> errors = new ArrayList<>();
		
		addHospitalForm.setEnteredBy(userId);

        if (addHospitalForm.getHospitalName() == null || addHospitalForm.getHospitalName().isEmpty()) {
            errors.add("Hospital name is required");
        }

        if (addHospitalForm.getHospitalOnCheque() == null || addHospitalForm.getHospitalOnCheque().isEmpty()) {
            errors.add("Name on cheque is required");
        }

        if (addHospitalForm.getLocation() == null || addHospitalForm.getLocation().isEmpty()) {
            errors.add("Location is required");
        }

        if (addHospitalForm.getRecognition() == null || addHospitalForm.getRecognition().isEmpty()) {
            errors.add("Recognition status is required");
        }

        if (addHospitalForm.getOnline() == null || addHospitalForm.getOnline().equalsIgnoreCase("") ) {
            errors.add("Online status is required");
        }
       if (addHospitalForm.getMobileNo() == null || addHospitalForm.getMobileNo().equals("0") ) {
            errors.add("Online status is required");
        }
        if (!errors.isEmpty()) {
        	model.addAttribute("error", errors);
        }
		List<AddHospitalForm> districts = new ArrayList<AddHospitalForm>();
		districts = addHospitalService.getDistricts();
		model.addAttribute("districts",districts);
		
		Integer hospcode = addHospitalService.addHospital(addHospitalForm,request);
		System.out.println("hospcode===="+hospcode);
		if(hospcode==-1) {
			redirect.addFlashAttribute("error", "Mobile no already exists for another hospital ");
		//	model.addAttribute("success", "successfully added hospital with hospcode : "+hospcode);
		}
		else if(hospcode>0) {
			redirect.addFlashAttribute("success", "Successfully added hospital with hospcode : "+hospcode);
		//	model.addAttribute("success", "successfully added hospital with hospcode : "+hospcode);
		}else {
		//	model.addAttribute("error", "error in adding hospital");
			redirect.addFlashAttribute("error", "Error in adding hospital");

		}}catch (Exception e) {
			e.printStackTrace();
		}
		//return "addHospital";
		return "redirect:/addHospital";
	}

}

package com.cgg.dataentry.dao;

import java.util.List;
import java.util.Map;

import com.cgg.dataentry.model.LocDetailsEntryForm;

public interface LocDetailsEntryDao {
	public List<LocDetailsEntryForm> getRecommendedDetails() throws Exception;
	public List<LocDetailsEntryForm> getHospitalList() throws Exception;
	public String saveLocDetails(LocDetailsEntryForm locEntryForm, Map<String, Object> model)throws Exception;
	public String getLocData(String locTokenNo)throws Exception;
	public List<Map<String, Object>> getCmrfLocDetailsByAadhar(String uid,String locTokenNo)throws Exception;
	public LocDetailsEntryForm getLocLetterData(String locNo) throws Exception;
	public List<LocDetailsEntryForm> getDistricts() throws Exception;

	

}

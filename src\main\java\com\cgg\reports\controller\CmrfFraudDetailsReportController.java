package com.cgg.reports.controller;

import java.util.Arrays;
import java.util.List;

import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import com.cgg.common.ApplicationConstants;
import com.cgg.reports.model.CMRFEntryDetailsReport;
import com.cgg.reports.service.CmrfFraudDetailsReportService;


@Controller
@RequestMapping(value = "/cmrfFraudDetailsReport")
public class CmrfFraudDetailsReportController 
{
	@Autowired
	private CmrfFraudDetailsReportService cmrfFraudDetailsReportService;
	
	@GetMapping
	public ModelAndView viewPage(HttpSession session) throws Exception
	{
		List<Integer> validRoleIds = Arrays.asList(ApplicationConstants.CMRF_OFFICER,Integer.parseInt(ApplicationConstants.CMRF_OSD_ROLE));
		String roleId = (String) session.getAttribute("rolesStr");
		String userId=null;
		if(session!=null && session.getAttribute("userid")!=null) userId=(String)session.getAttribute("userid");
	
		  if (userId == null || userId.equals("null") || roleId == null ||  ! validRoleIds.contains(Integer.parseInt(roleId))) {
			 return new ModelAndView("redirect:/");
		  }
		
		ModelAndView mav = new ModelAndView();
		CMRFEntryDetailsReport cmrfEntryDetailsReport = new CMRFEntryDetailsReport();
		mav.addObject("CMRFEntryDetailsReport", cmrfEntryDetailsReport);
		mav.setViewName("cmrfFraudDetailsReport");
		return mav;
	}
	
	@PostMapping
	public ModelAndView getCmrfFraudDetailsReport(@ModelAttribute("CMRFEntryDetailsReport") CMRFEntryDetailsReport cmrfEntryDetailsReport,HttpSession session) throws Exception 
	{
		  ModelAndView mav = new ModelAndView();
		  String userId=null;
		  if(session!=null && session.getAttribute("userid")!=null) userId=(String)session.getAttribute("userid");		
		     if(userId==null || userId.equals("null") ) {
			    return  null;
		     }
		   
		  List<CMRFEntryDetailsReport> cmrfFraudDetailsReportData = cmrfFraudDetailsReportService.getCmrfFraudDetailsReport(cmrfEntryDetailsReport);
		 
		  if(cmrfFraudDetailsReportData.size() == 0) {
			  mav.addObject("msg", "No Data Found");
		  }
		  else {			
			   mav.addObject("cmrfFraudDetailsReportData", cmrfFraudDetailsReportData);			  
		  }
		  mav.addObject("statusFromDate",cmrfEntryDetailsReport.getCmrfDate());
		  mav.addObject("statusToDate",cmrfEntryDetailsReport.getCmrfToDate());
		  mav.setViewName("cmrfFraudDetailsReport");
		  return mav;
	  }
}

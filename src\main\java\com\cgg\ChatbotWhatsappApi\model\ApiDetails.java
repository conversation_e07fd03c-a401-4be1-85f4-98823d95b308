package com.cgg.ChatbotWhatsappApi.model;

public class ApiDetails {

    public String phoneNo;
    public String message;
    public String locNo;
    public String tokenNo;
    public String applicantName;
    public String status;
    
    public String getPhoneNo() {
        return phoneNo;
    }
    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }
    public String getMessage() {
        return message;
    }
    public void setMessage(String message) {
        this.message = message;
    }
    public String getStatus() {
        return status;
    }
    public void setStatus(String status) {
        this.status = status;
    }
    public String getTokenNo() {
        return tokenNo;
    }
    public void setTokenNo(String tokenNo) {
        this.tokenNo = tokenNo;
    }
    public String getLocNo() {
        return locNo;
    }
    public void setLocNo(String locNo) {
        this.locNo = locNo;
    }
    public String getApplicantName() {
        return applicantName;
    }
    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }
    
    @Override
    public String toString() {
        return "ApiDTO [phoneNo=" + phoneNo + ", message=" + message + ", locNo=" + locNo + ", tokenNo=" + tokenNo
                + ", applicantName=" + applicantName + ", status=" + status + "]";
    }
}

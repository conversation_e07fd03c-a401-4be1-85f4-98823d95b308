<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<% 
    String basePath = request.getContextPath()+"/"; 
 %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print PR Acknowledgement Letter</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode-generator/1.4.4/qrcode.min.js"></script>
    <link rel="stylesheet" href="<%=basePath%>css/bootstrap5.3.3/css/bootstrap.min.css">
    <style>
        #myTable th {
            text-align: center;
            margin-top: 10px;
        }

        p {
            margin-bottom: 7px;
        }

        @media print {
            .empty{
                display: none;
            }
            .data{
                width: 100% !important;
            }
            .table {
                font-size: 14px !important; /* default font size for print */
            }

            .table.large-list {
                font-size: 12px !important; /* smaller font if list > 15 */
            }
        }

        #myTable tbody tr td {
            padding: 2px;
        }
    </style>

</head>
<body>
    <div class="container-fluid d-flex">
        <div class="empty" style="width: 10%;"></div>
        <div class="data mt-2" style="width: 80%;">
            <h6 class="text-center">People Representative : ${cmpDetailsList[0].getCname()}</h6>
            <c:set var="isLargeList" value="${fn:length(cmpDetailsList) > 15}" />
            <table align="center" class="table table-bordered border border-2 ${isLargeList ? 'large-list' : ''}" id="myTable">
                <thead class="table-dark">
                    <tr>
                        <th colspan="5">Ackonwledgement Details</th>
                    </tr>
                    <tr>
                        <th width="10%">Sl No.</th>
                        <th width="30%">Series</th>
                        <th width="15%">Esigned Date</th>
                        <th width="10%">Number of Cheques</th>
                        <th width="35%">Remarks</th>
                    </tr>
                </thead>
                <tbody>
                    <c:set var="totalCheques" value="0" />
                    <c:forEach var="data" items="${cmpDetailsList}" varStatus="row">
                        <c:set var="totalCheques" value="${totalCheques + data.cheques_cnt}" />
                        <tr>
                            <td width="10%" align="center">${row.index + 1}</td>
                            <td width="30%">${data.cmp_no}</td>
                            <td width="15%" align="center">${data.esigned_date}</td>
                            <td width="10%" align="right">${data.cheques_cnt}</td>
                            <td width="35%"></td>
                        </tr>
                    </c:forEach>
                </tbody>
                <tfoot>
                    <tr>
                        <th colspan="3" style="text-align: left;">Total</th>
                        <th style="text-align: right;">${totalCheques}</th>
                        <th></th>
                    </tr>
                </tfoot>
            </table>
            <p><b>I acknowledge the receipt of above mentioned cheques.</b></p>
            <p><b>Name : </b></p>
            <p><b>Phone Number : </b></p>
            <p><b>Date & Time : </b></p>
        </div>
        <div class="empty" style="width: 10%;"></div>
    </div>
    <div class="row">
        <div class="space" style="width: 70%;"></div>
        <div class="space" style="width: 30%;">
            <div id="qrCode"></div>
        </div>
    </div>

    <script>
        // Generate QR code with the token number
        const qrData = "${dataForQR}";
        const qrCodeContainer = document.getElementById('qrCode');
        const qr = qrcode(0, 'L');
        const data = "{\"tokenNumber\" : \"" + qrData.trim() +"\"}";
        qr.addData(data);
        qr.make();
        qrCodeContainer.innerHTML = qr.createImgTag(4); // Adjust the size of the QR code with the scale factor
        // Style the image to set width and height
        const qrImg = qrCodeContainer.querySelector('img');
        qrImg.style.width = "100px"; // Set desired width
        qrImg.style.height = "100px"; // Set desired height
    </script>
</body>
</html>
package com.cgg.common.dao;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.cgg.common.CommonUtils;
import com.cgg.reports.model.CmpReport;

@Repository
public class CommonDaoImpl implements CommonDao 
{
	@Autowired
	private DataSource dataSource;
	
	public List<CmpReport> getHospitalList() throws Exception
	{
		Connection con = null;
		PreparedStatement pst = null;
		ResultSet rs = null;
		List<CmpReport> list = new ArrayList<>();
		try 
		{
			con = dataSource.getConnection();
			
			pst = con.prepareStatement("select hospcode, hospname||'('||recog||')' as hosp_name from hospital  where delete_flag='false' order by hospname");
			rs = pst.executeQuery();
			while(rs.next())
			{
				CmpReport cmpReportobj = new CmpReport();
				cmpReportobj.setHosp_codes(rs.getInt("hospcode"));
				cmpReportobj.setHosp_name(rs.getString("hosp_name"));
				
				list.add(cmpReportobj);
			}
		}
		catch (Exception e) 
		{
			e.printStackTrace();
		}
		finally
		{   if(pst!=null){pst.close();pst=null;}
			CommonUtils.closeCon(con,null,rs);
		}
		return list;
	}

	public List<CmpReport> getRecommendedByList() throws Exception 
	{
		Connection con = null;
		PreparedStatement pst = null;
		ResultSet rs = null;
		List<CmpReport> list = new ArrayList<>();
		try 
		{
			con = dataSource.getConnection();
			
			pst = con.prepareStatement("select cno, cno||','||mlamp||coalesce(design,'')||coalesce('('||case when minister='Y' then 'Minister' else  party end ||')','')|| ', '||cname as mla_mp_name from  constituency order by cno");
			rs = pst.executeQuery();
			while(rs.next())
			{
				CmpReport cmpReportobj = new CmpReport();
				cmpReportobj.setCno(rs.getInt("cno"));
				cmpReportobj.setMla_mp_names(rs.getString("mla_mp_name"));
				
				list.add(cmpReportobj);
			}
		}
		catch (Exception e) 
		{
			e.printStackTrace();
		}
		finally
		{
			if(pst!=null){pst.close();pst=null;}
			CommonUtils.closeCon(con,null,rs);
		}
		return list;
	}

	public List<CmpReport> getDistrictList() throws Exception 
	{
		Connection con = null;
		PreparedStatement pst = null;
		ResultSet rs = null;
		List<CmpReport> list = new ArrayList<>();
		try 
		{
			con = dataSource.getConnection();
			
			pst = con.prepareStatement("select distno, distname from  district order by distname");
			rs = pst.executeQuery();
			while(rs.next())
			{
				CmpReport cmpReportobj = new CmpReport();
				cmpReportobj.setDistno(rs.getInt("distno"));
				cmpReportobj.setDistname(rs.getString("distname"));
				
				list.add(cmpReportobj);
			}
		}
		catch (Exception e) 
		{
			e.printStackTrace();
		}
		finally
		{
			if(pst!=null){pst.close();pst=null;}
			CommonUtils.closeCon(con,null,rs);
		}
		return list;
	}
	public List<CmpReport> getMandals(String cmrf_no) throws Exception 
	{

		Connection con = null;
		PreparedStatement pst = null;
		PreparedStatement pst1 = null;
		ResultSet rs = null;
		ResultSet rs1 = null;
		List<CmpReport> list = new ArrayList<>();
		try 
		{
			con = dataSource.getConnection();
			
			pst = con.prepareStatement("select pat_district from cmrelief where cmrf_no = ?");
			pst.setString(1, cmrf_no);
			rs = pst.executeQuery();
			if(rs.next())
			{
				pst1 = con.prepareStatement("select mcode, mname from mandal where distcode::int=? order by mname");
				pst1.setInt(1, rs.getInt("pat_district"));
				rs1 = pst1.executeQuery();
				while(rs1.next())
				{
					CmpReport cmpReportobj = new CmpReport();
					cmpReportobj.setMcode(rs1.getInt("mcode"));
					cmpReportobj.setMname(rs1.getString("mname"));
					
					list.add(cmpReportobj);
				}
			}
			else
			{
				CmpReport cmpReportobj = new CmpReport();
				cmpReportobj.setMcode(0);
				cmpReportobj.setMname("--Select--");
				list.add(cmpReportobj);
			}
		}
		catch (Exception e) 
		{
			e.printStackTrace();
		}
		finally
		{
			if(pst!=null){pst.close();pst=null;}
			if(pst1!=null){pst1.close();pst1=null;}
			CommonUtils.closeCon(con,null,rs);
		}
		return list;
	}
	public String getDistName(int distcode) throws Exception 
	{
		Connection con = null;
		PreparedStatement pst = null;
		ResultSet rs = null;
		String value = null;
		try 
		{
			con = dataSource.getConnection();
			pst = con.prepareStatement("select distname from district where distno = ?");
			pst.setInt(1, distcode);
			rs = pst.executeQuery();
			if(rs.next())
				value = rs.getString("distname");
			else
				value = "Wrong District Code";
		}
		catch (Exception e) 
		{
			e.printStackTrace();
		}
		finally
		{
			if(pst!=null){pst.close();pst=null;}
			CommonUtils.closeCon(con,null,rs);
		}
		return value;
	}
	
	//ajax mandals
	public String getAjaxMandals(String distCode) throws Exception
	{
		Connection con = null;
		PreparedStatement pst = null;
		ResultSet rs = null;
		String outline = "";
		
		try 
		{
			con = dataSource.getConnection();
			pst = con.prepareStatement("select mcode, mname from mandal where distcode::int=? order by mname");
			pst.setInt(1, Integer.parseInt(distCode));
			rs = pst.executeQuery();
			while(rs.next())
			{
				outline = outline+"<option value="+rs.getString(1)+">"+rs.getString(2)+"</option>";
			}
		}
		catch (Exception e) 
		{
			e.printStackTrace();
		}
		finally
		{
			if(pst!=null){pst.close();pst=null;}
			CommonUtils.closeCon(con,null,rs);
		}
		return outline;
	}
}

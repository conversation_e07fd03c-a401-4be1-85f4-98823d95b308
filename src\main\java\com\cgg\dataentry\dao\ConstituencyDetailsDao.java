package com.cgg.dataentry.dao;

import java.util.List;
import java.util.Map;

import com.cgg.dataentry.model.ConstituencyDetailsForm;

public interface ConstituencyDetailsDao {
	public List<ConstituencyDetailsForm> getDistricts() throws Exception;
	public List<ConstituencyDetailsForm> getConstDtls() throws Exception;
	public int insertConstDetails(ConstituencyDetailsForm constDtlsForm, Map<String, Object> model)throws Exception;
	public int  updateConstDetails(ConstituencyDetailsForm constDtlsForm, Map<String, Object> model)throws Exception;
	public String getConstData(String constituencyVal)throws Exception;
	
}

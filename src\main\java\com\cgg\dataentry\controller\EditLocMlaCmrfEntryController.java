package com.cgg.dataentry.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.cgg.dataentry.model.EditLOCMLACMRFEntryModel;
import com.cgg.dataentry.service.EditLOCMLACMRFEntryService;



@Controller
@RequestMapping(value = "/editLocToken")
public class EditLocMlaCmrfEntryController {
	
	@Autowired
	private EditLOCMLACMRFEntryService editlocService;
	
	@RequestMapping(method = RequestMethod.GET)
    public String editlocView(Map<String, Object> model)throws Exception {
		
		EditLOCMLACMRFEntryModel editlocEntryForm = new EditLOCMLACMRFEntryModel();
		
		List<EditLOCMLACMRFEntryModel> recommendedList = new ArrayList<EditLOCMLACMRFEntryModel>();  
		recommendedList = editlocService.getRecommendedDetails();
		
		List<EditLOCMLACMRFEntryModel> hospitalList = new ArrayList<EditLOCMLACMRFEntryModel>();  
		hospitalList = editlocService.getHospitalDetails();
		
		List<EditLOCMLACMRFEntryModel> districts = new ArrayList<EditLOCMLACMRFEntryModel>();  
		districts = editlocService.getDistricts();
		
		model.put("recommendedList",recommendedList);
		model.put("hospitalList",hospitalList);
		model.put("districts",districts);
        model.put("editlocEntryForm", editlocEntryForm);
         
        return "editLocTokenDetails";
    }
	
	@RequestMapping(value = "/getLocTokenData",method = RequestMethod.POST)
    public @ResponseBody String retrieveLocDetails(Map<String, Object> model,@RequestParam("loc_token")String loc_token,@ModelAttribute("editlocEntryForm") EditLOCMLACMRFEntryModel editlocEntryForm)throws Exception {
		
		List<EditLOCMLACMRFEntryModel> loc_data=new ArrayList<EditLOCMLACMRFEntryModel>();
		StringBuilder mainData = new StringBuilder();
	
		if(loc_token !=null && !loc_token.isEmpty() && !loc_token.equals("0")) {		
			loc_data=editlocService.getLocData(loc_token);
			for(EditLOCMLACMRFEntryModel tempDTO : loc_data) {
                mainData.append(tempDTO.getPatient_name()+"*-*"); 
                mainData.append(tempDTO.getFather_name()+"*-*"); 
                mainData.append(tempDTO.getAadharNo()+"*-*"); 
                mainData.append(tempDTO.getMobileNo()+"*-*"); 
                mainData.append(tempDTO.getPatDistrict()+"*-*"); 
                mainData.append(tempDTO.getAddress()+"*-*"); 
                mainData.append(tempDTO.getPurpose()+"*-*");
                mainData.append(tempDTO.getAssured_amt()+"*-*");
                mainData.append(tempDTO.getRecommendedBy()+"*-*");
                mainData.append(tempDTO.getVipletter_date()+"*-*");
                mainData.append(tempDTO.getHospCode()+"*-*");
                mainData.append(tempDTO.getStatus());
			}
			
		}else {
			model.put("msg", "Invalid Loc Token Number");
		}
		editlocEntryForm.setLoc_token(loc_token);
        model.put("editlocEntryForm", editlocEntryForm);      
        return mainData.toString();
    }
	
	@RequestMapping(method = RequestMethod.POST)
    public String updateLocDetails(Map<String, Object> model,@ModelAttribute("editlocEntryForm") EditLOCMLACMRFEntryModel editlocEntryForm,HttpServletRequest request)throws Exception {
		
		boolean flag=false;
		flag=editlocService.updateLocDetails(editlocEntryForm,request);
		if(flag) {
			model.put("msg", "Loc Token Details Updated.");
		}else {
			model.put("msg", "Updation Failed.");
		}
		model.put("recommendedList",editlocService.getRecommendedDetails());
		model.put("hospitalList",editlocService.getHospitalDetails());
		editlocEntryForm.setPatient_name("");editlocEntryForm.setFather_name("");editlocEntryForm.setAddress("");
		editlocEntryForm.setAadharNo("");
		editlocEntryForm.setMobileNo("");
		editlocEntryForm.setPurpose("");editlocEntryForm.setAssured_amt("");editlocEntryForm.setRecommendedBy("");
		editlocEntryForm.setVipletter_date("");editlocEntryForm.setHospCode("");editlocEntryForm.setStatus("");  
		editlocEntryForm.setLoc_mla_no("");editlocEntryForm.setMla_cmrf_year("0");
        return "editLocTokenDetails";
    }

}

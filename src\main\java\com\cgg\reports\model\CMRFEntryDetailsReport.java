package com.cgg.reports.model;

import java.io.Serializable;

public class CMRFEntryDetailsReport implements Serializable{

	private String cmrfDate;
	private String cmrfToDate;
	private String hospVerCnt;
	private String userId,userName,count,cmrfType;
	private String hospName;
	private String cname;
	private String cno;
	private String qrScannedBy;
	private String qrScannedCount;
	
	private String patientName,Address,hospital,purpose,fatherSonOf,patAddress;
	private Long mobile_no;
	
	private String bundleName;
	private String bundleSerialNo;
	private String mlaCmrfTokenNo;
	private String hospCode;
	private String status;
	private String hosVerCnt;
	private String docVerCnt;
	private String docPendCnt;
	private String docVerBy;
	private String docVerDate;
	
	private String name,mobileNo,fraudDetails,enteredDateTime;
	

	public String getCname() {
		return cname;
	}

	public void setCname(String cname) {
		this.cname = cname;
	}

	public String getHospName() {
		return hospName;
	}

	public void setHospName(String hospName) {
		this.hospName = hospName;
	}

	public String getCmrfType() {
		return cmrfType;
	}

	public void setCmrfType(String cmrfType) {
		this.cmrfType = cmrfType;
	}

	public String getCmrfDate() {
		return cmrfDate;
	}

	public void setCmrfDate(String cmrfDate) {
		this.cmrfDate = cmrfDate;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getCount() {
		return count;
	}

	public void setCount(String count) {
		this.count = count;
	}

	public String getCmrfToDate() {
		return cmrfToDate;
	}

	public void setCmrfToDate(String cmrfToDate) {
		this.cmrfToDate = cmrfToDate;
	}

	public String getHospVerCnt() {
		return hospVerCnt;
	}

	public void setHospVerCnt(String hospVerCnt) {
		this.hospVerCnt = hospVerCnt;
	}

	public String getQrScannedBy() {
		return qrScannedBy;
	}

	public void setQrScannedBy(String qrScannedBy) {
		this.qrScannedBy = qrScannedBy;
	}

	public String getQrScannedCount() {
		return qrScannedCount;
	}

	public void setQrScannedCount(String qrScannedCount) {
		this.qrScannedCount = qrScannedCount;
	}

	public String getCno() {
		return cno;
	}

	public void setCno(String cno) {
		this.cno = cno;
	}

	public String getPatientName() {
		return patientName;
	}

	public void setPatientName(String patientName) {
		this.patientName = patientName;
	}

	public String getAddress() {
		return Address;
	}

	public void setAddress(String address) {
		Address = address;
	}

	public Long getMobile_no() {
		return mobile_no;
	}

	public void setMobile_no(Long mobile_no) {
		this.mobile_no = mobile_no;
	}

	public String getPurpose() {
		return purpose;
	}

	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}

	public String getHospital() {
		return hospital;
	}

	public void setHospital(String hospital) {
		this.hospital = hospital;
	}

	public String getBundleName() {
		return bundleName;
	}

	public void setBundleName(String bundleName) {
		this.bundleName = bundleName;
	}

	public String getFatherSonOf() {
		return fatherSonOf;
	}

	public void setFatherSonOf(String fatherSonOf) {
		this.fatherSonOf = fatherSonOf;
	}

	public String getPatAddress() {
		return patAddress;
	}

	public void setPatAddress(String patAddress) {
		this.patAddress = patAddress;
	}

	public String getBundleSerialNo() {
		return bundleSerialNo;
	}

	public void setBundleSerialNo(String bundleSerialNo) {
		this.bundleSerialNo = bundleSerialNo;
	}

	public String getMlaCmrfTokenNo() {
		return mlaCmrfTokenNo;
	}

	public void setMlaCmrfTokenNo(String mlaCmrfTokenNo) {
		this.mlaCmrfTokenNo = mlaCmrfTokenNo;
	}

	public String getHospCode() {
		return hospCode;
	}

	public void setHospCode(String hospCode) {
		this.hospCode = hospCode;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getHosVerCnt() {
		return hosVerCnt;
	}

	public void setHosVerCnt(String hosVerCnt) {
		this.hosVerCnt = hosVerCnt;
	}

	public String getDocVerCnt() {
		return docVerCnt;
	}

	public void setDocVerCnt(String docVerCnt) {
		this.docVerCnt = docVerCnt;
	}

	public String getDocPendCnt() {
		return docPendCnt;
	}

	public void setDocPendCnt(String docPendCnt) {
		this.docPendCnt = docPendCnt;
	}

	public String getDocVerBy() {
		return docVerBy;
	}

	public void setDocVerBy(String docVerBy) {
		this.docVerBy = docVerBy;
	}

	public String getDocVerDate() {
		return docVerDate;
	}

	public void setDocVerDate(String docVerDate) {
		this.docVerDate = docVerDate;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getMobileNo() {
		return mobileNo;
	}

	public void setMobileNo(String mobileNo) {
		this.mobileNo = mobileNo;
	}

	public String getFraudDetails() {
		return fraudDetails;
	}

	public void setFraudDetails(String fraudDetails) {
		this.fraudDetails = fraudDetails;
	}

	public String getEnteredDateTime() {
		return enteredDateTime;
	}

	public void setEnteredDateTime(String enteredDateTime) {
		this.enteredDateTime = enteredDateTime;
	}
	
	

}

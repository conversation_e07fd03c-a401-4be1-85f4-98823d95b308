package com.cgg.dataentry.model;

import java.math.BigDecimal;

public interface LocMlaCmrfPrintProjection {
    String getMlaLocNo();
    String getPatientName();
    String getFatherSonOf();
    String getPatientIp();
    String getAge();
    String getGender();
    String getDistName();
    String getSpecialName();
    String getDepartmentName();
    String getAddress();
    String getHospName();
    Integer getHospCode();
    BigDecimal getMobileNo();
    String getAadhaarNo();
    String getOpcrNo();
    String getPurpose();
    Integer getAssuredAmt();
    String getAarogyasreeCovered();
    Integer getBedCharges();
    Integer getInvestigCharges();
    Integer getDrugsDispCharges();
    Integer getSurgProcCharges();
    Integer getImplantCharges();
    Integer getMiscCharges();
}

package com.cgg.dataentry.service;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.dataentry.dao.MoveCmrfDataDao;
import com.cgg.dataentry.model.MoveDataModel;

@Service
public class MoveCmrfDataServiceImpl implements MoveCmrfDataService {
	
	@Autowired
	MoveCmrfDataDao moveDao;

	@Override
	public boolean updateData(MoveDataModel moveData,HttpServletRequest request) throws Exception {
		return moveDao.updateData(moveData,request);
	}
	
	

}

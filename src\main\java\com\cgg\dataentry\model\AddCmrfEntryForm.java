package com.cgg.dataentry.model;


public class AddCmrfEntryForm {
	

	private String cmrfDate;
	private String cmrfNo;
	private String oldCmrfNo;
	private String userId;
	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}
	private String year=String.valueOf(new java.util.Date().getYear() + 1900);
	private String isReimbursement,exgratia;
	private String ipAddress;
	private String mobileNo;
	private String cmrfUser;
	private String specialCaseToken;
	private String tokenEnteredDate;
	
	
	
	public String getTokenEnteredDate() {
		return tokenEnteredDate;
	}

	public void setTokenEnteredDate(String tokenEnteredDate) {
		this.tokenEnteredDate = tokenEnteredDate;
	}

	public String getSpecialCaseToken() {
		return specialCaseToken;
	}

	public void setSpecialCaseToken(String specialCaseToken) {
		this.specialCaseToken = specialCaseToken;
	}
	private String otherConst;
	private String otherConHidval;
	private String password;
	private int cmrfType;
	private Boolean isSpecialFlg;
	private String specialFlgRefBy;

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getCmrfNo() {
		return cmrfNo;
	}

	public void setCmrfNo(String cmrfNo) {
		this.cmrfNo = cmrfNo;
	}

	public String getValueck() {
		return valueck;
	}

	public String getIpAddress() {
		return ipAddress;
	}

	public String getMobileNo() {
		return mobileNo;
	}

	public void setMobileNo(String mobileNo) {
		this.mobileNo = mobileNo;
	}

	public String getCmrfUser() {
		return cmrfUser;
	}

	public void setCmrfUser(String cmrfUser) {
		this.cmrfUser = cmrfUser;
	}

	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}

	public void setValueck(String valueck) {
		this.valueck = valueck;
	}
	private String valueck;
	public String getExgratia() {
		return exgratia;
	}

	public void setExgratia(String exgratia) {
		this.exgratia = exgratia;
	}
	public String getCo() {
		return co;
	}

	public void setCo(String co) {
		this.co = co;
	}
	private String co;
	private String Signed;
	private String cmrfLoc,patientIpNo,tokenNo;
	public String getTokenNo() {
		return tokenNo;
	}

	public void setTokenNo(String tokenNo) {
		this.tokenNo = tokenNo;
	}

	protected String mlaCmrfNo,inwardId;
	private String aadharNo,hospCode,age,fatherName,patAddress,hospName,oldFscNo,newFscNo;
	private String constNo;
	private String constName,recommendedBy,purpose,sancDate; 
	private String bankAccNo;
	private String bankAccHolderName;
	
	
	public String getBankAccNo() {
		return bankAccNo;
	}

	public void setBankAccNo(String bankAccNo) {
		this.bankAccNo = bankAccNo;
	}

	public String getOldFscNo() {
		return oldFscNo;
	}

	public void setOldFscNo(String oldFscNo) {
		this.oldFscNo = oldFscNo;
	}

	public String getNewFscNo() {
		return newFscNo;
	}

	public void setNewFscNo(String newFscNo) {
		this.newFscNo = newFscNo;
	}

	public String getSancDate() {
		return sancDate;
	}

	public void setSancDate(String sancDate) {
		this.sancDate = sancDate;
	}

	private String patDistrict,reqAmt,patientName,docVerAmt;
	public String getPurpose() {
		return purpose;
	}

	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}
	public String getPatientName() {
		return patientName;
	}

	public void setPatientName(String patientName) {
		this.patientName = patientName;
	}

	private String PatMandal,recFrom,pancAmt,paymentTo,diseaseType,detDiseases,sancAmt,range,endorsment;
	public String getEndorsment() {
		return endorsment;
	}

	public void setEndorsment(String endorsment) {
		this.endorsment = endorsment;
	}

	public String getRange() {
		return range;
	}

	public void setRange(String range) {
		this.range = range;
	}

	public String getSancAmt() {
		return sancAmt;
	}

	public void setSancAmt(String sancAmt) {
		this.sancAmt = sancAmt;
	}

	private String prevSanc,prevCmpno,prevDate,pndorsment,prevApplnos;
	private String distNo,distName;
	private String mandalNo,mandalName;
	public String getHospName() {
		return hospName;
	}

	public void setHospName(String hospName) {
		this.hospName = hospName;
	}

	public String getDistNo() {
		return distNo;
	}

	public void setDistNo(String distNo) {
		this.distNo = distNo;
	}

	public String getDistName() {
		return distName;
	}

	public void setDistName(String distName) {
		this.distName = distName;
	}

	public String getMandalNo() {
		return mandalNo;
	}

	public void setMandalNo(String mandalNo) {
		this.mandalNo = mandalNo;
	}

	public String getMandalName() {
		return mandalName;
	}

	public void setMandalName(String mandalName) {
		this.mandalName = mandalName;
	}

	public String getRecFrom() {
		return recFrom;
	}

	public void setRecFrom(String recFrom) {
		this.recFrom = recFrom;
	}

	

	public String getPatMandal() {
		return PatMandal;
	}

	public void setPatMandal(String patMandal) {
		PatMandal = patMandal;
	}


	

	
	
	

	public String getPatDistrict() {
		return patDistrict;
	}

	public void setPatDistrict(String patDistrict) {
		this.patDistrict = patDistrict;
	}

	public String getReqAmt() {
		return reqAmt;
	}

	public void setReqAmt(String reqAmt) {
		this.reqAmt = reqAmt;
	}

	public String getPancAmt() {
		return pancAmt;
	}

	public void setPancAmt(String pancAmt) {
		this.pancAmt = pancAmt;
	}

	public String getPaymentTo() {
		return paymentTo;
	}

	public void setPaymentTo(String paymentTo) {
		this.paymentTo = paymentTo;
	}

	public String getDiseaseType() {
		return diseaseType;
	}

	public void setDiseaseType(String diseaseType) {
		this.diseaseType = diseaseType;
	}

	public String getDetDiseases() {
		return detDiseases;
	}

	public void setDetDiseases(String detDiseases) {
		this.detDiseases = detDiseases;
	}

	public String getPrevSanc() {
		return prevSanc;
	}

	public void setPrevSanc(String prevSanc) {
		this.prevSanc = prevSanc;
	}

	public String getPrevCmpno() {
		return prevCmpno;
	}

	public void setPrevCmpno(String prevCmpno) {
		this.prevCmpno = prevCmpno;
	}

	public String getPrevDate() {
		return prevDate;
	}

	public void setPrevDate(String prevDate) {
		this.prevDate = prevDate;
	}

	public String getPndorsment() {
		return pndorsment;
	}

	public void setPndorsment(String pndorsment) {
		this.pndorsment = pndorsment;
	}

	public String getPrevApplnos() {
		return prevApplnos;
	}

	public void setPrevApplnos(String prevApplnos) {
		this.prevApplnos = prevApplnos;
	}

	public String getRecommendedBy() {
		return recommendedBy;
	}

	public void setRecommendedBy(String recommendedBy) {
		this.recommendedBy = recommendedBy;
	}

	public String getConstNo() {
		return constNo;
	}

	public void setConstNo(String constNo) {
		this.constNo = constNo;
	}

	public String getConstName() {
		return constName;
	}

	public void setConstName(String constName) {
		this.constName = constName;
	}

	public String getPatAddress() {
		return patAddress;
	}

	public void setPatAddress(String patAddress) {
		this.patAddress = patAddress;
	}

	public String getFatherName() {
		return fatherName;
	}

	public void setFatherName(String fatherName) {
		this.fatherName = fatherName;
	}

	public String getAge() {
		return age;
	}

	public void setAge(String age) {
		this.age = age;
	}

	public String getAadharNo() {
		return aadharNo;
	}

	public String getHospCode() {
		return hospCode;
	}

	public void setHospCode(String hospCode) {
		this.hospCode = hospCode;
	}

	public void setAadharNo(String aadharNo) {
		this.aadharNo = aadharNo;
	}

	public String getMlaCmrfNo() {
		return mlaCmrfNo;
	}

	public void setMlaCmrfNo(String mlaCmrfNo) {
		this.mlaCmrfNo = mlaCmrfNo;
	}

	public String getInwardId() {
		return inwardId;
	}

	public void setInwardId(String inwardId) {
		this.inwardId = inwardId;
	}

	public String getPatientIpNo() {
		return patientIpNo;
	}

	public void setPatientIpNo(String patientIpNo) {
		this.patientIpNo = patientIpNo;
	}

	public String getCmrfLoc() {
		return cmrfLoc;
	}

	public void setCmrfLoc(String cmrfLoc) {
		this.cmrfLoc = cmrfLoc;
	}

	public String getIsReimbursement() {
		return isReimbursement;
	}

	public String getSigned() {
		return Signed;
	}

	public void setSigned(String signed) {
		Signed = signed;
	}

	public void setIsReimbursement(String isReimbursement) {
		this.isReimbursement = isReimbursement;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getCmrfDate() {
		return cmrfDate;
	}

	public void setCmrfDate(String cmrfDate) {
		this.cmrfDate = cmrfDate;
	}

	public String getOtherConst() {
		return otherConst;
	}

	public void setOtherConst(String otherConst) {
		this.otherConst = otherConst;
	}

	public String getOtherConHidval() {
		return otherConHidval;
	}

	public void setOtherConHidval(String otherConHidval) {
		this.otherConHidval = otherConHidval;
	}

	public String getDocVerAmt() {
		return docVerAmt;
	}

	public void setDocVerAmt(String docVerAmt) {
		this.docVerAmt = docVerAmt;
	}

	public int getCmrfType() {
		return cmrfType;
	}

	public void setCmrfType(int cmrfType) {
		this.cmrfType = cmrfType;
	}

	public String getBankAccHolderName() {
		return bankAccHolderName;
	}

	public void setBankAccHolderName(String bankAccHolderName) {
		this.bankAccHolderName = bankAccHolderName;
	}

	public String getOldCmrfNo() {
		return oldCmrfNo;
	}

	public void setOldCmrfNo(String oldCmrfNo) {
		this.oldCmrfNo = oldCmrfNo;
	}

	public Boolean getIsSpecialFlg() { 
		return isSpecialFlg;
	}

	public void setIsSpecialFlg(Boolean isSpecialFlg) { 
		this.isSpecialFlg = isSpecialFlg;
	}

	public String getSpecialFlgRefBy() {
		return specialFlgRefBy;
	}

	public void setSpecialFlgRefBy(String specialFlgRefBy) {
		this.specialFlgRefBy = specialFlgRefBy;
	}

	
}

package com.cgg.dataentry.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.cgg.dataentry.model.LOCMLACMRFEntryModel;
import com.cgg.dataentry.model.Treatments;
import com.cgg.dataentry.service.LOCMLACMRFEntryService;

@Controller
@RequestMapping(value = "/locMlaCmrfEntry")
public class LocMlaCmrfEntryController {
	
	@Autowired
	private LOCMLACMRFEntryService locService;
	
	@RequestMapping(method = RequestMethod.GET)
    public String locMlaCmrfView(Map<String, Object> model,HttpSession session)throws Exception {
		String userId=null;
		if(session!=null && session.getAttribute("userid")!=null) 
			userId=(String)session.getAttribute("userid");
		
		String roleId = (String) session.getAttribute("rolesStr");
		
		/*if (Integer.parseInt(roleId) != ApplicationConstants.MLA_ROLE) {
			 return  "redirect:/";
			}*/
		 
		 if(userId==null || userId.equals("null") ) {
				System.out.println("enhancementlocDetails"+userId);
			 return  "redirect:/locMlaCmrfView";
		  }
		LOCMLACMRFEntryModel locEntryForm = new LOCMLACMRFEntryModel();
		
		List<LOCMLACMRFEntryModel> recommendedList = new ArrayList<LOCMLACMRFEntryModel>();  
		recommendedList = locService.getRecommendedDetails(session);
		
		List<LOCMLACMRFEntryModel> hospitalList = new ArrayList<LOCMLACMRFEntryModel>();  
		hospitalList = locService.getHospitalDetails(session);
		
		List<LOCMLACMRFEntryModel> districts = new ArrayList<LOCMLACMRFEntryModel>();  
		districts = locService.getDistricts();

		List<Treatments> healthCareServices = new ArrayList<Treatments>();
		healthCareServices = locService.getHealthCareServices();
		
		model.put("recommendedList",recommendedList);
		model.put("hospitalList",hospitalList);
		model.put("districts",districts);
        model.put("healthCareServices", healthCareServices);
        model.put("locEntryForm", locEntryForm);
         
        return "locMLACmrfEntry";
    }
	
	@RequestMapping(method = RequestMethod.POST)
    public String saveLocMlaCmrfEntry(@ModelAttribute("locEntryForm") LOCMLACMRFEntryModel locEntryForm,
            Map<String, Object> model,RedirectAttributes redirectAttributes,HttpSession session,HttpServletRequest request) throws Exception {
		String userId=null;
		if(session!=null && session.getAttribute("userid")!=null) 
			userId=(String)session.getAttribute("userid");
		 if(userId==null || userId.equals("null") ) {
				System.out.println("enhancementlocDetails"+userId);
			 return  "redirect:/locMlaCmrfView";
		  }
		 locEntryForm.setUserId(userId);
		// Boolean flag=false;
		String result = null;
		String msg="";
		
		// flag=locService.saveLocMlaCmrfEntry(locEntryForm,request);
		result=locService.saveLocMlaCmrfEntry(locEntryForm,request);
		if(result != null && !result.isEmpty()) {
			msg="Registration Completed. Token Number is: " + locEntryForm.getLoc_mla_no();
		}else {
			msg="Error While Inserting";
		}
		//model.put("msg", msg);
		redirectAttributes.addFlashAttribute("msg", msg);
		locEntryForm.setPatient_name("");locEntryForm.setFather_name("");locEntryForm.setAddress("");
		locEntryForm.setPurpose("");locEntryForm.setAssured_amt("");locEntryForm.setRecommendedBy("");
		locEntryForm.setVipletter_date("");locEntryForm.setHospCode("");locEntryForm.setStatus("");
        model.put("locEntryForm", locEntryForm);         
      //  return "locMLACmrfEntry";
        return "redirect:/locMlaCmrfEntry"; 
    }
	
	@RequestMapping(value = "/editLocToken",method = RequestMethod.GET)
    public String editlocView(Map<String, Object> model,HttpSession session)throws Exception {
		
		LOCMLACMRFEntryModel locEntryForm = new LOCMLACMRFEntryModel();
		
		List<LOCMLACMRFEntryModel> recommendedList = new ArrayList<LOCMLACMRFEntryModel>();  
		recommendedList = locService.getRecommendedDetails(session);
		
		List<LOCMLACMRFEntryModel> hospitalList = new ArrayList<LOCMLACMRFEntryModel>();  
		hospitalList = locService.getHospitalDetails(session);
		
		model.put("recommendedList",recommendedList);
		model.put("hospitalList",hospitalList);
        model.put("locEntryForm", locEntryForm);
         
        return "editLocTokenDetails";
    }
	
	@RequestMapping(value = "/getLocTokenData",method = RequestMethod.POST)
    public @ResponseBody String retrieveLocDetails(Map<String, Object> model,@RequestParam("loc_token")String loc_token,@ModelAttribute("locEntryForm") LOCMLACMRFEntryModel locEntryForm)throws Exception {
		
		List<LOCMLACMRFEntryModel> loc_data=new ArrayList<LOCMLACMRFEntryModel>();
		StringBuilder mainData = new StringBuilder();
	
		if(loc_token !=null && !loc_token.isEmpty() && !loc_token.equals("0")) {		
			loc_data=locService.getLocData(loc_token);
			for(LOCMLACMRFEntryModel tempDTO : loc_data) {
                mainData.append(tempDTO.getPatient_name()+":"); 
                mainData.append(tempDTO.getFather_name()+":"); 
                mainData.append(tempDTO.getAddress()+":"); 
                mainData.append(tempDTO.getPurpose()+":");
                mainData.append(tempDTO.getAssured_amt()+":");
                mainData.append(tempDTO.getRecommendedBy()+":");
                mainData.append(tempDTO.getVipletter_date()+":");
                mainData.append(tempDTO.getHospCode()+":");
                mainData.append(tempDTO.getStatus());
			}
			
		}else {
			model.put("msg", "Invalid Loc Token Number");
		}
		locEntryForm.setLoc_token(loc_token);
        model.put("locEntryForm", locEntryForm);      
        return mainData.toString();
    }
	
	@RequestMapping(value = "/editLocToken",method = RequestMethod.POST)
    public String updateLocDetails(Map<String, Object> model,@ModelAttribute("locEntryForm") LOCMLACMRFEntryModel locEntryForm,HttpSession session)throws Exception {
		
		boolean flag=false;
		flag=locService.updateLocDetails(locEntryForm);
		if(flag) {
			model.put("msg", "Loc Token Details Updated.");
		}else {
			model.put("msg", "Updation Failed.");
		}
		model.put("recommendedList",locService.getRecommendedDetails(session));
		model.put("hospitalList",locService.getHospitalDetails(session));
		locEntryForm.setPatient_name("");locEntryForm.setFather_name("");locEntryForm.setAddress("");
		locEntryForm.setPurpose("");locEntryForm.setAssured_amt("");locEntryForm.setRecommendedBy("");
		locEntryForm.setVipletter_date("");locEntryForm.setHospCode("");locEntryForm.setStatus("");  
		locEntryForm.setLoc_mla_no("");locEntryForm.setMla_cmrf_year("0");
        return "editLocTokenDetails";
    }

	@PostMapping("/getSubTreat")
    public ResponseEntity<List<Treatments>> getSubTreatments(@RequestParam("hcsId") Integer treatParId) throws Exception {
        List<Treatments> subTreatments = locService.getSubTreatmentsByHcsId(treatParId);
        return ResponseEntity.ok(subTreatments);
    }

	@PostMapping("/getProc")
    public ResponseEntity<List<Treatments>> getProcedures(@RequestParam("parId") Integer treatParId,@RequestParam("subId") Integer treatSubId) throws Exception {
        List<Treatments> subTreatments = locService.getProceduresBySubId(treatParId,treatSubId);
        return ResponseEntity.ok(subTreatments);
    }
}

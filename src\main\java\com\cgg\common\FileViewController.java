 package com.cgg.common;

import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URLConnection;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.HandlerMapping;

@Controller
public class FileViewController {

	private static final Logger log = LoggerFactory.getLogger(FileViewController.class);

	@RequestMapping(value = "/preview/**", method = RequestMethod.GET)
	public @ResponseBody ResponseEntity<Resource> previewPage(
	        HttpServletRequest request, HttpServletResponse response, AntPathMatcher pathMatcher) throws IOException {

	    String arguments = pathMatcher.extractPathWithinPattern(
	            request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE).toString(),
	            request.getRequestURI());
	    arguments = URLDecoder.decode(arguments, StandardCharsets.UTF_8.name());
	    String filePath = arguments.startsWith("preview/") ? arguments.substring(8) : arguments;

	    Resource resource = loadResource(filePath);

	    if (!resource.exists() || !resource.isReadable()) {
	        log.error("File not found: {}", filePath);
	        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
	    }

	    String mimeType = Files.probeContentType(Paths.get(resource.getFilename()));
	    if (mimeType == null || mimeType.isEmpty()) {
	        mimeType = URLConnection.guessContentTypeFromName(resource.getFilename());
	    }
	     System.out.println("mimeType----"+mimeType);
	    // Handle Excel files manually
	    if (resource.getFilename().endsWith(".xls")) {
	        mimeType = "application/vnd.ms-excel";
	    } else if (resource.getFilename().endsWith(".xlsx")) {
	        mimeType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
	    }

	    log.info("File: {}, MIME Type: {}", resource.getFilename(), mimeType);

	    HttpHeaders headers = new HttpHeaders();
	    headers.add("Content-Type", mimeType);
	   // headers.add("Content-Disposition", "attachment; filename=" + resource.getFilename());
	    headers.add("Content-Disposition", "inline;filename= " + resource.getFilename());
	  //   headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
	 //   headers.add("Pragma", "no-cache");
	 //   headers.add("Expires", "0");

	    return new ResponseEntity<>(resource, headers, HttpStatus.OK);
	}


	public Resource loadResource(String fileName) throws MalformedURLException {
		// log.info("LoadResource :>>>>>>>>>>> Storage Path : {}, FileName : {}
		// ",storagePath, fileName);
		// System.out.println("loadresource string argument method in preview controller");
		return loadResource(new File(ApplicationConstants.BASE_PATH, fileName));

	}

	static final byte[] blankPngBytes = new byte[] { (byte) 0x89, (byte) 0x50, (byte) 0x4e, (byte) 0x47, 0xd, 0xa,
			(byte) 0x1a, 0xa, 0x0, 0x0, 0x0, 0xd, (byte) 0x49, (byte) 0x48, (byte) 0x44, (byte) 0x52, 0x0, 0x0, 0x0,
			0x1, 0x0, 0x0, 0x0, 0x1, 0x8, 0x4, 0x0, 0x0, 0x0, (byte) 0xb5, (byte) 0x1c, 0xc, 0x2, 0x0, 0x0, 0x0, 0xb,
			(byte) 0x49, (byte) 0x44, (byte) 0x41, (byte) 0x54, (byte) 0x78, (byte) 0xda, (byte) 0x63, (byte) 0x64,
			(byte) 0x60, 0x0, 0x0, 0x0, 0x6, 0x0, 0x2, (byte) 0x30, (byte) 0x81, (byte) 0xd0, (byte) 0x2f, 0x0, 0x0,
			0x0, 0x0, (byte) 0x49, (byte) 0x45, (byte) 0x4e, (byte) 0x44, (byte) 0xae, (byte) 0x42, (byte) 0x60,
			(byte) 0x82 };

	static final ByteArrayResource blankPng = new ByteArrayResource(blankPngBytes, "empty.png");

	public Resource loadResource(File file) throws MalformedURLException {
		// log.info("LoadResource :>>>>>>>>>>> Storage Path : {}, File Name : {} ",
		// storagePath, file.getName());
		// System.out.printf("storagePath: %s, fileName: %s\n", Home_Path,
		// file.getName());
		// log.info("file : {} >>>>>>> File URI:{}", file.getAbsolutePath(),
		// file.toURI() );
		// System.out.println(file.toURI());
		Resource resource = new UrlResource(file.toURI());

		if (!resource.exists() || !resource.isReadable()) {
			log.info("Resource is not existing ");
			return blankPng;
		}
		log.info("Resource existing");
		return resource;
	}
}

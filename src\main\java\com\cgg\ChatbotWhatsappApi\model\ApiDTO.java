package com.cgg.ChatbotWhatsappApi.model;

import java.util.ArrayList;

public class ApiDTO {
    public ArrayList<String> locNos;
    public ArrayList<String> tokenNos;
	public String tokenNumber;
	public String phoneNo;
	public String message;
    
    public ArrayList<String> getLocNos() {
        return locNos;
    }
    public void setLocNos(ArrayList<String> locNos) {
        this.locNos = locNos;
    }
    public ArrayList<String> getTokenNos() {
        return tokenNos;
    }
    public void setTokenNos(ArrayList<String> tokenNos) {
        this.tokenNos = tokenNos;
    }
    public String getPhoneNo() {
        return phoneNo;
    }
    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }
    public String getMessage() {
        return message;
    }
    public void setMessage(String message) {
        this.message = message;
    }
    public String getTokenNumber() {
        return tokenNumber;
    }
    public void setTokenNumber(String tokenNumber) {
        this.tokenNumber = tokenNumber;
    }
    
    @Override
    public String toString() {
        return "ApiDTO [locNos=" + locNos + ", tokenNos=" + tokenNos + ", tokenNumber=" + tokenNumber + ", phoneNo="
                + phoneNo + ", message=" + message + "]";
    }
}

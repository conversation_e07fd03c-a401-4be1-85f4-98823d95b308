package com.cgg.dataentry.dao;

import java.net.HttpURLConnection;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.cgg.common.BSNLSMSHttpPostClient;
import com.cgg.common.SMSHttpMVaayooClient;
import com.cgg.dataentry.model.AddCMPNo;

@Repository
public class AddCMPNoDaoImpl implements AddCMPNoDao
{
	@Autowired
	DataSource dataSource;
	
	 @Autowired
	 JdbcTemplate jdbcTemlate;

	public List<AddCMPNo> getCmpNo(AddCMPNo addCMPNo) throws Exception 
	{
		Connection con = null;
		PreparedStatement pst = null;
		ResultSet rs = null;
		List<AddCMPNo> list = new ArrayList<AddCMPNo>();
		try 
		{
			con = dataSource.getConnection();

			pst = con.prepareStatement("select distinct cmp_no,cmp_no,cmp_date from cmrelief where  cmp_date is null and cmp_no is not null and cmp_no!='' "
				//	+ "order by cmp_date desc");
					+ " order by cmp_no desc");
			rs = pst.executeQuery();
			while(rs.next())
			{
				AddCMPNo addCMPNoObj = new AddCMPNo();
				addCMPNoObj.setCmp_no(rs.getString(1));

				list.add(addCMPNoObj);
			}
		}
		catch (Exception e) 
		{
			e.printStackTrace();
		}
		finally
		{
			if(rs!=null) {rs.close();rs=null;}
			if(pst!=null) {pst.close();pst=null;}
			if(con!=null && !con.isClosed()) {con.close();con=null;}
		}
		return list;
	}
	@SuppressWarnings("resource")
	public int updSanctionDate(AddCMPNo addCMPNo, HttpServletRequest request) throws Exception 
	{
		int flag = 0;
		int flag1 = 0;
		int returnvalue = 0;
		Connection con = null;
		PreparedStatement pst = null;
		ResultSet rs = null;
		SMSHttpMVaayooClient sms=null;
		String cmpDate=null;
		String ipAddress = request.getRemoteAddr();
		HttpSession session=request.getSession();
		String userId=(String)session.getAttribute("userid");
		
		try 
		{
			con = dataSource.getConnection();
			
			pst = con.prepareStatement("update cmrelief set sanc_date=to_date(?,'dd-mm-yyyy'),cmp_date=to_date(?,'dd-mm-yyyy'), ip_address = ?,updated_by=?,updated_on=? where cmp_no= ?");
			if(addCMPNo.getCMPDate()!=null && addCMPNo.getCMPDate()!="") {
            	
            	String []dateFromArr=addCMPNo.getCMPDate().split("-");
            	
            	cmpDate=dateFromArr[2]+"-"+dateFromArr[1]+"-"+dateFromArr[0];
            	System.out.println("dFromstr"+cmpDate);
            	


            	}
			System.out.println("date---"+addCMPNo.getCMPDate());
			System.out.println("date---"+addCMPNo.getCMPDate());
			pst.setString(1, cmpDate);
			pst.setString(2, cmpDate);
			pst.setString(3, ipAddress);
			pst.setString(4, userId);
			 LocalDateTime now = LocalDateTime.now();
             Timestamp timestamp = Timestamp.valueOf(now);
             pst.setTimestamp(5, timestamp); 
			pst.setString(6, addCMPNo.getCMPNo());
			flag = pst.executeUpdate();
			System.out.println("flag---"+flag);
			// if(flag >= 1) {
			// 	returnvalue = 1;
			// }
			if(flag >= 1)
			{
				returnvalue = 1;
				String pat_name="";
				String sanc_amt= "";
				//String cmp_no1 = "";
				String cmp_date1 = "";
				String cmrf_no1 = "";
				String cell_no = "";
				String mname= "";
				// HttpURLConnection connection = null;
				String connection = null;
				String templateId=null, templateName=null; 
				pst = con.prepareStatement("insert into sms_queue(smsnumber,message,is_sent,priority,no_times_tried,application) values('9390359959','CMRF-Status: "+flag+" No. of Cases Sanctioned Under CMRF on '||'"+addCMPNo.getCMPDate()+" -CMO','f',1,0,'cmsite')");
				flag1 = pst.executeUpdate();
				String message=null;
				if(flag1==1)
				{
					//9848363765
					// 9393924568
					//templateId="1007733149073670614";
					templateId="1007392274222352286";					
					templateName="TSCMRF5";					
					//cell_no="9848363765";
					//sms.sendSingleSMS("9848363765","CMRF-Status: "+flag+" No. of Cases Sanctioned Under CMRF on "+addCMPNo.getCMPDate()+" -CMO");
				//	message="No. of Cases Sanctioned Under CMRF on "+addCMPNo.getCMPDate()+" -CMO";
					message="CMRF-Status: "+flag+" No. of Cases Sanctioned Under CMRF on  "+addCMPNo.getCMPDate()+" -CMO";
						
					// connection = SMSHttpMVaayooClient.sendSingleSMS("9393924568",message,templateId);
					connection = BSNLSMSHttpPostClient.sendBSNLSms(message,"9390359959",templateId,templateName);
					String sql = "select pat_name,sanc_amt,cmp_no,to_char(cmp_date,'dd-mm-yyyy'),cmrf_no,cell_no,coalesce(mname,'') from cmrelief t1 inner join constituency t2 on (recommended_by=cno) left join mandal on(int4(t1.pat_district)=int4(mandal.distcode) and t1.pat_mandal=int4(mandal.mcode)) where cmp_date is not null and sms_status is false and char_length(trim(cell_no))>=10 and  sanc_date=to_date(?,'dd-mm-yyyy') and cmp_date=to_date(?,'dd-mm-yyyy') and cmp_no=?";
					System.out.println("addCMPNo.getCMPDate()"+addCMPNo.getCMPDate());
					System.out.println("addCMPNo.getCMPDate()"+addCMPNo.getCMPNo());
					pst = con.prepareStatement(sql);
					pst.setString(1, cmpDate);
					pst.setString(2, cmpDate);
					pst.setString(3, addCMPNo.getCMPNo());
					rs = pst.executeQuery();
					while(rs.next())
					{
						pat_name= rs.getString(1);
						sanc_amt= rs.getString(2);
						//cmp_no1 = rs.getString(3);
						cmp_date1 = rs.getString(4);
						cmrf_no1 = rs.getString(5);
						cell_no = rs.getString(6);
						mname= rs.getString(7);
						templateId="1007791624605313667";
						templateName="TSCMRF6";
						String msg="Sir/Madam, CMRF case referred by you for "+pat_name+mname+" has been sanctioned Rs "+sanc_amt+"/- vide CMRF No:"+cmrf_no1+" dt. "+cmp_date1+"--CMO";
						if(msg.length()>150)
							msg="Sir/Madam, CMRF case referred by you for "+pat_name+" has been sanctioned Rs "+sanc_amt+"/- vide CMRF No:"+cmrf_no1+" dt."+cmp_date1+"--CMO";
						if(msg.length()>150)
							msg="Sir/Madam, CMRF case referred by you for  "+pat_name+" has been sanctioned Rs "+sanc_amt+"/-, CMRF No:"+cmrf_no1+" dt."+cmp_date1+"--CMO";

						pst = con.prepareStatement("update cmrelief set sms_status=true where cmrf_no='"+cmrf_no1+"'");
						flag1 = flag1+pst.executeUpdate();
						pst = con.prepareStatement("insert into sms_queue(smsnumber,message,is_sent,priority,no_times_tried,application) values('"+cell_no+"','"+msg+"','f',1,0,'CMRF_VIPSMS')");
						flag1 = flag1+pst.executeUpdate();
						System.out.println("cell_no"+cell_no);
						// connection = SMSHttpMVaayooClient.sendSingleSMS(cell_no,msg,templateId);
						// connection = BSNLSMSHttpPostClient.sendBSNLSms(msg,cell_no,templateId,templateName);

						//sms.sendSingleSMS(cell_no,msg);
					}
				}
			}
			else
				returnvalue = 0;
		}
		catch (Exception e) 
		{
			e.printStackTrace();
		}
		finally
		{
			if(pst!=null) {pst.close();pst=null;}
			if(con!=null && !con.isClosed()) {con.close();con=null;}
		}
		return returnvalue;
	}
}
<%@ page language="java" contentType="text/html; charset=UTF-8"
pageEncoding="UTF-8"%> <% String path = request.getContextPath(); 
//String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
String basePath = path+"/";
%>
<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Navigation Bar</title>
    <!-- <link rel="stylesheet" href="css/bootstrap5.3.3/css/bootstrap.min.css"> -->
    <!-- Include jQuery if not already included -->
   <script src="js/jquery/jquery-3.6.0.min.js"></script>
    <!-- Add your styles here -->
    <style>
      .navbar-nav .nav-item .nav-link {
        transition: color 0.3s ease;
        /* color: #fff8f8; */
        color: #ffffff;
        border-right: 1px solid #ffffff;
      }
      .navbar-nav .nav-item:hover .nav-link {
        background-color: #e88601;
        /* background-color: #34546b; */
        color: #ffffff;
      }

      .navbar-nav .nav-item.active .nav-link {
        background-color: #e88601;
        /* background-color: #34546b; */
        color: #ffffff;
      }

      .navbar-nav .nav-item.active .nav-link:hover {
        color: #ffffff;
      }
      .btnSubmit {
          width: 100%;
          border-radius: 5px;
          padding: 1.5%;
          border: none;
      }
        .btnSubmit {
        font-weight: 600;
        color: #fff !important;
        background: linear-gradient(to right, #7fb79e, #766998);
      }

      .btnSubmit:hover {
        font-weight: 600;
        color: #fff !important;
        background: linear-gradient(to right, #29875c, #1a0553);
      }
        .btnSubmit {
        font-weight: 600;
        color: #1c0652;
        background-color: #fff;
      }
      .refresh_button {
        background: #188844;
        height: 30px;
        width: 30px;
        border: none;
        border-radius: 4px;
        color: #fff;
        cursor: pointer;
      }
      .refresh_button:active {
        transform: translateY(-5%) scale(0.98);
      }
    </style>
    <script type="text/javascript">
    var basePath = "<%= basePath %>";
    function saveCaptcha(user_id, regHidCapcha) {
    	//alert(basePath)
	      // alert("--111---");
	       $.ajax({
	           url: basePath+"saveCaptcha",
	           type: "POST",
	           contentType: "application/json",
	           dataType: "text",
	           data: JSON.stringify({
	               userId: user_id,
	               captcha: regHidCapcha.value
	           }),
	           success: function(data) {
	               //alert(data);
	           },
	           error: function(error) {
	               alert("Something happened. Please try again after some time..");
	           }
	       });
	   }
	   
	   function encryptValue(value) {
	       return btoa(value);
	   }
      function validateLoginForm() {
        var txtCaptcha = $("#txtCapcha").val();
        var hidCaptcha = $("#hidCapcha").val();

        if ($("#username").val() == "") {
          alert("Enter User Name");
          return false;
        }

        if ($("#password").val() == "") {
          alert("Enter Password");
          return false;
        } else {
          if (
            txtCaptcha == null ||
            txtCaptcha == undefined ||
            txtCaptcha == ""
          ) {
            alert("Enter captcha");
            return false;
          }

          if (
            txtCaptcha != null &&
            txtCaptcha != undefined &&
            txtCaptcha != ""
          ) {
            if (txtCaptcha != hidCaptcha) {
              alert("Wrong captcha");
              return false;
            }
          }
          var pwd = hex_md5($("#password").val());
          $("#password").val(pwd);
          $("#enteredCaptcha").val(encryptValue(txtCaptcha));
          //alert($("#enteredCaptcha").val())
          return true;
        }

        //alert(1);
      }
      $(document).ready(function () {
    	  $('#otp').on('input', function () {
    		    this.value = this.value.replace(/\D/g, '');
    		  });
    	  $('#loginModal').on('hidden.bs.modal', function () {
    	    $('#loginForm')[0].reset();
    	    $('#error-message').text('');
    	    $('#success-message').text('');
    	    $('#otpForm')[0].reset();
    	    $('#otpModal').modal('hide');
    	    $("#refresh").click();
    	  });

    	  $('#otpModal').on('hidden.bs.modal', function () {
    	    $('#otpForm')[0].reset();
    	    $('#otp-error-message').text('');
    	    $('#loginModal').modal('hide');
    	    $("#refresh").click();
    	  });
    	});

    </script>
  </head>
  <body>
    <nav
      style="background: #701414"
      class="navbar navbar-expand-lg navbar-light p-0"
    >
      <!-- style="background-color: #e3f2fd" -->
      <button
        class="navbar-toggler"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#navbarSupportedContent"
        aria-controls="navbarSupportedContent"
        aria-expanded="false"
        aria-label="Toggle navigation"
      >
        <span class="navbar-toggler-icon"></span>
      </button>

      <div class="collapse navbar-collapse" id="navbarSupportedContent">
        <ul style="font-size: large" class="navbar-nav">
          <li class="nav-item ms-3">
            <a class="nav-link px-3" href="<%=basePath%>"><b>HOME</b></a>
          </li>
          <li class="nav-item">
            <a class="nav-link px-3" href="<%=basePath%>about"
              ><b>ABOUT US</b></a
            >
          </li>
          <li class="nav-item">
            <a class="nav-link px-3" href="<%=basePath%>contact"
              ><b>CONTACT US</b></a
            >
          </li>
          <li class="nav-item">
            <a class="nav-link px-3" href="<%=basePath%>gallery"><b>GALLERY</b></a>
          </li>
          <li class="nav-item">
            <a class="nav-link px-3" href="https://relieffund.telangana.gov.in/" target="_blank"
              ><b>HOSPITAL LOGIN</b></a
            >
          </li>
          <li class="nav-item">
            <a class="nav-link px-3" href="#" data-bs-toggle="modal" data-bs-target="#loginModal">
              <b>OFFICIAL LOGIN</b>
            </a>
          </li> 
          <li class="nav-item">
            <a class="nav-link px-3 ms-auto" href="<%=basePath%>reportFraud"><b>REPORT FRAUD</b>&nbsp;<img alt="No Image" src="images/new4.gif" style="height: 25px;"/></a>
          </li> 
        </ul>
      </div>
    </nav>

    <!-- Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header text-center" style="text-align: center !important;">
            <h4 class="modal-title" id="loginModalLabel">Official Login</h4>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <!-- Include the login form here -->
            <b><div id="error-message" class="text-danger text-center"></div></b>
            <b><div id="success-message" class="text-success text-center"></div></b>
            <div class="row p-3">
                <form:form action="login" method="post" id="loginForm" modelAttribute="login">
                <input type="hidden" id="enteredCaptcha" name="enteredCaptcha" />
                  <div class="  mb-2    ">
      <label for="username" class="form-label">User Name</label>
                    <form:input
                      path="username"
                      size="20"
                      maxlength="50"
                      id="username"
                      class="form-control  "
                      tabindex="1"
                    />
                    
                  </div>
                  <div class="form-group ">
                    <!-- <i class="fa fa-key align-self-center ms-5"></i>&nbsp;&nbsp; -->
                      <label for="password" class="form-label">Password</label>
                    
                    <form:password
                      path="password"
                      size="20"
                      maxlength="50"
                      id="password"
                      class="form-control me-5"
                      tabindex="2"
                    /><!-- <html:hidden property="password" /> -->
                  </div>
                  <!-- new code added for capcha 05-06-2017::start -->
                  <div class="row">
                    <div class="col-md-6 mt-3">
                      <table   style="width: 100% !important">
                        <tr style="border: 1px solid #dee2e6">
                          <td align="center">
                            <div id="divGenerateRandomValues"></div>
                          </td>
                          <td align="center">
                            &nbsp;&nbsp;
                          <!--  <img
                              src="images/refresh.png"
                              style="width: 20px; height: 10x; cursor: pointer"
                              id="refresh"
                              title="refresh"
                            />&nbsp; &nbsp; -->
                            
        <button type="button" class="refresh_button"  id="refresh" >
                <i class="fa fa-refresh"></i>
              </button>                    </td>
                        </tr>
                        <tr  >
                          <!-- <td align="center"><b>Enter Captcha:</b></td> -->
                          <td align="left" colspan="2" >
                          
                          </td>
                        </tr>
                        <!-- <tr>
                          <td><br /></td>
                        </tr> -->
                      </table>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group " style="padding-top: 15px !important">
                      <!-- <i class="fa fa-key align-self-center ms-5"></i>&nbsp;&nbsp; -->
                          
                            <input
                              type="text"
                              id="txtCapcha"
                              tabindex="3"
                              class="form-control " 
                              placeholder="Enter Captcha"
                              autocomplete="off"
                              oninput="this.value = this.value.replace(/\D/g, '')"
                              onchange="saveCaptcha((document.getElementById('username').value),(document.getElementById('hidCapcha')))"
                            />
                            
                            </div><input type="hidden"   Id="hidCapcha" />
                    </div>
                  </div>

                  <!-- new code added for capcha 05-06-2017::end-->

                  <div class="col-sm-4 form-group mt-3 m-auto">
                    <input
                      type="submit"
                      class="  btnSubmit"
                      tabindex="4"
                      onclick="return validateLoginForm()"
                      value="Sign In"
                    />
                  </div>
                </form:form>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="modal fade" id="otpModal" tabindex="-1" role="dialog">
      <div class="modal-dialog" role="document">
          <div class="modal-content">
              <div class="modal-header">
                  <h5 class="modal-title">OTP Validation</h5>
                  <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body">
                  <form id="otpForm" onsubmit="handleOtpSubmit(event)">
                      <div class="form-group">
                          <label for="otp">Enter OTP</label>
                          <input type="text" id="otp" name="otp" class="form-control" maxlength="6"  required />
                      </div>
                      <div class="form-group mt-3">
                          <input type="submit" class="btn btn-primary" value="Validate OTP" />
                      </div>
                  </form>
                  <b><div id="otp-error-message" class="text-danger text-center"></div></b>
              </div>
          </div>
      </div>
    </div>
    <!-- <script src="js/bootstrap5.3.3/js/bootstrap.bundle.min.js"></script>
    <script src="js/jquery/jquery-3.5.1.min.js"></script> -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        var navItems = document.querySelectorAll(".nav-item");
        var currentPage = window.location.pathname;

        navItems.forEach(function (navItem) {
          var navLink = navItem.querySelector(".nav-link");
          var linkHref = navLink.getAttribute("href");

          if (currentPage === linkHref) {
            navItem.classList.add("active");
          }

          navItem.addEventListener("click", function (event) {
            // Remove 'active' class from all nav-items
            navItems.forEach(function (item) {
              item.classList.remove("active");
            });

            // Add 'active' class to the clicked nav-item
            this.classList.add("active");
          });
        });
      });
    </script>
    <script type="text/javascript">
    $(document).ready(function () {
        // Login form submission
        $("#loginForm").submit(function (event) {
            event.preventDefault(); // Prevent the default form submission

            $.ajax({
                url: basePath + "login", // Ensure the URL is correct
                type: "POST",
                data: $(this).serialize(), // Serialize form data
                success: function (response) {
                    $("#error-message").text("");
                    $("#success-message").text("");

                    if (response.success) {
                        if (response.otpRequired) {
                            // If OTP is required, display the OTP modal
                            $("#success-message").text(response.msg);
                            $("#loginModal").modal("hide"); // Hide the login modal
                            $("#otpModal").modal("show"); // Show the OTP modal
                            $("#otpForm")[0].reset(); // Reset OTP form for a clean input
                        } else if (response.disclaimer) {
                            $("#success-message").text(response.msg);
                            setTimeout(function () {
                                window.location.href = basePath + "welcome?disclaimer=true"; // Redirect to welcome page
                            }, 500);
                        } else {
                            // If no OTP is required, redirect to the welcome page
                            $("#success-message").text(response.msg);
                            setTimeout(function () {
                                window.location.href = basePath + "welcome"; // Redirect to welcome page
                            }, 500);
                        }
                    } else {
                        // If login fails, show the error message in the modal
                        $("#error-message").text(response.msg);
                        $("#loginForm")[0].reset(); // Reset the form
                        $("#refresh").click(); // Refresh captcha if needed
                        $("#loginModal").modal("show"); // Show the login modal
                    }
                },
                error: function () {
                    $("#error-message").text("An error occurred. Please try again.");
                    $("#loginForm")[0].reset(); // Reset the form
                    $("#refresh").click(); // Refresh captcha if needed
                },
            });
        });

        // OTP form submission
        $("#otpForm").submit(function (event) {
            event.preventDefault(); // Prevent the default form submission

            const otpInput = document.getElementById("otp");
            const encodedOtp = btoa(otpInput.value); // Encode the OTP
            const formData = new FormData();
            formData.append("otp", encodedOtp);

            $.ajax({
                url: basePath + "verifyOtp", // Endpoint for OTP validation
                type: "POST",
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    $("#otp-error-message").text(""); // Clear any previous OTP error messages

                    if (response.success) {
                        // OTP validation successful, redirect to the welcome page
                        $("#success-message").text(response.msg);
                        setTimeout(function () {
                            window.location.href = basePath + "welcome?disclaimer=true"; // Redirect to welcome page
                        }, 500);
                    } else {
                        // OTP validation failed
                        $("#otp-error-message").text(response.msg); // Show OTP error message
                        $("#otpForm")[0].reset(); // Clear OTP input
                        $("#otpModal").modal("show");
                        $("#refresh").click();
                    }
                },
                error: function () {
                    $("#otp-error-message").text("An error occurred during OTP validation. Please try again.");
                    $("#otpForm")[0].reset();
                    $("#loginForm")[0].reset();
                    $("#otpModal").modal("hide");
                    $("#loginModal").modal("hide");
                    $("#refresh").click(); // Refresh captcha if needed
                },
            });
        });
    });
</script>

    <script src="js/capcha.js"></script>
    <script src="js/md5.js"></script>
  </body>
</html>

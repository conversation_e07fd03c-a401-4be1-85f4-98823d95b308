package com.cgg.dataentry.service;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.dataentry.dao.UpdateLocMlaCmrfEntryDao;
import com.cgg.dataentry.model.MlaCmrfEntryForm;

@Service
public class UpdateLocMlaCmrfServiceImpl implements UpdateLocMlaCmrfService{
	@Autowired
	private UpdateLocMlaCmrfEntryDao updateLocMlaCmrfEntryDao;

	@Override
	public String updateLocMlaCmrfDetails(MlaCmrfEntryForm updatemlaCmrfForm, Map<String, Object> model,HttpServletRequest request)
			throws Exception {
		// TODO Auto-generated method stub
		return updateLocMlaCmrfEntryDao.updateLocMlaCmrfDetails(updatemlaCmrfForm, model,request);
	}
}

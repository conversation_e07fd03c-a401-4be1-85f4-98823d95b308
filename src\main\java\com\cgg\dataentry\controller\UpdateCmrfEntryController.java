package com.cgg.dataentry.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.cgg.common.ApplicationConstants;
import com.cgg.dataentry.model.UpdateCmrfEntryForm;
import com.cgg.dataentry.service.UpdateCmrfEntryService;

@Controller
@RequestMapping(value = "/updateCmrfEntry")

public class UpdateCmrfEntryController {
	@Autowired
	private UpdateCmrfEntryService updateCmrfEntryService;
	
	@RequestMapping(method = RequestMethod.GET)
    public String updateCmrfEntryForm(Map<String, Object> model,HttpServletRequest request)throws Exception {
		List<String> validRoleIds = Arrays.asList(String.valueOf(ApplicationConstants.CMRF_OFFICER), ApplicationConstants.CMRF_OSD_ROLE);
		List<UpdateCmrfEntryForm> recommendedList = new ArrayList<UpdateCmrfEntryForm>();
		List<UpdateCmrfEntryForm> hospitalList = new ArrayList<UpdateCmrfEntryForm>();
		List<UpdateCmrfEntryForm> districts = new ArrayList<UpdateCmrfEntryForm>();
		UpdateCmrfEntryForm updateCmrfEntryForm = new UpdateCmrfEntryForm(); 
		HttpSession session=request.getSession();
		String userId=(String)session.getAttribute("userid");
		String roleId=(String)session.getAttribute("rolesStr");
		if(roleId==null || !validRoleIds.contains(roleId) || userId==null) {
			 return  "redirect:/";
		}
		updateCmrfEntryForm.setUserId(userId);
		String cmrfVal=null;
		if(userId.equals("cmrf_aps")) {
			//addCmrfEntryForm.setCmrfUser("true");	
			model.put("cmrfUser", true);
		}else {
			model.put("cmrfUser", false);
		}
		recommendedList = updateCmrfEntryService.getRecommendedDetails();
		hospitalList = updateCmrfEntryService.getHospitalList();
		districts=updateCmrfEntryService.getDistricts();
		model.put("recommendedList",recommendedList);
		model.put("hospitalList",hospitalList);
		model.put("districts",districts);
        model.put("updateCmrfEntryForm", updateCmrfEntryForm);
        model.put("userId", userId);
        updateCmrfEntryForm.setPurpose("UNDERGONE TREATMENT FOR DURING  201");
        return "updateCmrfEntry";
    }
	
	@RequestMapping(method = RequestMethod.POST)
    public String updateCmrfDetails(@ModelAttribute("updateCmrfEntryForm") UpdateCmrfEntryForm updateCmrfEntryForm,
            Map<String, Object> model,HttpServletRequest request) throws Exception {
		List<UpdateCmrfEntryForm> mandals = new ArrayList<UpdateCmrfEntryForm>();
		String msg=null;
		updateCmrfEntryForm.setIpAddress(request.getRemoteAddr());
		HttpSession session=request.getSession();
		  String userId=(String)session.getAttribute("userid");
		  updateCmrfEntryForm.setUserId(userId);
		if(userId.equals("cmrf_aps")) {
			//addCmrfEntryForm.setCmrfUser("true");	
			model.put("cmrfUser", true);
		}else {
			model.put("cmrfUser", false);
		}
		updateCmrfEntryForm.setCmrfno(updateCmrfEntryForm.getCmrfno()+updateCmrfEntryForm.getCmrfYr());

		//  String msg=updateCmrfEntryService.saveCmrfDetails(updateCmrfEntryForm, model);
		int cnt=updateCmrfEntryService.updateCmrfDetails(updateCmrfEntryForm, model,request);
		if(cnt==1) {
			msg="Updated successfully";
			mandals=updateCmrfEntryService.getMandals(updateCmrfEntryForm.getPatDistrict());
        	 model.put("mandals",mandals);
        	// System.out.println("999999"+updateCmrfEntryForm.getOtherConst());
        	 updateCmrfEntryForm.setOtherConst(updateCmrfEntryForm.getOtherConst());
        	 model.put("otherConHidval",updateCmrfEntryForm.getOtherConst());
		}
		else {
			msg="Updation Failed";
			 model.put("otherConHidval","0");
		}
		  List<UpdateCmrfEntryForm> recommendedList = new ArrayList<UpdateCmrfEntryForm>();
		  List<UpdateCmrfEntryForm> hospitalList = new ArrayList<UpdateCmrfEntryForm>();
		  List<UpdateCmrfEntryForm> districts = new ArrayList<UpdateCmrfEntryForm>();
		  updateCmrfEntryForm.setCmrfno("");
		  recommendedList = updateCmrfEntryService.getRecommendedDetails(); 
		  hospitalList = updateCmrfEntryService.getHospitalList();
		  districts=updateCmrfEntryService.getDistricts();
		  List<UpdateCmrfEntryForm> OtherConsts = new ArrayList<UpdateCmrfEntryForm>();
		  if(updateCmrfEntryForm.getRecommendedBy()!=null && updateCmrfEntryForm.getRecommendedBy().equals("998")) {
		    			     OtherConsts=updateCmrfEntryService.getOtherConsList();
		    		  }
		   model.put("otherConstList",OtherConsts);
		  model.put("recommendedList",recommendedList);
		  model.put("hospitalList",hospitalList); 
		  model.put("districts",districts);
		  model.put("userId",userId);
		 
	     model.put("msg", msg);
        model.put("updateCmrfEntryForm", updateCmrfEntryForm);
      //  System.out.println("99vv9999"+updateCmrfEntryForm.getOtherConst());
        return "updateCmrfEntry";
    }
	@RequestMapping(method = RequestMethod.POST,value = "/updateCmrfEntry")
    public String updateCmrfQueryDetails(@ModelAttribute("updateCmrfEntryForm") UpdateCmrfEntryForm updateCmrfEntryForm,
            Map<String, Object> model,HttpServletRequest request) throws Exception {
		List<UpdateCmrfEntryForm> mandals = new ArrayList<UpdateCmrfEntryForm>();
		String msg=null;
		System.out.println("updateCmrfQueryDetails");
		updateCmrfEntryForm.setIpAddress(request.getRemoteAddr());
		HttpSession session=request.getSession();
		  String userId=(String)session.getAttribute("userid");
		  updateCmrfEntryForm.setUserId(userId);
		if(userId.equals("cmrf_aps")) {
			//addCmrfEntryForm.setCmrfUser("true");	
			model.put("cmrfUser", true);
		}else {
			model.put("cmrfUser", false);
		}
		System.out.println("userId"+userId);
		updateCmrfEntryForm.setCmrfno(updateCmrfEntryForm.getCmrfUpdNo());
		//  String msg=updateCmrfEntryService.saveCmrfDetails(updateCmrfEntryForm, model);
		 List<UpdateCmrfEntryForm> OtherConsts = new ArrayList<UpdateCmrfEntryForm>();
		int cnt=updateCmrfEntryService.updateCmrfDetails(updateCmrfEntryForm, model,request);
		System.out.println("cnt"+cnt);
		if(cnt==1) {
			msg="Updated successfully";
			mandals=updateCmrfEntryService.getMandals(updateCmrfEntryForm.getPatDistrict());
        	 model.put("mandals",mandals);
		}
		else {
			msg="Updation Failed";
		}
		  updateCmrfEntryForm.setCmrfno("");
		  List<UpdateCmrfEntryForm> recommendedList = new ArrayList<UpdateCmrfEntryForm>();
		  List<UpdateCmrfEntryForm> hospitalList = new ArrayList<UpdateCmrfEntryForm>();
		  List<UpdateCmrfEntryForm> districts = new ArrayList<UpdateCmrfEntryForm>();
		  if(updateCmrfEntryForm.getRecommendedBy()!=null && updateCmrfEntryForm.getRecommendedBy().equals("998")) {
			     OtherConsts=updateCmrfEntryService.getOtherConsList();
		  }
		  model.put("otherConstList",OtherConsts);
		  recommendedList = updateCmrfEntryService.getRecommendedDetails(); 
		  hospitalList = updateCmrfEntryService.getHospitalList();
		  districts=updateCmrfEntryService.getDistricts();
		  
		  model.put("recommendedList",recommendedList);
		  model.put("hospitalList",hospitalList); 
		  model.put("districts",districts);
		 
	     model.put("msg", msg);
        model.put("updateCmrfEntryForm", updateCmrfEntryForm);
         
        return "updateCmrfEntry";
    }
	
	@RequestMapping(value = "getMandals", method = RequestMethod.POST)
    public @ResponseBody String getMandals(HttpServletRequest request,@RequestParam("district_code")String districtCode,HttpServletResponse response) {
		System.out.println("getMandal");
        StringBuilder mainData = new StringBuilder();
        List<UpdateCmrfEntryForm> mandalList = new ArrayList<UpdateCmrfEntryForm>();
        try{
            mainData.append("<option value=''>--Select--</option>");
            if(districtCode!=null) {
            	mandalList=updateCmrfEntryService.getMandals(districtCode);
                for(UpdateCmrfEntryForm tempDTO : mandalList) {
                    mainData.append("<option value='" + tempDTO.getMandalNo() + "'>" + tempDTO.getMandalName() + "</option>");     
                }
            }

        } catch (Exception exception) {
            exception.printStackTrace();
        }

        return mainData.toString();
    }
	@RequestMapping(value = "getInwardData", method = RequestMethod.POST)
    public @ResponseBody String getInwardData(HttpServletRequest request,@RequestParam("mlaCmrfNo")String mlaCmrfNo,HttpServletResponse response) {
		System.out.println("getInwardData");
		String mlaCmrfData=null;
        try{
          //  mainData.append("<option value=''>--Select--</option>");
            if(mlaCmrfNo!=null) {
            	mlaCmrfData=updateCmrfEntryService.getInwardData(mlaCmrfNo);
                
            }

        } catch (Exception exception) {
            exception.printStackTrace();
        }

        return mlaCmrfData;
    }
   
	@RequestMapping(value = "getCmrfData", method = RequestMethod.POST)
    public @ResponseBody String getCmrfData(HttpServletRequest request,@RequestParam("cmrfVal")String cmrfVal,HttpServletResponse response) {
		System.out.println("getLocData");
		String cmrfData=null;
        try{
          //  mainData.append("<option value=''>--Select--</option>");
            if(cmrfVal!=null) {
            	cmrfData=updateCmrfEntryService.getCmrfData(cmrfVal);
                
            }

        } catch (Exception exception) {
            exception.printStackTrace();
        }

        return cmrfData;
    }
	@RequestMapping(value = "getCmrfLocData", method = RequestMethod.POST)
    public @ResponseBody String getCmrfLocData(HttpServletRequest request,@RequestParam("cmrfLocNo")String cmrfLocNo,HttpServletResponse response) {
		System.out.println("getCmrfLocData");
		String cmrfLocData=null;
        try{
          //  mainData.append("<option value=''>--Select--</option>");
            if(cmrfLocNo!=null) {
            	cmrfLocData=updateCmrfEntryService.getCmrfLocData(cmrfLocNo);
                
            }

        } catch (Exception exception) {
            exception.printStackTrace();
        }

        return cmrfLocData;
    } 
	
	@RequestMapping(value = "getCmrfValues", method = RequestMethod.GET)
    public String  getCmrfValues(HttpServletRequest request,@RequestParam("cmrfVal")String cmrfVal,HttpServletResponse response,  Map<String, Object> model) throws Exception 
{
		UpdateCmrfEntryForm updateCmrfEntryForm = new UpdateCmrfEntryForm();  
		System.out.println("getCmrfValues");
		boolean checkSign=false;
		boolean checkExg=false;
        try{
        	HttpSession session=request.getSession();
        	String userId=(String)session.getAttribute("userid");
    		updateCmrfEntryForm.setUserId(userId);
    		if(userId.equals("cmrf_aps")) {
    			//addCmrfEntryForm.setCmrfUser("true");	
    			model.put("cmrfUser", true);
    		}else {
    			model.put("cmrfUser", false);
    		}            if(cmrfVal!=null) {
            	updateCmrfEntryForm=updateCmrfEntryService.getCmrfValues(cmrfVal);
                
            }
    		 List<UpdateCmrfEntryForm> recommendedList = new ArrayList<UpdateCmrfEntryForm>();
   		  List<UpdateCmrfEntryForm> hospitalList = new ArrayList<UpdateCmrfEntryForm>();
   		  List<UpdateCmrfEntryForm> districts = new ArrayList<UpdateCmrfEntryForm>();
   		  List<UpdateCmrfEntryForm> mandals = new ArrayList<UpdateCmrfEntryForm>();
   		 List<UpdateCmrfEntryForm> OtherConsts = new ArrayList<UpdateCmrfEntryForm>();

   		  recommendedList = updateCmrfEntryService.getRecommendedDetails(); 
   		  hospitalList = updateCmrfEntryService.getHospitalList();
   		  districts=updateCmrfEntryService.getDistricts();
   		  mandals=updateCmrfEntryService.getMandals(updateCmrfEntryForm.getPatDistrict());
   		  model.put("mandals",mandals);
   		  model.put("recommendedList",recommendedList);
   		  model.put("hospitalList",hospitalList); 
   		  model.put("districts",districts);
   		  if(updateCmrfEntryForm.getRecommendedBy()!=null && updateCmrfEntryForm.getRecommendedBy().equals("998")) {
   			     OtherConsts=updateCmrfEntryService.getOtherConsList();
   		  }
   		  model.put("otherConstList",OtherConsts);
   		  System.out.println(updateCmrfEntryForm.getSigned());
   		  System.out.println(updateCmrfEntryForm.getExgratia());
          model.put("updateCmrfEntryForm", updateCmrfEntryForm);
          model.put("updsign", updateCmrfEntryForm.getSigned());

          model.put("cmrfUpdNo", cmrfVal);

   	
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return "updateCmrfEntry";
    }
	
	@RequestMapping(value = "getOtherConsList", method = RequestMethod.POST)
    public @ResponseBody String getOtherConsList(HttpServletRequest request,HttpServletResponse response) {
		System.out.println("getOtherConsList");
        StringBuilder mainData = new StringBuilder();
        List<UpdateCmrfEntryForm> consList = new ArrayList<UpdateCmrfEntryForm>();
        try{
           mainData.append("<option value='0'>--Select--</option>");
            	consList=updateCmrfEntryService.getOtherConsList();
        		System.out.println("getOtherConsList"+consList.size());

                for(UpdateCmrfEntryForm tempDTO : consList) {
                    mainData.append("<option value='" + tempDTO.getConstNo() + "'>" + tempDTO.getConstName() + "</option>");     
                
            }

        } catch (Exception exception) {
            exception.printStackTrace();
        }
		System.out.println("getOtherConsList"+mainData);

        return mainData.toString();

    }

	@RequestMapping(value = "getTokenBankDetails", method = RequestMethod.GET)
    public @ResponseBody String getTokenBankDetails(HttpServletRequest request,@RequestParam("tokenNo")String tokenNo,HttpServletResponse response) {
		System.out.println("getTokenBankDetails");
		String mlaCmrfData=null;
        try{
          //  mainData.append("<option value=''>--Select--</option>");
            if(tokenNo!=null) {
            	mlaCmrfData=updateCmrfEntryService.getTokenBankData(tokenNo);
            }

        } catch (Exception exception) {
            exception.printStackTrace();
        }

        return mlaCmrfData;
    }
}

package com.cgg.dataentry.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.ModelAttribute;
import com.cgg.dataentry.model.EnhancementLocDetails;

public interface EnhancementLocDetailsEntryService {
	public List<EnhancementLocDetails> getRecommendedDetails() throws Exception;
	public List<EnhancementLocDetails> getHospitalList() throws Exception;
	public String saveCmrfDetails(@ModelAttribute("locEntryForm") EnhancementLocDetails locEntryForm,
            Map<String, Object> model,HttpServletRequest request) throws Exception;
	public String getLocData(String locTokenNo) throws Exception;
	public EnhancementLocDetails getLocLetterData(String locNo,String prevLocNo) throws Exception;
	public String getPreviousLocAmount(String prevLocNo) throws Exception;

	

}

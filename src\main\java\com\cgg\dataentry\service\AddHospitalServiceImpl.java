package com.cgg.dataentry.service;

import java.sql.SQLException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.dataentry.dao.AddHospitalDao;
import com.cgg.dataentry.model.AddHospitalForm;

@Service
public class AddHospitalServiceImpl implements AddHospitalService{
	
	@Autowired
	AddHospitalDao addHospitalDao;

	@Override
	public Integer addHospital(AddHospitalForm addHospitalForm, HttpServletRequest request) {
		Integer hospcode = null;
		try {
			hospcode = addHospitalDao.addHospital(addHospitalForm,request);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return hospcode;
	}

	@Override
	public List<AddHospitalForm> getDistricts() throws Exception {
		return addHospitalDao.getDistricts();
	}

}

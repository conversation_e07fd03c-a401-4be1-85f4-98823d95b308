package com.cgg.dataentry.service;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.cgg.dataentry.model.EditLOCMLACMRFEntryModel;

public interface EditLOCMLACMRFEntryService {
	
List<EditLOCMLACMRFEntryModel> getRecommendedDetails() throws Exception;
	
	List<EditLOCMLACMRFEntryModel> getHospitalDetails() throws Exception;

	
	List<EditLOCMLACMRFEntryModel> getLocData(String loc_token) throws Exception;

	boolean updateLocDetails(EditLOCMLACMRFEntryModel locEntryForm,HttpServletRequest request);

	public List<EditLOCMLACMRFEntryModel> getDistricts() throws Exception;
}

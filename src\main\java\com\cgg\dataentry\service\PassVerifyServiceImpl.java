package com.cgg.dataentry.service;

import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.dataentry.dao.PassVerifyDao;
import com.cgg.dataentry.model.PassVerifyForm;


@Service
public class PassVerifyServiceImpl implements PassVerifyService{
	@Autowired
	private PassVerifyDao passVerifyDao;
	
	@Override
	public String validUser(PassVerifyForm passVerifyForm, Map<String, Object> model) throws Exception {
		// TODO Auto-generated method stub
		return passVerifyDao.validUser(passVerifyForm,model);
	}

}

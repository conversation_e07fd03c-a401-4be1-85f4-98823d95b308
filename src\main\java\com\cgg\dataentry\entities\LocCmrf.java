package com.cgg.dataentry.entities;

import java.sql.Timestamp;
import java.time.LocalDate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

@Entity
@Table(name = "loc_cmrf")
@Data
public class LocCmrf {
    
    @Id
    @Column(name = "loc_no")
    private String locNo;

    private String patientName;

    private String fatherName;

    private String address;

    private int assuredAmt;

    private int hospCode;

    private String purpose;

    private Timestamp timeStamp;

    private String userId;

    private String hospitalName;

    private String recommendedBy;

    private String prevLocNo;

    private Integer prevAssuredAmt;

    private LocalDate vipLetterDt;

    private String ipaddress;

    private String updatedBy;

    private Timestamp updatedOn;

    private String aadhaarNo;

    private String mobileNo;

    private Integer patDistrict;

    private Boolean isWhatsappSent;

    @Column(columnDefinition = "boolean default false")
    private Boolean isQrScanned;

    private String qrScannedBy;

    private Timestamp qrScannedTimestamp;

    private String qrScannedIpAddress;
}

package com.cgg.dataentry.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.cgg.dataentry.model.ConstituencyDetailsForm;
import com.cgg.dataentry.service.ConstituencyDetailsService;

@Controller
@RequestMapping(value = "/constituencyDetails")
public class ConstituencyDetailsController {
	@Autowired
	private ConstituencyDetailsService constDtlsService;
	
	@RequestMapping(method = RequestMethod.GET)
    public String constDtlsForm(Map<String, Object> model)throws Exception {
		List<ConstituencyDetailsForm> constituencySelectBox = new ArrayList<ConstituencyDetailsForm>();
	//	List<AddCmrfEntryForm> hospitalList = new ArrayList<AddCmrfEntryForm>();
		List<ConstituencyDetailsForm> districts = new ArrayList<ConstituencyDetailsForm>();

		ConstituencyDetailsForm constituencyDtlsForm = new ConstituencyDetailsForm();  
		constituencySelectBox = constDtlsService.getConstDtls();
	//	hospitalList = addCmrfEntryService.getHospitalList();
		districts=constDtlsService.getDistricts();
		model.put("constituencySelectBox",constituencySelectBox);
	//	model.put("hospitalList",hospitalList);
		model.put("districts",districts);
        model.put("constituencyDtlsForm", constituencyDtlsForm);
        
        return "constituencyDetails";
    }
	
	@RequestMapping(method = RequestMethod.POST)
    public String insertConstDetails(@ModelAttribute("constituencyDtlsForm") ConstituencyDetailsForm constituencyDtlsForm,
            Map<String, Object> model,@RequestParam String action,RedirectAttributes redirectAttributes) throws Exception {
		System.out.println("ttttttttttttt");
		System.out.println("action----"+action);
		String msg=null;
		int cnt=0;
		//constituencyDtlsForm.
		System.out.println("no---"+constituencyDtlsForm.getConstituencyNo());
		if( action.equals("Insert") ){
			/*
			 * if(constituencyDtlsForm.getConstituencyNo()!="0") {
			 * msg="Insertion Not Allowed"; }
			 * 
			 * else
			 */ if(constituencyDtlsForm.getConstituencyNo().equals("0")) {
			cnt=constDtlsService.insertConstDetails(constituencyDtlsForm, model);
				System.out.println("cnt----"+cnt);			
			if(cnt==1) {
				msg="Inserted successfully";
			}
			else {
				msg="Insertion Failed";
			}
		    }else {
		    	msg="Insertion Not Allowed"; 
		    	
		    }
		}
		    else if( action.equals("Update") ){
		    	cnt=constDtlsService.updateConstDetails(constituencyDtlsForm, model);
		    	if(cnt==1) {
					msg="Updated successfully";
				}
				else {
					msg="Updation Failed";
				}
		    }
		redirectAttributes.addFlashAttribute("msg",msg);
	   //  model.put("msg", msg);
	   //  List<ConstituencyDetailsForm> constituencySelectBox = new ArrayList<ConstituencyDetailsForm>();
	   //  List<ConstituencyDetailsForm> districts = new ArrayList<ConstituencyDetailsForm>();
	   //  constituencySelectBox = constDtlsService.getConstDtls();
	    // districts=constDtlsService.getDistricts();
	    // model.put("constituencySelectBox",constituencySelectBox);
	    // model.put("districts",districts);
	   //  model.put("constituencyDtlsForm", constituencyDtlsForm); 

	     constituencyDtlsForm.setConstituencyName("");
	     constituencyDtlsForm.setMlaMpName("");
	     constituencyDtlsForm.setDesignation("");
	     constituencyDtlsForm.setParty("0");
	     constituencyDtlsForm.setPrefix("0");
	     constituencyDtlsForm.setAddress("");
	     constituencyDtlsForm.setEmail("");
	     constituencyDtlsForm.setCellno("");
	     constituencyDtlsForm.setLocalAddr("");
      //  return "constituencyDetails";
	     return "redirect:/constituencyDetails";
    }
	
	@RequestMapping(value = "getConstData", method = RequestMethod.POST)
    public @ResponseBody String getConstData(HttpServletRequest request,@RequestParam("constituencyVal")String constituencyVal,HttpServletResponse response) {
		System.out.println("con");
		String constData=null;
        try{
          //  mainData.append("<option value=''>--Select--</option>");
            if(constituencyVal!=null) {
            	constData=constDtlsService.getConstData(constituencyVal);
                
            }

        } catch (Exception exception) {
            exception.printStackTrace();
        }

        return constData;
    } 
}

package com.cgg.common;

public class Converter{

public static String convert(String no){
	int i=3;
	if(no.length()>3){
		no=""+no.substring(0,no.length()-3)+","+no.substring(no.length()-3,no.length());
		String a=no.substring(0,no.length()-4);
	   while(a.length()>2){
		i=i+3;
		no=""+no.substring(0,no.length()-i)+","+no.substring(no.length()-i,no.length());
		a=no.substring(0,no.length()-i-1);
		}
    }
return no;
}

public static String inWords(String no){
	
	String[] singles={"","One","Two","Three","Four","Five","Six","Seven","Eight","Nine"};
	String[] middles={"Ten","Eleven","Twelve","Thirteen","Fourteen","Fifteen","Sixteen","Seventeen","Eighteen","Nineteen"};
	String[] tens={"Ten","Twenty","Thirty","Forty","Fifty","Sixty","Seventy","Eighty","Ninety"};

	String wordstr="";
	int nolength=0;
	int num=0;
        if(no!=null &&!no.equalsIgnoreCase("null"))
        num=Integer.parseInt(no);
	nolength=(String.valueOf(num)).length();
try{
	if(nolength>9){
		wordstr="Amount is Greater than the Range";
		return wordstr;
	}
	int ft=0;
  do{
	switch(nolength){
			case 1 :
				wordstr+=" "+singles[num];
                nolength=0;
                num=0;
                break;
			case 2 :
				if(num>10 && num<20){
					wordstr+=" "+middles[num%10];
                    num=0;
                    nolength=0;
                }
                else{
					wordstr+=" "+tens[num/10-1];
                    num=num%10;
                    nolength=1;
                }
                break;
            case 3 :
                wordstr+=" "+singles[num/100]+" Hundred";
                num=num%100;
                nolength=(String.valueOf(num).toString()).length();
                break;
            case 4 :
                wordstr+=" "+singles[num/1000]+" Thousand";
                num=num%1000;
                nolength=(String.valueOf(num).toString()).length();
                break;
            case 5 :
                ft=num/1000;
                if(ft>10 && ft<20)
					wordstr+=" "+middles[ft-10]+" Thousand";
                else {
					wordstr+=" "+tens[ft/10-1];
                    if(ft%10!=0)
						wordstr+=" "+singles[ft%10]+" Thousand";
					else
						wordstr+=" Thousand";
                }
                num=num%1000;
                nolength=(String.valueOf(num).toString()).length();
                break;
            case 6 :
                wordstr+=" "+singles[num/100000]+" Lakh";
                num=num%100000;
                nolength=(String.valueOf(num).toString()).length();
                break;
            case 7 :
                ft=num/100000;
                if(ft>10 && ft<20)
					wordstr+=" "+middles[ft-10]+" Lakh";
                else{
					wordstr+=" "+tens[ft/10-1];
                    if(ft%10!=0)
						wordstr+=" "+singles[ft%10]+" Lakh";
					else
						wordstr+=" Lakh";
                }
                num=num%100000;
                nolength=(String.valueOf(num).toString()).length();
                break;
            case 8 :
                wordstr+=" "+singles[num/10000000]+" Crore";
                num=num%10000000;
                nolength=(String.valueOf(num).toString()).length();
                break;
			case 9 :
                ft=num/10000000;
                if(ft>10 && ft<20)
					wordstr+=" "+middles[ft-10]+" Crore";
                else{
					wordstr+=" "+tens[ft/10-1];
                    if(ft%10!=0)
						wordstr+=" "+singles[ft%10]+" Crore";
					else
						wordstr+=" Crore";
                }
                num=num%10000000;
                nolength=(String.valueOf(num).toString()).length();
                break;
	}
  }while(nolength!=0);
  wordstr+=" Only";
}catch(Exception e){
		e.printStackTrace();
}
  return wordstr;
}


}

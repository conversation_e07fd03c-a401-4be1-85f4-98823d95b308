<%@ page language="java" import="java.util.*" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%
String path = request.getContextPath();
String basePath = path+"/";
%>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<base href="<%=basePath%>">
<title>Cheques Received By PR in Month</title>
    
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="expires" content="0">    
<meta http-equiv="keywords" content="keyword1,keyword2,keyword3">
<meta http-equiv="description" content="This is my page">
	
<script type="text/javascript" src="js/jquery-1.11.3.min.js"></script>
<script src="js/jquery-1.12.4.js"></script>
<script src="js/jquery-ui.js"></script>
<link rel="stylesheet" href="css/jquery-ui.css">
<jsp:include page="/WEB-INF/jsp/include_DT.jsp" />  
<script language=javascript src="./scripts/popcal/calendar.js"></script>

<script type="text/javascript">
		
function getReport(){
	if($("#month").val()=="" || $("#month").val()=="0"){
		Swal.fire({
			title: "Please Select Month",
			icon: "warning",
			confirmButtonText: "Ok"
		}).then(() => {
			$("#month").focus();
		});
		return false;
	}
	
	if($("#year").val()=="" || $("#year").val()=="0"){
		Swal.fire({
			title: "Please Select Year",
			icon: "warning",
			confirmButtonText: "Ok"
		}).then(() => {
			$("#year").focus();
		});
		return false;
	}
	
  return true;
}
	
</script>
</head>
  
<body>
   <div style="color:red;font-size: 20px;font-weight: bold;" align="left">${msg}</div>
	  <div class="container complete_wrap">
		<div>
			<div class="container text-center">
				<h3>Cheques Received By People Representative</h3>				
			</div>
		</div>
			
	    <div class="container" style="width: 65%;border: 2px solid black">
		    <form:form action="monthWisePRCheques" method="POST" modelAttribute="MonthWiseSummary">
			  <br>
			  <div class="row">
				<div class="col-sm-6">
					<div class="form-group">
						<label for="date-id">Month:</label>
						<form:select path="month" styleId="month">
                             <form:option value="0">--Select--</form:option>
                             <form:option value="1">JANUARY</form:option>
                             <form:option value="2">FEBRUARY</form:option>
                             <form:option value="3">MARCH</form:option>
                             <form:option value="4">APRIL</form:option>
                             <form:option value="5">MAY</form:option>
                             <form:option value="6">JUNE</form:option>
                             <form:option value="7">JULY</form:option>
                             <form:option value="8">AUGUST</form:option>
                             <form:option value="9">SEPTEMBER</form:option>
                             <form:option value="10">OCTOBER</form:option>
                             <form:option value="11">NOVEMBER</form:option>
                             <form:option value="12">DECEMBER</form:option>
						</form:select>
					</div>
			   </div>
						
			   <div class="col-sm-6">
				 <div class="form-group">
					<label for="date-id">Year:</label>
						<form:select path="year" styleId="year">
                            <form:option value="0">--Select--</form:option>
                      <%--  <form:option value="2024">2024</form:option> --%>
                            <form:option value="2025">2025</form:option>
						</form:select>
				</div>
			  </div>
			  <br>
			</div>
			  
			<div>
				<div align="center">
					<input type="submit" value="Get Report" name="Save" class="btn btn-primary" onclick="return getReport();" />
				</div>
			   <br>					
			</div>
				       
		</form:form>
	 </div>
	</div> 
</body>
</html>

package com.cgg.ErrorHandler;

import java.nio.file.AccessDeniedException;

import javax.servlet.RequestDispatcher;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
public class ErrorHandlerController implements ErrorController {

    private static final Logger logger = LoggerFactory.getLogger(ErrorHandlerController.class);

    @RequestMapping("/error")
    public String handleError(HttpServletRequest request, Model model) throws AccessDeniedException {
        Object status = request.getAttribute(RequestDispatcher.ERROR_STATUS_CODE);
        
        if (status != null) {
            int statusCode = Integer.parseInt(status.toString());
            
            if (statusCode == HttpStatus.NOT_FOUND.value()) {
                 return "exceptions/error404";
            } else if (statusCode == HttpStatus.FORBIDDEN.value()) {
                return "exceptions/error403";
            } else if (statusCode == HttpStatus.INTERNAL_SERVER_ERROR.value()) {
                logger.error("Internal server error occurred for request: {}", request.getRequestURI());
                return "exceptions/error500";
            }
        }
        
        return "exceptions/generic-error";
    }

    @Override
    public String getErrorPath() {
        return "/error";
    }
    
}

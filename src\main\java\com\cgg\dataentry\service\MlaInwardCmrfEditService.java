package com.cgg.dataentry.service;

import java.net.HttpURLConnection;
import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.cgg.common.BSNLSMSHttpPostClient;
import com.cgg.common.CommonUtils;
import com.cgg.common.Response;
import com.cgg.common.SMSHttpMVaayooClient;
import com.cgg.dataentry.entities.MlaCmrf;
import com.cgg.dataentry.repositories.MlaInwardCmrfEditRepository;
import com.cgg.hospital.model.InwardCmrf;

@Service
public class MlaInwardCmrfEditService {

	@Autowired
	private DataSource dataSource;
	
	@Autowired
	MlaInwardCmrfEditRepository mlaInwardCmrfEditRepository;
	
	public Optional<MlaCmrf> getTokenDetails(String mlaCmrfNo) {
		List<String> statuses = Arrays.asList("1","15");
        return mlaInwardCmrfEditRepository.findByMlaCmrfNoAndStatusIn(mlaCmrfNo, statuses);
    }
	public Integer getHospDistrictCode(Integer hospcode) {
		return mlaInwardCmrfEditRepository.findDistrictByHospcode(hospcode);
	}
	public String getBranch(String ifsc) {
		return mlaInwardCmrfEditRepository.getBranchByIfsc(ifsc);
	}
	public Response saveTokenDetails(InwardCmrf inwardCmrf,HttpServletRequest request) {
		Response response = new Response();
		Date letterDate = null;
		try {
			String MLA_CMRF_NO = inwardCmrf.getMla_cmrf_no();
			String USER_ID = inwardCmrf.getUserId();
			String IP_ADDRESS = request.getRemoteAddr();
			List<String> statuses = Arrays.asList("1","15");
			Optional<MlaCmrf> mlaCmrf = mlaInwardCmrfEditRepository.findByMlaCmrfNoAndStatusIn(MLA_CMRF_NO, statuses);
			if(!mlaCmrf.isPresent()) {
				response.setStatus(HttpStatus.NO_CONTENT);
				response.setMessage("No Records Found");
				return response;
			}
			int logCount = mlaInwardCmrfEditRepository.insertMlaCmrfLog(MLA_CMRF_NO, "Deo Update",
					USER_ID, IP_ADDRESS);

			if (logCount < 1) {

				response.setStatus(HttpStatus.NO_CONTENT);
				response.setMessage("NO DATA FOUND");
				return response;
			}
			MlaCmrf cmrf = mlaCmrf.get();
			cmrf.setOldFscNo(inwardCmrf.getOldFscNo());
			cmrf.setNewFscNo(inwardCmrf.getNewFscNo());
			cmrf.setIncomeNo(inwardCmrf.getIncomeCerNo());
			cmrf.setPatientName(inwardCmrf.getPatientName().trim());
			cmrf.setGender(inwardCmrf.getGender());
			cmrf.setAge(inwardCmrf.getAge());
			cmrf.setFatherSonOf(inwardCmrf.getGuardian()+" "+inwardCmrf.getFatherName());
			cmrf.setAadharNo(inwardCmrf.getAadharNo());
			cmrf.setMobileNo(Long.parseLong(inwardCmrf.getMobileNo()));
			cmrf.setPatDistrict(Integer.parseInt(inwardCmrf.getPatDistrict()));
			cmrf.setPatMandal(Integer.parseInt(inwardCmrf.getPatMandal()));
			cmrf.setPatVillage(Integer.parseInt(inwardCmrf.getPatVillage()));
			cmrf.setPincode(Integer.parseInt(inwardCmrf.getPinCode()));
			//cmrf.setBankDist(inwardCmrf.getBankDistrict());
			cmrf.setBankName(inwardCmrf.getBankName());
			cmrf.setBankIfsc(inwardCmrf.getBankIfsc());
			cmrf.setBankAccNo(inwardCmrf.getBankAccNo().trim());
			cmrf.setBankAccHolderName(inwardCmrf.getBankAccHolderName().trim());
			cmrf.setPatAddress(inwardCmrf.getPatAddress());
			//cmrf.setRecommendedBy(inwardCmrf.getRecommendedBy());
			
			try {
				if (!(inwardCmrf.getCmrfDate() == null || inwardCmrf.getCmrfDate().trim().equals("")))
					letterDate = new java.sql.Date(
							new SimpleDateFormat("dd/MM/yyyy").parse(inwardCmrf.getCmrfDate()).getTime());
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			cmrf.setTimeStamp(letterDate);
			cmrf.setHospCode(Integer.parseInt(inwardCmrf.getHospCode()));
			cmrf.setPatientIp(inwardCmrf.getPatientIpNo());
			cmrf.setAdmissionNo(inwardCmrf.getAdmissNo());
			cmrf.setPurpose(inwardCmrf.getPurpose());
			cmrf.setStatus(inwardCmrf.getStatus());
			cmrf.setReason(inwardCmrf.getReason());
			cmrf.setRemarks(inwardCmrf.getRemarks());
			cmrf.setAadharCopy(inwardCmrf.getAadhaarCopy());
			cmrf.setHospBillsCopy(inwardCmrf.getHospitalBills());
			cmrf.setIncCerCopy(inwardCmrf.getIncomeCertificate());
			cmrf.setFscCopy(inwardCmrf.getFscRationCard());
			cmrf.setMlaLetterCopy(inwardCmrf.getMlaLetter());
			cmrf.setBankPassCopy(inwardCmrf.getBankPassbook());
			cmrf.setDeoUpdBy(inwardCmrf.getUserId());
			cmrf.setDeoUpdDt(LocalDateTime.now());
			cmrf.setDeoUpdIpaddr(request.getRemoteAddr());
			cmrf.setPatientIpStatus(null);
			cmrf.setHosVerifiedDate(null);
			cmrf.setHospPendRes(null);
			cmrf.setRejReasons(null);
			cmrf.setVerifiedByDeo(null);
			cmrf.setDeoVerifiedDate(null);
			cmrf.setDeoVerifiedBy(null);
			cmrf.setDeoPenReasons(null);
			cmrf.setDeoRejReasons(null);

			mlaInwardCmrfEditRepository.save(cmrf);
			try {
				if ("7".equals(inwardCmrf.getStatus())) {
					sendHospConfSmsNotif(inwardCmrf);
				}
			} catch (Exception e) {
				e.printStackTrace();
				response.setMessage("Update successful, but SMS notification failed.");
			}
			response.setStatus(HttpStatus.OK);
			response.setMessage("Updated Successfully");
		}
			
		catch (Exception e) {
			response.setStatus(HttpStatus.EXPECTATION_FAILED);
			response.setMessage("Something went wrong");
			e.printStackTrace();
			return response;
		}
		return response;
	}

	private void sendHospConfSmsNotif(InwardCmrf inwardCmrf) throws Exception {
		Statement st=null;
		PreparedStatement pst=null;
		ResultSet rs=null;
		Connection con = null;
		// HttpURLConnection connection = null;
		String connection = null;
		String msg = null;
		String sql = null;
		String hospName = null;
		String tokenNo = inwardCmrf.getMla_cmrf_no();
		String smsId = null;
		try {
		sql = "SELECT hospname FROM hospital WHERE hospcode = '" + inwardCmrf.getHospCode() + "'";
		con = dataSource.getConnection();
		System.out.println(sql);
		st=con.createStatement();
		rs=st.executeQuery(sql);
		if(rs.next()) {
			hospName = rs.getString("hospname");
			if (hospName.length() > 40) {
				hospName = hospName.substring(0, 40);
			}
		}
	    String patMobileNum = inwardCmrf.getMobileNo();
	    String patientIp = inwardCmrf.getPatientIpNo();
		String patientName = inwardCmrf.getPatientName().trim();
	    // String patMobileNum = "**********";
	    
	    if(tokenNo!=null) {
	    	String templateIdEng = "1407174229387525319";
	    	String templateNameEng = "TSCMRF_18mar25";
			msg = "Dear "+ patientName +" Your CMRF application related Patient IP "+ patientIp +" details have been sent to the "+ hospName +" hospital for confirmation in https://cmrf.telangana.gov.in website. Your CMRF application will be processed after receiving the confirmation from the hospital. TGCMRF";
			boolean flag = false;
			    
			if (patMobileNum != null && !patMobileNum.isEmpty()) {
			    try {
		            sql = "insert into cmrf_pat_sms(mobile_no,msg,sent_date,token_no) values ('"  + patMobileNum + "','" + msg + "',now(),'"+tokenNo+"')";
		            System.out.println("--sql---" + sql);
		            flag = CommonUtils.insertQuery(con, sql);

			        if (flag) {
			            sql = "SELECT MAX(sms_id) FROM cmrf_pat_sms";
			            rs = st.executeQuery(sql);
			            if (rs.next()) {
		                    smsId = rs.getString(1);
		                }
			            connection = BSNLSMSHttpPostClient.sendBSNLSms(msg, "91" + patMobileNum, templateIdEng, templateNameEng);
			            if ("200".equals(connection)) {
			                sql = "update cmrf_pat_sms set sms_response='" + connection +
			                      "' where sms_id='" + smsId + "'";
		                    flag = CommonUtils.updateQuery(con, sql);
		                }
		            }
			    } catch (SQLException e) {
			        e.printStackTrace();
			    }
		    }
	    }
	    
	    if(tokenNo!=null) {
	    	String templateIdTel = "1407174231313966049";
	    	String templateNameTel = "TSCMRF_180325_1";
			msg = "ప్రియమైన "+ patientName +", మీ CMRF దరఖాస్తుకు సంబంధించిన IP నంబర్ "+ patientIp +" వివరాలు https://cmrf.telangana.gov.in వెబ్‌సైట్‌లో ధృవీకరణ కోసం "+ hospName +" కి పంపబడ్డాయి. మీ CMRF దరఖాస్తు హాస్పిటల్ నుండి నిర్ధారణ పొందిన తర్వాత ప్రాసెస్ చేయబడుతుంది.TGCMRF";
			boolean flag = false;
			    
			if (patMobileNum != null && !patMobileNum.isEmpty()) {
			    try {
		            sql = "insert into cmrf_pat_sms(mobile_no,msg,sent_date,token_no) values ('"  + patMobileNum + "','" + msg + "',now(),'"+tokenNo+"')";
		            System.out.println("--sql---" + sql);
		            flag = CommonUtils.insertQuery(con, sql);

			        if (flag) {
			            sql = "SELECT MAX(sms_id) FROM cmrf_pat_sms";
			            rs = st.executeQuery(sql);
			            if (rs.next()) {
		                    smsId = rs.getString(1);
		                }
			            connection = BSNLSMSHttpPostClient.sendBSNLUnicodeSms(msg, "91" + patMobileNum, templateIdTel, templateNameTel);
			            if ("200".equals(connection)) {
			                sql = "update cmrf_pat_sms set sms_response='" + connection +
			                      "' where sms_id='" + smsId + "'";
		                    flag = CommonUtils.updateQuery(con, sql);
		                }
		            }
			    } catch (SQLException e) {
			        e.printStackTrace();
			    }
		    }
	    }
		}catch (Exception e) {
			e.printStackTrace();
		}finally {
			CommonUtils.closeCon(con, st, rs);
		}
		
	}

}

package com.cgg.dataentry.repositories;

import java.util.List;

import javax.persistence.Tuple;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import com.cgg.dataentry.entities.Ifsccodes;

public interface AddIfscRepository extends JpaRepository<Ifsccodes, String>{
	
	@Query(value="select distno,distname from district order by distname",nativeQuery = true)
	List<Tuple> getDistricts();

	@Query(value="select distinct(bankname) from ifsccodes i order by bankname",nativeQuery = true)
	List<Tuple> getBanks();

}

package com.cgg.dataentry.controller;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.cgg.dataentry.model.AddCmrfEntryForm;
import com.cgg.dataentry.model.PassVerifyForm;
import com.cgg.dataentry.service.PassVerifyService;

@Controller
@RequestMapping(value = "/passVerify")
public class PassVerifyController {
	
	@Autowired
	private PassVerifyService passVerifyService;
	
	@RequestMapping(method = RequestMethod.GET)
    public String inwardCmrf(Map<String, Object> model,ServletRequest request)throws Exception {
		
		List<PassVerifyForm> recommendedList = new ArrayList<PassVerifyForm>();
		PassVerifyForm passVerifyForm = new PassVerifyForm();  
	//	recommendedList = inwardService.getRecommendedDetails();
		System.out.println(request.getParameter("mode1"));
		model.put("recommendedList",recommendedList);
        model.put("passVerifyForm", passVerifyForm);
         
      //  return "/passVerify";
        return "/dataentry/passVerify";
    }
	
	@RequestMapping(method = RequestMethod.POST)
    public String verifyUser(@ModelAttribute("passVerifyForm") PassVerifyForm passVerifyForm,
            Map<String, Object> model) throws Exception{
	 String msg=passVerifyService.validUser(passVerifyForm, model);
		 model.put("msg", msg);
		 passVerifyForm.setMessage(msg);
		 model.put("message", msg);
		 AddCmrfEntryForm addCmrfEntryForm=new AddCmrfEntryForm();
		 if(msg!=null && msg.equals("authorised")) {
			 addCmrfEntryForm.setValueck("y");
		 }else {
			 addCmrfEntryForm.setValueck("n");
		 }
       // model.put("passVerifyForm", passVerifyForm);
        // inwardService.
      //  return "passVerify";
        return "/dataentry/passVerify";

    }

}

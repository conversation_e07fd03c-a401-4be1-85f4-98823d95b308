package com.cgg.dataentry.dao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import com.cgg.dataentry.model.EnhancementLocDetails;
import com.cgg.common.CommonFunctions;
import com.cgg.common.CommonUtils;

@Repository
public class EnhancementLocDetailsEntryDaoImpl implements EnhancementLocDetailsEntryDao{
	@Autowired
	private DataSource dataSource;
	
	@Autowired
	JdbcTemplate jdbcTemlate;
	
	public List<EnhancementLocDetails> getRecommendedDetails() throws Exception{
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<EnhancementLocDetails> locEntryDtls = new ArrayList<EnhancementLocDetails>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			sql="select cno,mlamp||design||'('||party||'), '||cname as cname from  constituency  order by mlamp,cno";
			rs=st.executeQuery(sql);
			while(rs.next()) {
				EnhancementLocDetails locEntry = new EnhancementLocDetails();
				locEntry.setConstNo(rs.getString("cno"));
				locEntry.setConstName(rs.getString("cname"));
				locEntryDtls.add(locEntry);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con,st,rs);
		}
		return locEntryDtls;
	}
	public List<EnhancementLocDetails> getHospitalList() throws Exception{
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<EnhancementLocDetails> hospDetails = new ArrayList<EnhancementLocDetails>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			sql="select hospcode, hospname||'('||recog||')' as hospname from  hospital where delete_flag='false' order by hospname";
			rs=st.executeQuery(sql);
			while(rs.next()) {
				EnhancementLocDetails locEntry = new EnhancementLocDetails();
				locEntry.setHospCode(rs.getString("hospcode"));
				locEntry.setHospName(rs.getString("hospname"));
				hospDetails.add(locEntry);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con,st,rs);
		}
		return hospDetails;
	}
	@Override
	public String saveLocDetails(EnhancementLocDetails formbean, Map<String, Object> model,HttpServletRequest request)throws Exception {
		// TODO Auto-generated method stub
		System.out.println("dap");
		Connection	con=null;
		Statement	st=null;
		boolean insFlag=false;
		ResultSet rs=null;
		String locNo=null;
		try {
		con = dataSource.getConnection();
		st=con.createStatement();
		//String userId="cmrf1";
		String userId=null;
		userId=(String)formbean.getUserId();

		String qry=null;
		Statement stmt=null;
		stmt=con.createStatement();	
		Map<String,Object> insertMap = new HashMap<String,Object>();
		String sql=null;	
		sql ="select coalesce(max(int4(split_part(loc_no,'/',1))+1),1) from loc_cmrf where split_part(loc_no,'/',3)=to_char(current_date,'yyyy')";
        System.out.println("1---"+sql);
        
		 String var1 = CommonUtils.getStringfromQuery(con,sql);
		System.out.println("var1----"+var1);
		String currYear = new SimpleDateFormat("yyyy").format(new Date());
		System.out.println("currYear--------"+currYear);
		 locNo=var1+"/CMRF-LOC/"+currYear;
		System.out.println("loc--"+ locNo);
		formbean.setLocNo(locNo);
//var1 = var1 + 6;
//String loc_no=var1+1+currYear;
 		insertMap.put("loc_no","'"+ locNo+"'");
		
	//	insertMap.put("cmrf_dt","to_date('"+formbean.getCmrfDate()+"','dd/mm/yyyy')");
		
		
		if(CommonFunctions.validateData(formbean.getPatientName()))
			insertMap.put("patient_name","'"+formbean.getPatientName()+"'");
		
		if(CommonFunctions.validateData(formbean.getFatherName()))
			insertMap.put("father_name","'"+formbean.getFatherName()+"'");
		
		if(CommonFunctions.validateData(formbean.getAadharNo()))
			insertMap.put("aadhaar_no","'"+formbean.getAadharNo()+"'");
		
		if(CommonFunctions.validateData(formbean.getMobileNo()))
			insertMap.put("mobile_no","'"+formbean.getMobileNo()+"'");
		
		if(CommonFunctions.validateSelectBox(formbean.getAddress()))
			insertMap.put("address","'"+formbean.getAddress()+"'");
		
		if(CommonFunctions.validateSelectBox(formbean.getAssuredAmt()))
			insertMap.put("assured_amt","'"+formbean.getAssuredAmt()+"'");
		
		if(CommonFunctions.validateData(formbean.getHospCode()))
			insertMap.put("hosp_code","'"+formbean.getHospCode()+"'");
		
		if(CommonFunctions.validateData(formbean.getPurpose()))
			insertMap.put("purpose","'"+formbean.getPurpose()+"'");	
		
		
		if(CommonFunctions.validateData(formbean.getHospCode()))
			insertMap.put("hospital_name","'"+formbean.getTxthospname()+"'");
		
		if(CommonFunctions.validateData(formbean.getRecommendedBy()))
			insertMap.put("recommended_by","'"+formbean.getRecommendedBy()+"'");
		if(CommonFunctions.validateData(userId))
			insertMap.put("user_id","'"+userId+"'");
		if(CommonFunctions.validateSelectBox(formbean.getPrevLocAmt()))
			insertMap.put("prev_assured_amt","'"+formbean.getPrevLocAmt()+"'");
		
		if(CommonFunctions.validateData(formbean.getPrevLocNo()))
 		  insertMap.put("prev_loc_no","'"+formbean.getPrevLocNo()+"'");

		insertMap.put("vip_letter_dt","to_date('"+formbean.getVipLetterDate()+"','dd/mm/yyyy')");
		
		insertMap.put("ipaddress","'"+formbean.getIpAddress()+"'");
		String insquery=null;
		synchronized (this) {
			Map<String,Object> tmap = CommonFunctions.insertQuery(insertMap);
			if(tmap!=null && !tmap.isEmpty())
				 insquery = "insert into loc_cmrf ("+(tmap.get("colNames"))+") values ("+tmap.get("colValues")+")";
			System.out.println("insquery---"+insquery);

			 insFlag = CommonUtils.insertQuery(con, insquery);
		}
	    if(insFlag) {
	    	qry = "insert into loc_mla_cmrf_log(mla_loc_no, patient_name, "
    				+ "father_son_of,aadhaar_no,mobile_no, purpose, assured_amt, recommended_by, hosp_code, status, loc_no, entered_on,"
    				+ " delete_flag, updated_on, rej_reasons, address, vip_letter_dt,ip_address,logged_timestamp,logged_ipaddress,logged_by,"
    				+ "logged_remarks,updated_by)"
    				+ "select mla_loc_no, patient_name, "
    				+ "father_son_of,aadhaar_no,mobile_no, purpose, assured_amt, recommended_by, hosp_code, status, loc_no, entered_on,"
    				+ " delete_flag, updated_on, rej_reasons, address, vip_letter_dt,ip_address,now(),'"+request.getRemoteAddr()+"','"+userId+"'"
    						+ ",'Edit Loc',updated_by " 
    						+ " from loc_mla_cmrf where mla_loc_no = '"+ formbean.getLocToken()+"' and status in ('1','9')" ;
	    	
	  	              System.out.println("In updateDB upd"+qry);
	  	              int res = st.executeUpdate(qry); 
	  	    if(res>0) {
	    	String updQry="update loc_mla_cmrf set status='3',loc_no='" +locNo+"'"
	                   +" ,updated_by = '" + userId + "', updated_on = '" + LocalDateTime.now() + "', " + "ip_address = '" + request.getRemoteAddr() + "' "
	    			+ " where mla_loc_no='" + formbean.getLocToken()+ "' and status in ('1','9')";
            System.out.println("query is sql2" + updQry);
		 boolean updFlag = CommonUtils.updateQuery(con, updQry);
	  	    }
            
	    }else {
	    	//msg="Insertion Failed";
	    }
		System.out.println("qry---"+qry);
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		
		return locNo;
	}
	
	
	public String getLocData(String locTokenNo) throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		String locStr=null;
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
            sql = "select patient_name||':#'||father_son_of||':#'||aadhaar_no||':#'||mobile_no||':#'||address||':#'||purpose||':#'||coalesce(assured_amt,0)||':#'||recommended_by||':#'||to_char(vip_letter_dt,'dd/mm/yyyy')||':#'||hosp_code  as locStr from loc_mla_cmrf  "
            		+ "where mla_loc_no='" + locTokenNo + "' and status in ('1','9') and  delete_flag=false";
			System.out.println("sql---"+sql);
			rs=st.executeQuery(sql);
			while(rs.next()) {
				locStr=rs.getString("locStr");
				
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return locStr;

}
	public EnhancementLocDetails getLocLetterData(String locNo,String prevLocNo) throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		EnhancementLocDetails locEntry = new EnhancementLocDetails();
		String prevLocDate=null;
		try {
            System.out.println("prevLocNo---"+prevLocNo);

			con = dataSource.getConnection();
			st=con.createStatement();
			sql="select  to_char(time_stamp,'dd-mm-yyyy') from loc_cmrf where loc_no='"+prevLocNo+"'";
            System.out.println("sql---"+sql);
            prevLocDate=CommonUtils.getStringfromQuery(con, sql);
			
            sql = " SELECT user_id,loc_no, patient_name, father_name, address, assured_amt, case when hospital_name is null then hospname else hospital_name end as hospname,  "
            		+ " to_char(time_stamp,'dd-mm-yyyy') as letterDate,mlamp , case when minister='Y' then 'Hon''ble ' else '' end ||cname as vipdesig,to_char(vip_letter_dt,'dd-mm-yyyy') as vipletterdate,"
            		+ " '"+prevLocDate+"' as prevLocDate, prefix,prev_loc_no,prev_assured_amt+assured_amt as prevAssAmt   from  public.loc_cmrf  L"
            		+ " inner join hospital h on l.hosp_code=h.hospcode left join constituency c on"
            		+ " c.cno=recommended_by::int2 "
            		+ " where loc_no='"+locNo+"'";
          			System.out.println("sql-ddd--"+sql);
			rs=st.executeQuery(sql);
			if(rs.next()) {
				locEntry.setLocNo(rs.getString("loc_no"));
				locEntry.setPatientName(rs.getString("patient_name"));
				locEntry.setFatherName(rs.getString("father_name"));
				locEntry.setAddress(rs.getString("address"));
				locEntry.setAssuredAmt(rs.getString("assured_amt"));
				locEntry.setHospName(rs.getString("hospname"));
				locEntry.setLetterDate(rs.getString("letterDate"));
				locEntry.setVipName(rs.getString("mlamp"));
				locEntry.setVipDesg(rs.getString("vipdesig"));
				locEntry.setVipLetterDate(rs.getString("vipletterdate"));
				locEntry.setPrevLocAmt(rs.getString("prevAssAmt"));
				locEntry.setPrevLocNo(rs.getString("prev_loc_no"));
				locEntry.setPrevLocLetterDate(rs.getString("prevLocDate"));
				locEntry.setPrefix(rs.getString("prefix"));
				locEntry.setHospName(rs.getString("hospname"));
				locEntry.setPreFormatAmt(CommonUtils.convert(rs.getString("prevAssAmt")));
				locEntry.setPreAmtInWords(CommonUtils.inWords(rs.getString("prevAssAmt")));
				locEntry.setFormatAmt(CommonUtils.convert(rs.getString("assured_amt")));
				locEntry.setAmtInWords(CommonUtils.inWords(rs.getString("assured_amt")));
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return locEntry;

}
	public String getPreviousLocAmount(String prevLocNo) throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		String amount=null;
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
            sql = "select assured_amt  as amount from loc_cmrf  "
            		+ "where loc_no='" + prevLocNo + "' ";
			System.out.println("sql---"+sql);
			rs=st.executeQuery(sql);
			while(rs.next()) {
				amount=rs.getString("amount");
				
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return amount;

}

	
}

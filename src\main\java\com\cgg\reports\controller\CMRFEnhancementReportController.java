package com.cgg.reports.controller;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.cgg.common.ApplicationConstants;
import com.cgg.common.Encryptor;
import com.cgg.reports.model.DeoVerifiedPatientStatusReport;
import com.cgg.reports.service.CMRFEnhancementReportService;


@Controller
public class CMRFEnhancementReportController {
	
	
	@Autowired
	private CMRFEnhancementReportService cmrfEnhancementReportService;
	
	@GetMapping("/cmrfEnhancementReport")
	public String cmrfEnhancementReportViewPage(HttpServletRequest request) {
		HttpSession session = request.getSession();
		List<String> validRoleIds = Arrays.asList(String.valueOf(ApplicationConstants.CMRF_OFFICER),ApplicationConstants.CMRF_OSD_ROLE);
		String userId = (String) session.getAttribute("userid");
		String roleId = (String) session.getAttribute("rolesStr");
		if (userId == null || roleId == null ||  ! validRoleIds.contains(roleId)) {
			return "redirect:/";
		}
		return "cmrfEnhancementReport";
	}
	
	@PostMapping("/cmrfEnhancementReport")
	public String getCMRFEnhancementReport(@ModelAttribute("deoVerifiedStatusForm") DeoVerifiedPatientStatusReport deoVerifiedStatusForm,
			HttpSession session, Model model, RedirectAttributes redirect,HttpServletRequest request) throws Exception {
		
		DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
		DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		List<?> deoTokenVerifiedCount =null;
		
		try {
			LocalDate dateFrom = LocalDate.parse(deoVerifiedStatusForm.getDateFrom(), inputFormatter);
		    LocalDate dateTo = LocalDate.parse(deoVerifiedStatusForm.getDateTo(), inputFormatter);
			
		    deoVerifiedStatusForm.setDateFrom(dateFrom.format(outputFormatter));
		    deoVerifiedStatusForm.setDateTo(dateTo.format(outputFormatter));

			deoTokenVerifiedCount = cmrfEnhancementReportService.getCMRFEnhancementReport(deoVerifiedStatusForm);
			
			if (!deoTokenVerifiedCount.isEmpty()) {
				model.addAttribute("enhancementReportDetailsList", deoTokenVerifiedCount);
				model.addAttribute("dateFrom", deoVerifiedStatusForm.getDateFrom());
				model.addAttribute("dateTo", deoVerifiedStatusForm.getDateTo());

				Object firstRecord = deoTokenVerifiedCount.get(0);

				if (firstRecord instanceof DeoVerifiedPatientStatusReport) {
					DeoVerifiedPatientStatusReport report = (DeoVerifiedPatientStatusReport) firstRecord;
					if ("PRWise".equals(report.getReportType())) {
						model.addAttribute("isPRWise", true);
					}
				}
			} else {
				model.addAttribute("msg", "No records found!");
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return "cmrfEnhancementReport";
	}
	
	
	@GetMapping("/getPRWiseEnhDetails")
	public String getPRWiseEnhancementDetails(@ModelAttribute("deoVerifiedStatusForm") DeoVerifiedPatientStatusReport deoVerifiedStatusForm, Map<String,Object> model, HttpServletRequest request) throws Exception {
		List<?> deoTokenVerifiedCount =null;
		try {
			String dateFrom = request.getParameter("dateFrom");
			String dateTo = request.getParameter("dateTo");
			String cno = Encryptor.decrypt(request.getParameter("cno"));

			deoVerifiedStatusForm.setDateFrom(dateFrom);
			deoVerifiedStatusForm.setDateTo(dateTo);
			deoVerifiedStatusForm.setConstNo(cno);

			String otherCons=null;
			if(cno!=null && cno.equals("998")) {
				otherCons=Encryptor.decrypt(request.getParameter("OtherConst"));
				deoVerifiedStatusForm.setOtherConst(otherCons);
			}else {
				deoVerifiedStatusForm.setOtherConst("-");
			}

			deoTokenVerifiedCount = cmrfEnhancementReportService.getPRWiseEnhancementDetails(deoVerifiedStatusForm);
			
			if (!deoTokenVerifiedCount.isEmpty()) {
				model.put("enhancementReportDetailsList", deoTokenVerifiedCount);
				model.put("dateFrom", deoVerifiedStatusForm.getDateFrom());
				model.put("dateTo", deoVerifiedStatusForm.getDateTo());
				model.put("isPRWiseBack", true);
			} else {
				model.put("msg", "No records found!");
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "cmrfEnhancementReport";
	}

	
}

package com.cgg.dataentry.dao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.cgg.common.CommonUtils;
import com.cgg.dataentry.model.EditLOCMLACMRFEntryModel;


@Repository
public class EditLOCMLACMRFEntryDaoImpl implements EditLOCMLACMRFEntryDao{
	
	@Autowired
	DataSource dataSource;
	
	@Autowired
	JdbcTemplate jdbcTemlate;

	@Override
	public List<EditLOCMLACMRFEntryModel> getRecommendedDetails() throws Exception {	
		
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<EditLOCMLACMRFEntryModel> recommendedList= new ArrayList<EditLOCMLACMRFEntryModel>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			sql="select cno,mlamp||design||'('||party||'), '||cname as cname from  constituency   order by mlamp,int4(cno) ";
			rs=st.executeQuery(sql);
			while(rs.next()) {
				EditLOCMLACMRFEntryModel editlocEntryForm=new EditLOCMLACMRFEntryModel();
				editlocEntryForm.setConstNo(rs.getString("cno"));
				editlocEntryForm.setConstName(rs.getString("cname"));
				recommendedList.add(editlocEntryForm);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return recommendedList;
	}

	@Override
	public List<EditLOCMLACMRFEntryModel> getHospitalDetails() throws Exception {
		List<EditLOCMLACMRFEntryModel> hospitalList= new ArrayList<EditLOCMLACMRFEntryModel>();
		String sql="";
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		try {
			sql="select hospcode, initcap(hospname) as hospname  from   hospital where length(hospname)>0 and  delete_flag='false' order by hospname";
			con=dataSource.getConnection();
			st=con.createStatement();
			rs=st.executeQuery(sql);
			while(rs.next()) {
				EditLOCMLACMRFEntryModel editlocEntryForm=new EditLOCMLACMRFEntryModel();
				editlocEntryForm.setHospCode(rs.getString("hospcode"));  
				editlocEntryForm.setHospName(rs.getString("hospname")); 
				hospitalList.add(editlocEntryForm);
			}
		}catch (Exception e) {
			e.printStackTrace();
		}finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return hospitalList;
	}


	@Override
	public List<EditLOCMLACMRFEntryModel> getLocData(String loc_token) throws Exception {
		String sql="";
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		List<EditLOCMLACMRFEntryModel> locData=new ArrayList<EditLOCMLACMRFEntryModel>();
		try {
		if (loc_token != null && !"".equals(loc_token)) {

			sql = "select patient_name,father_son_of,aadhaar_no ,mobile_no ,coalesce(pat_district,'0') as pat_district,address,purpose,assured_amt,recommended_by,to_char(vip_letter_dt,'dd/mm/yyyy') as vip_letter_dt,hosp_code,status "
					+ "from loc_mla_cmrf  where mla_loc_no='" + loc_token + "'";
			con=dataSource.getConnection();
			st = con.createStatement();
			rs = st.executeQuery(sql);
			if (rs.next()) {
				EditLOCMLACMRFEntryModel editlocEntryForm=new EditLOCMLACMRFEntryModel();
				editlocEntryForm.setPatient_name(rs.getString("patient_name"));  
				editlocEntryForm.setFather_name(rs.getString("father_son_of")); 
				editlocEntryForm.setAadharNo(rs.getString("aadhaar_no")); 
				editlocEntryForm.setMobileNo(rs.getString("mobile_no")); 
				editlocEntryForm.setPatDistrict(rs.getString("pat_district")); 
				editlocEntryForm.setAddress(rs.getString("address")); 
				editlocEntryForm.setPurpose(rs.getString("purpose"));
				editlocEntryForm.setAssured_amt(rs.getString("assured_amt"));
				editlocEntryForm.setRecommendedBy(rs.getString("recommended_by"));
				editlocEntryForm.setVipletter_date(rs.getString("vip_letter_dt"));
				editlocEntryForm.setHospCode(rs.getString("hosp_code"));
				editlocEntryForm.setStatus(rs.getString("status"));
				locData.add(editlocEntryForm);
			} 
		} }
		catch (Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return locData;
	}

	@Override
	public boolean updateLocDetails(EditLOCMLACMRFEntryModel locEntryForm,HttpServletRequest request) {
		boolean flag=false;int count=0;
		HttpSession session=request.getSession();
		String userId=(String)session.getAttribute("userid");
		String query = "insert into loc_mla_cmrf_log(mla_loc_no, patient_name, "
				+ "father_son_of,aadhaar_no,mobile_no,pat_district, purpose, assured_amt, recommended_by, hosp_code, status, loc_no, entered_on,"
				+ " delete_flag, updated_on, rej_reasons, address, vip_letter_dt,ip_address,logged_timestamp,logged_ipaddress,logged_by,"
				+ "logged_remarks,updated_by)"
				+ "select mla_loc_no, patient_name, "
				+ "father_son_of,aadhaar_no,mobile_no,pat_district, purpose, assured_amt, recommended_by, hosp_code, status, loc_no, entered_on,"
				+ " delete_flag, updated_on, rej_reasons, address, vip_letter_dt,ip_address,now(),'"+request.getRemoteAddr()+"','"+userId+"'"
						+ ",'Edit Loc',updated_by " 
						+ " from loc_mla_cmrf where mla_loc_no = '"+locEntryForm.getLoc_token()+"'" ;
		 System.out.println(query);
         final int res = this.jdbcTemlate.update(query);
         if(res>0) {
		String sql="update  public.loc_mla_cmrf set patient_name='"+locEntryForm.getPatient_name()+"', father_son_of='"+locEntryForm.getFather_name()+"',aadhaar_no='"+locEntryForm.getAadharNo()+"',mobile_no='"+locEntryForm.getMobileNo()+"'"
                + ", address='"+locEntryForm.getAddress()+"', assured_amt="+locEntryForm.getAssured_amt()+", hosp_code="+locEntryForm.getHospCode()+", pat_district = "+ locEntryForm.getPatDistrict() +","
                + "purpose='"+locEntryForm.getPurpose()+"',recommended_by="+locEntryForm.getRecommendedBy()+","
                + "vip_letter_dt=to_date('" +locEntryForm.getVipletter_date()+"','dd/mm/yyyy'),status='"+locEntryForm.getStatus()+"'"
                +" ,updated_by = '" + userId + "', updated_on = '" + LocalDateTime.now() + "', " + "ip_address = '" + request.getRemoteAddr() + "' "
                + "  where mla_loc_no='"+locEntryForm.getLoc_token()+"' ";
		count=jdbcTemlate.update(sql);
         }
		if(count>0) {
			flag=true;
		}else {
			flag=false;
		}
		return flag;
	} 

	@Override
	public List<EditLOCMLACMRFEntryModel> getDistricts() throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<EditLOCMLACMRFEntryModel> districts = new ArrayList<EditLOCMLACMRFEntryModel>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			sql="select distno, distname from  district  order by distname";
			System.out.println("sql---"+sql);
			rs=st.executeQuery(sql);
			while(rs.next()) {
				EditLOCMLACMRFEntryModel mlaInwardLoc = new EditLOCMLACMRFEntryModel();
				mlaInwardLoc.setDistNo(rs.getString("distno"));
				mlaInwardLoc.setDistName(rs.getString("distname"));
				districts.add(mlaInwardLoc);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return districts;
	}
}

package com.cgg.common.service;

import java.util.List;

import com.cgg.reports.model.CmpReport;

public interface CommonService {

	List<CmpReport> getHospitalList() throws Exception;
	List<CmpReport> getRecommendedByList() throws Exception;
	List<CmpReport> getDistrictList() throws Exception;
	List<CmpReport> getMandals(String cmrf_no) throws Exception;
	String getDistName(int distcode) throws Exception;
	
	//ajax mandal
		String getAjaxMandals(String distCode) throws Exception;
}

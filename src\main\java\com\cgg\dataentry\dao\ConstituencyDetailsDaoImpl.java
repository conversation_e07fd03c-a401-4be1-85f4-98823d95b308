package com.cgg.dataentry.dao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.cgg.dataentry.model.ConstituencyDetailsForm;
import com.cgg.common.CommonUtils;

@Repository
public class ConstituencyDetailsDaoImpl implements ConstituencyDetailsDao{
	@Autowired
	private DataSource dataSource;
	
	@Autowired
	JdbcTemplate jdbcTemlate;

	@Override
	public List<ConstituencyDetailsForm> getDistricts() throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<ConstituencyDetailsForm> constDetails = new ArrayList<ConstituencyDetailsForm>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			sql="select DISTNO,DISTNO||','||DISTNAME as distname from DISTRICT  order by DISTNO";
			System.out.println("sql---"+sql);
			rs=st.executeQuery(sql);
			while(rs.next()) {
				ConstituencyDetailsForm constDtlsEntry = new ConstituencyDetailsForm();
				constDtlsEntry.setDistNo(rs.getString("DISTNO"));
				constDtlsEntry.setDistName(rs.getString("distname"));
				constDetails.add(constDtlsEntry);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return constDetails;
	}

	@Override
	public List<ConstituencyDetailsForm> getConstDtls() throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<ConstituencyDetailsForm> constDetails = new ArrayList<ConstituencyDetailsForm>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			sql="select cno,cno||'-'||cname||'-'||mlamp as cname from constituency  order by cno";
			rs=st.executeQuery(sql);
			while(rs.next()) {
				ConstituencyDetailsForm constDtlsEntry = new ConstituencyDetailsForm();
				constDtlsEntry.setConstNo(rs.getString("cno"));
				constDtlsEntry.setConstName(rs.getString("cname"));
				constDetails.add(constDtlsEntry);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return constDetails;
	}

	@Override
	public int  insertConstDetails(ConstituencyDetailsForm formBean, Map<String, Object> model) {
		String sql="insert into CONSTITUENCY(DISTNO,CNO,CNAME,MLAMP,ADDR,PARTY,DESIGN,MINISTER,cell_no,local_address,email,prefix,gender,active_con) values('"+formBean.getDistrictId()+"',(select max(CNO)+1 from constituency),'"+formBean.getConstituencyName()+"',"
				+ "'"+formBean.getMlaMpName()+"','"+formBean.getAddress()+"','"+formBean.getParty()+"','"+formBean.getDesignation()+"','"+formBean.getMinsiter()+"','"+formBean.getCellno()+"','"+formBean.getLocalAddr()
				+ "','"+formBean.getEmail()+"','"+formBean.getPrefix()+"','"+formBean.getGender()+"',true)";
	   System.out.println("insert---"+sql);
		return jdbcTemlate.update(sql);
	}
	@Override
	public int  updateConstDetails(ConstituencyDetailsForm formBean, Map<String, Object> model)throws Exception {
		
		String gender="";
		  String sql=null;
		  Connection	con=null;
			Statement	st=null;
			String var=null;
			
	
		  int cnt=0;
		  try {
			  con = dataSource.getConnection();
			  sql ="select count(*) from CONSTITUENCY where CNO='"+formBean.getConstituencyNo()+"'";
				System.out.println(sql);
				var=CommonUtils.getStringfromQuery(con, sql);
				
				if(var.equals("1")) {
					
					 if(formBean.getGender()==null)
				            gender=" gender=null" ;
				        else
				            gender=" gender=  '"+formBean.getGender()+"' " ;
				            
					 sql="update CONSTITUENCY set DISTNO='"+formBean.getDistrictId()+"',"
							+ "CNO='"+formBean.getConstituencyNo()+"',CNAME='"+formBean.getConstituencyName()+"',"
							+ "MLAMP='"+formBean.getMlaMpName()+"',ADDR='"+formBean.getAddress()+"',PARTY='"+formBean.getParty()+"',"
							+ "DESIGN='"+formBean.getDesignation()+"',MINISTER='"+formBean.getMinsiter()+"',"
							+ "cell_no='"+formBean.getCellno()+"',local_address='"+formBean.getLocalAddr()+"',"
							+ "email='"+formBean.getEmail()+"', "+gender+",prefix='"+formBean.getPrefix()+"',"
							+ "active_con='"+formBean.getIsActive()+"' where CNO='"+formBean.getConstituencyNo()+"'";
				   System.out.println("insert---"+sql);
				   cnt=jdbcTemlate.update(sql);
					return cnt;
				  }else {
					  System.out.println("else");
					  return cnt;
				  }
			
		  }
		  catch(Exception e) {
				e.printStackTrace();
			}
		  finally {
			  CommonUtils.closeCon(con, st, null);
			}
		return cnt;
		
	}

	@Override
	public String getConstData(String constituencyVal) throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		String costStr=null;
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			
			sql = "select distno||':#'||cname||':#'||mlamp||':#'||design||':#'||party||':#'||minister||':#'||prefix ||':#'||gender||':#'||addr||':#'||cell_no||':#'||email ||':#'||active_con ||':#'||local_address "  	
          + " as costStr  from constituency  "
           + "where cno='" + constituencyVal + "' ";
			System.out.println("sql---"+sql);
			rs=st.executeQuery(sql);
			while(rs.next()) {
				costStr=rs.getString("costStr");
				
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return costStr;

	}
	
}

package com.cgg.common;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class WhatsAppAPIService {

	// Static configuration
	private static final String API_KEY = "47121f45-8a42-435c-bf6b-8d3b500cbc14";
	private static final String ENDPOINT = "https://api.telinfy.net/gagp/whatsapp/templates/message";
	// Use a single instance of RestTemplate
	private final RestTemplate restTemplate = new RestTemplate();
	//template name
	private static final String TEMPLATE_NAME= "loclinktemplate";
	
	
	public static void main(String[] args) {
		WhatsAppAPIService service = new WhatsAppAPIService();
		String to = "+919121448388";
		String templateName = "loclinktemplate";
		String url = "Loc/2025/1031_LOC.pdf";

		int responseCode = service.sendWhatsAppMessage(to,  url);
		System.out.println("Test Result: HTTP Response Code = " + responseCode);
	}

	public int sendWhatsAppMessage(String to, String url) {
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.set("Api-Key", API_KEY); 

		// Build request body
		Map<String, Object> requestBody = buildLOCButtonRequestBody(to,"preview/"+url);

		// Print the Request Body before sending
		System.out.println("Request Body: " + requestBody);

		HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

		// Send the request
		try {
			ResponseEntity<String> response = restTemplate.exchange(ENDPOINT, HttpMethod.POST, entity, String.class);
			System.out.println("Response Code: " + response.getStatusCodeValue());
			System.out.println("Response Body: " + response.getBody());
			return response.getStatusCodeValue();
		} catch (Exception e) {
			System.err.println("Error sending WhatsApp message: " + e.getMessage());
			return 500; // Return error status
		}
	}

	private Map<String, Object> buildLOCButtonRequestBody(String to,  String url) {
		Map<String, Object> requestBody = new HashMap<>();
		requestBody.put("to", to);
		requestBody.put("templateName", TEMPLATE_NAME);
		requestBody.put("language", "en");
		requestBody.put("header", null);
		requestBody.put("body", null);

		// Button structure
		Map<String, Object> button = new HashMap<>();
		button.put("sub_type", "url");
		button.put("index", 1);

		// Parameters inside button
		Map<String, String> param = new HashMap<>();
		param.put("type", "text");
		param.put("text", url);

		// Attach parameters to button
		button.put("parameters", Collections.singletonList(param));
		requestBody.put("button", Collections.singletonList(button));

		return requestBody;
	}

	 

	/*
	 * //test new loclinktemplate and declare static methods public static void
	 * main(String[] args) { // Test parameters String to = "+919121448388"; //
	 * Replace with actual recipient number String templateName = "loclinktemplate";
	 * String url = "preview/Loc/2025/1033_LOC.pdf";
	 * 
	 * // Call the method and capture response int responseCode; try { responseCode
	 * = WhatsAppAPI.sendWhatsAppMessage(to, templateName, url); } catch (Exception
	 * e) { // TODO Auto-generated catch block e.printStackTrace(); }
	 * 
	 * // Print test result System.out.println("Test Result: HTTP Response Code = "
	 * + responseCode); } public static int sendRequest(String requestBody) throws
	 * Exception { HttpURLConnection connection = null;
	 * 
	 * try { URL url = new URL(ENDPOINT); connection = (HttpURLConnection)
	 * url.openConnection();
	 * 
	 * connection.setRequestMethod("POST"); connection.setRequestProperty("Api-Key",
	 * API_KEY); connection.setRequestProperty("Content-Type", "application/json");
	 * connection.setDoOutput(true);
	 * 
	 * try (OutputStream os = connection.getOutputStream()) {
	 * os.write(requestBody.getBytes("UTF-8")); os.flush(); }
	 * 
	 * int responseCode = connection.getResponseCode();
	 * System.out.println("Response Code: " + responseCode);
	 * 
	 * return responseCode; } finally { if (connection != null) {
	 * connection.disconnect(); } } } public static void main(String[] args) {
	 * String to = "+919121448388"; String templateName = "pdftemplate"; String link
	 * = "https://uat5.cgg.gov.in/tgcmrf/preview/Loc/2024/12880_LOC.pdf";
	 * 
	 * try { String requestBody = WhatsAppAPI.buildPDFRequestBody(to, templateName,
	 * link); System.out.println(requestBody); int responseCode =
	 * WhatsAppAPI.sendPDFRequest(requestBody); if (responseCode ==
	 * HttpURLConnection.HTTP_OK) {
	 * System.out.println("Message sent successfully."); } else {
	 * System.out.println("Failed to send message. HTTP Error Code: " +
	 * responseCode); } } catch (Exception e) { e.printStackTrace(); } }
	 */
}

package com.cgg.common;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;

import java.util.Map;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;

import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import com.fasterxml.jackson.databind.ObjectMapper;

public class BSNLSMSHttpPostClient {
	
 	private static final String BSNL_AUTH_URL = "https://cpaas.bsnl.co.in/SMS/api/token";
    private static final String BSNL_SMS_URL = "https://cpaas.bsnl.co.in/SMS/api/broadcast";
    private static final String BSNL_USERNAME = "<EMAIL>";
    private static final String BSNL_PASSWORD = "$WkbwKY4^SzS2NG";//"Cgg@2024"
    private static final String BSNL_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1bmlxdWVfbmFtZSI6IjE5IiwibmJmIjoxNzM1ODEzNDYyLCJleHAiOjE3OTg4ODU0NjIsImlhdCI6MTczNTgxMzQ2MiwiaXNzIjoiaHR0cHM6Ly8xOTIuOS4yMDAuNzAvIiwiYXVkIjoiaHR0cHM6Ly8xOTIuOS4yMDAuNzAvIn0.BE2lZZYbZdGpj7asZRZWgTF-NKbxD2399HfMZj2XB4I";
	private static final String BSNL_TEMPLATE_NAME="TSCMRF_211224_1"; //every template id has individual Template name
	private static final String SENDER_ID="TSCMRF"; //every template id has individual Template name
	
	
	/* BSNL SMS API Integration Start */
	public static String sendBSNLSms(String message, String mobileNumber,  String templateId, String templateName) throws Exception {
		
		//String responseString="404";
		String responseString=null;
		
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost postRequest = new HttpPost(BSNL_SMS_URL);
        postRequest.setHeader("Content-Type", "application/json");
        postRequest.setHeader("Authorization", "Bearer " + BSNL_TOKEN);

        Map<String, Object> smsPayload = new HashMap<>();
        smsPayload.put("pingBackType", "0");
        smsPayload.put("pingBackId", "0");

        Map<String, Object> jsonData = new HashMap<>();
        jsonData.put("senderId", SENDER_ID); 
        jsonData.put("templateId", templateId); 
        jsonData.put("templateName", templateName);
        jsonData.put("unicodeStatus", 0);

        Map<String, String> messageData = new HashMap<>();
        messageData.put("msisdn", mobileNumber);
        messageData.put("message", message);
        messageData.put("customerReferenceId", "");

        jsonData.put("messages", new Map[]{messageData});
        smsPayload.put("jsonData", jsonData);
        smsPayload.put("validationFlag", "1");

        ObjectMapper mapper = new ObjectMapper();
        StringEntity entity = new StringEntity(mapper.writeValueAsString(smsPayload));
        postRequest.setEntity(entity);

        try (CloseableHttpResponse response = httpClient.execute(postRequest)) {
             responseString = EntityUtils.toString(response.getEntity());
             System.out.println("responseString  "+responseString);
            if (response.getStatusLine().getStatusCode() == 200) {
                System.out.println("BSNL SMS sent successfully");
                responseString=""+response.getStatusLine().getStatusCode();
            } else {
            	responseString=""+response.getStatusLine().getStatusCode();
            	
                System.out.println("BSNL SMS Failed to send " );
            }
            return responseString;
        }
    }
	/* BSNL SMS API Integration End */
	
	
	/* BSNL SMS API Integration Start - FOR UNICODE SMS */
	public static String sendBSNLUnicodeSms(String message, String mobileNumber,  String templateId, String templateName) throws Exception {
		
		//String responseString="404";
		String responseString=null;
		
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost postRequest = new HttpPost(BSNL_SMS_URL);
        postRequest.setHeader("Content-Type", "application/json; charset=UTF-8");
        postRequest.setHeader("Accept-Charset", "UTF-8");
        postRequest.setHeader("Authorization", "Bearer " + BSNL_TOKEN);

        Map<String, Object> smsPayload = new HashMap<>();
        smsPayload.put("pingBackType", "0");
        smsPayload.put("pingBackId", "0");

        Map<String, Object> jsonData = new HashMap<>();
        jsonData.put("senderId", SENDER_ID); 
        jsonData.put("templateId", templateId); 
        jsonData.put("templateName", templateName);
        jsonData.put("unicodeStatus", 8);

        Map<String, String> messageData = new HashMap<>();
        messageData.put("msisdn", mobileNumber);
        messageData.put("message", message);
        messageData.put("customerReferenceId", "");

        jsonData.put("messages", new Map[]{messageData});
        smsPayload.put("jsonData", jsonData);
        smsPayload.put("validationFlag", "1");

        ObjectMapper mapper = new ObjectMapper();
        StringEntity entity = new StringEntity(mapper.writeValueAsString(smsPayload), StandardCharsets.UTF_8);
        postRequest.setEntity(entity);
        
        System.out.println("JSON Payload: " + mapper.writeValueAsString(smsPayload));

        try (CloseableHttpResponse response = httpClient.execute(postRequest)) {
             responseString = EntityUtils.toString(response.getEntity());
             System.out.println("responseString  "+responseString);
            if (response.getStatusLine().getStatusCode() == 200) {
                System.out.println("BSNL SMS sent successfully");
                responseString=""+response.getStatusLine().getStatusCode();
            } else {
            	responseString=""+response.getStatusLine().getStatusCode();
            	
                System.out.println("BSNL SMS Failed to send " );
            }
            return responseString;
        }
    }
	/* BSNL SMS API Integration End - FOR UNICODE SMS */
		
}
		
		
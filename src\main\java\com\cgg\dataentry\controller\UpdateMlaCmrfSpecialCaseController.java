package com.cgg.dataentry.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.cgg.common.ApplicationConstants;
import com.cgg.dataentry.model.MlaCmrfEntityModel;
import com.cgg.dataentry.service.UpdateMlaCmrfSpecialCaseService;


@Controller
public class UpdateMlaCmrfSpecialCaseController {

	@Autowired
	UpdateMlaCmrfSpecialCaseService updateMlaCmrfSpecialCaseService;

	@GetMapping("/updateMlaCmrfSpecialCase")
	public String updateMlaCmrfSpecialCaseViewPage(HttpServletRequest request) {
		HttpSession session = request.getSession();
		// List<String> validRoleIds = Arrays.asList("25");
		List<String> validRoleIds = Arrays.asList(ApplicationConstants.PS_OSD_CM_ROLE, ApplicationConstants.CMRF_OSD_ROLE);
		String userId = null;
		String roleId = null;
		userId = (String) session.getAttribute("userid");
		roleId = (String) session.getAttribute("rolesStr");
		if (roleId == null || !validRoleIds.contains(roleId) || userId == null) {
			return "redirect:/";
		}
		return "updateMlaCmrfSpecialCase";
	}

	
	@PostMapping("getMlaCmrfDetails")
	public String getMlaCmrfDetails(HttpServletRequest request ,Model model,@RequestParam("mlaCmrfNo") String mlaCmrfNo,@RequestParam("year") String year,
			RedirectAttributes redirect) {
		
		String mlatTokenNo=mlaCmrfNo+year;
		MlaCmrfEntityModel mlaCmrfDetails = updateMlaCmrfSpecialCaseService.getMlaCmrfDetails(mlatTokenNo);	
		
		if(mlaCmrfDetails==null) {
			redirect.addFlashAttribute("error","Details Not Found");
			return  "redirect:/updateMlaCmrfSpecialCase";
		} 

		boolean isSpecialFlag = mlaCmrfDetails.isSpecialFlag();
		System.out.println("Special Flag is : "+isSpecialFlag);
		
		if (isSpecialFlag) {
			redirect.addFlashAttribute("error","Special case is already mapped for this file.");
			return  "redirect:/updateMlaCmrfSpecialCase";
		}
		model.addAttribute("mlaCmrfDetails", mlaCmrfDetails);
		return "updateMlaCmrfSpecialCase";
	}
	
	
	@PostMapping("updateMlaCmrfSpecialCaseFlag")
	public String updateMlaCmrfSpecialCase(HttpServletRequest request,Model model,@RequestParam("mlaCmrfNo1") String mlaCmrfNo,
			@RequestParam("isSpecialFlg") Boolean isSpecialFlg,@RequestParam("referredBy") String referredBy, RedirectAttributes redirect) {
	
		HttpSession session = request.getSession();
		String userId = (String) session.getAttribute("userid");
	
		if(!isSpecialFlg) {
			referredBy=null;
		}
		
		Map<String, String> resultMap = updateMlaCmrfSpecialCaseService.updateMlaCmrfSpecialCase(mlaCmrfNo, isSpecialFlg, userId, referredBy, request);

		if (resultMap.containsKey("error")) {
			redirect.addFlashAttribute("error", resultMap.get("error"));
		} else {
			redirect.addFlashAttribute("success", resultMap.get("success"));
		}
		
		return "redirect:/updateMlaCmrfSpecialCase";
	}
	
	

}

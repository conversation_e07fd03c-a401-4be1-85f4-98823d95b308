package com.cgg.proceedings.repositories;

import java.util.List;

import javax.persistence.Tuple;
import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.cgg.proceedings.entities.Cmrelief;

@Repository
public interface PRAckLettersRepository extends JpaRepository<Cmrelief, String> {
    
    @Query(value = " SELECT COALESCE(cm.other_const, '-') AS other_const, c.cno, "
            + " CASE WHEN c.cno = 998 THEN mlamp || ' - ' || cm.other_const WHEN c.cno IN (999,997,194) THEN mlamp ELSE mlamp || ' (' || cname || ')' END AS cname, count(1) as total_cnt, pal.is_qr_scanned, pal.ack_letter "
            + " FROM cmrelief cm "
            + " LEFT JOIN constituency c ON c.cno = cm.recommended_by "
            + " LEFT JOIN pr_acknowledgement_letters pal "
            + " ON CAST(cm.esigned_date AS DATE) = CAST(pal.esigned_date AS DATE) "
            + " AND pal.cno = cm.recommended_by "
            + " AND (pal.other_const = cm.other_const OR (pal.other_const IS NULL AND cm.other_const IS NULL)) "
            + " WHERE CAST(cm.esigned_date AS DATE) >= to_date(:dateFrom,'DD-MM-YYYY') AND CAST(cm.esigned_date AS DATE) <= to_date(:dateTo,'DD-MM-YYYY') AND cm.esigned_date IS NOT NULL "
            + " AND (cmrf_loc IS NULL OR cmrf_loc = '' OR cmrf_loc = '0') "
            + " GROUP BY cm.other_const, c.cno, mlamp, cname, pal.is_qr_scanned, pal.ack_letter ORDER BY cname", nativeQuery = true)
    List<Tuple> getPRAckLetters(@Param("dateFrom") String dateFrom, @Param("dateTo") String dateTo);
    
    @Query(value = " SELECT COALESCE(cm.other_const, '-') AS other_const, c.cno, "
            + " CASE WHEN c.cno = 998 THEN mlamp || ' - ' || cm.other_const WHEN c.cno IN (999,997,194) THEN mlamp ELSE mlamp || ' (' || cname || ')' END AS cname, cm.cmp_no, to_char(cm.esigned_date,'DD-MM-YYYY') AS esigned_date, count(1) as cheques_cnt "
            + " FROM cmrelief cm "
            + " LEFT JOIN constituency c ON c.cno = cm.recommended_by "
            + " WHERE CAST(cm.esigned_date AS DATE) >= to_date(:dateFrom,'DD-MM-YYYY') AND CAST(cm.esigned_date AS DATE) <= to_date(:dateTo,'DD-MM-YYYY') AND cm.esigned_date IS NOT NULL "
            + " AND (cmrf_loc IS NULL OR cmrf_loc = '' OR cmrf_loc = '0') "
            + " GROUP BY cm.other_const, c.cno, mlamp, cname, cm.cmp_no, cm.esigned_date ORDER BY cname, cmp_no", nativeQuery = true)
    List<Tuple> getPRCmpDetails(@Param("dateFrom") String dateFrom, @Param("dateTo") String dateTo);
    
    @Query(value = " SELECT  COALESCE(cm.other_const, '-') AS other_const, cno, cmp_no , count(1) AS cheques_cnt, to_char(cm.esigned_date,'DD-MM-YYYY') AS esigned_date, "
            + " CASE WHEN c.cno = 998 THEN mlamp || ' - ' || cm.other_const WHEN c.cno IN (999,997,194) THEN mlamp ELSE mlamp || ' (' || cname || ')' END AS cname "
            + " FROM cmrelief cm "
            + " LEFT JOIN constituency c ON c.cno = cm.recommended_by "
            + " WHERE CAST(cm.esigned_date AS DATE) >= to_date(:dateFrom,'DD-MM-YYYY') AND CAST(cm.esigned_date AS DATE) <= to_date(:dateTo,'DD-MM-YYYY') AND cm.esigned_date IS NOT NULL "
            + " AND (cmrf_loc IS NULL OR cmrf_loc = '' OR cmrf_loc = '0') "
            + " AND CAST(cm.recommended_by AS TEXT) = :cno "
            + " GROUP BY cm.other_const, c.cno, cmp_no, mlamp, cname, cm.esigned_date ORDER BY cno, cmp_no", nativeQuery = true)
    List<Tuple> getCmpDetails(@Param("dateFrom") String dateFrom, @Param("dateTo") String dateTo, @Param("cno") String cno);
    
    @Query(value = " SELECT  COALESCE(cm.other_const, '-') AS other_const, cno, cmp_no , count(1) AS cheques_cnt, to_char(cm.esigned_date,'DD-MM-YYYY') AS esigned_date, "
            + " CASE WHEN c.cno = 998 THEN mlamp || ' - ' || cm.other_const WHEN c.cno IN (999,997,194) THEN mlamp ELSE mlamp || ' (' || cname || ')' END AS cname "
            + " FROM cmrelief cm "
            + " LEFT JOIN constituency c ON c.cno = cm.recommended_by "
            + " WHERE CAST(cm.esigned_date AS DATE) >= to_date(:dateFrom,'DD-MM-YYYY') AND CAST(cm.esigned_date AS DATE) <= to_date(:dateTo,'DD-MM-YYYY') AND cm.esigned_date IS NOT NULL "
            + " AND (cmrf_loc IS NULL OR cmrf_loc = '' OR cmrf_loc = '0') "
            + " AND CAST(cm.recommended_by AS TEXT) = :cno AND cm.other_const = :otherConst "
            + " GROUP BY cm.other_const, c.cno, cmp_no, mlamp, cname, cm.esigned_date ORDER BY cno, cmp_no", nativeQuery = true)
    List<Tuple> getCmpDetailsForOtherConst(@Param("dateFrom") String dateFrom, @Param("dateTo") String dateTo, @Param("cno") String cno, @Param("otherConst") String otherConst);
    
    @Query(value = " SELECT TO_CHAR(pal.esigned_date,'DD-MM-YYYY') FROM pr_acknowledgement_letters pal WHERE pal.cno = :cno "
            + " AND cast(pal.esigned_date AS date) >= to_date(:dateFrom,'YYYY-MM-DD') "
            + " AND cast(pal.esigned_date AS date) <= to_date(:dateTo,'YYYY-MM-DD') AND pal.is_qr_scanned IS TRUE ", nativeQuery = true)
    String getAckDetailsForUpload(@Param("dateFrom") String dateFrom, @Param("dateTo") String dateTo, @Param("cno") Integer cno);
    
    @Query(value = " SELECT TO_CHAR(pal.esigned_date,'DD-MM-YYYY') FROM pr_acknowledgement_letters pal WHERE pal.cno = :cno "
            + " AND pal.other_const = :otherConst AND cast(pal.esigned_date AS date) >= to_date(:dateFrom,'YYYY-MM-DD') "
            + " AND cast(pal.esigned_date AS date) <= to_date(:dateTo,'YYYY-MM-DD') AND pal.is_qr_scanned IS TRUE ", nativeQuery = true)
    String getAckDetailsForUploadWithOtherConst(@Param("dateFrom") String dateFrom, @Param("dateTo") String dateTo, @Param("cno") Integer cno, @Param("otherConst") String otherConst);
    
    @Modifying
    @Transactional
    @Query(value = " UPDATE pr_acknowledgement_letters pal SET ack_letter = :ackLetterPath, pa_name = :paName, "
            + " pa_mobile_no = :paMobileNo, entered_by = :enteredBy, entered_ip = :enteredIp, entered_on = CURRENT_TIMESTAMP "
            + " WHERE pal.cno = :cno "
            + " AND cast(pal.esigned_date AS date) = to_date(:esignedDate,'DD-MM-YYYY') "
            + " AND pal.is_qr_scanned IS TRUE ", nativeQuery = true)
    Integer updateAckDetailsForUpload(@Param("ackLetterPath") String ackLetterPath, @Param("paName") String paName, @Param("paMobileNo") String paMobileNo, @Param("enteredBy") String enteredBy, @Param("enteredIp") String enteredIp, @Param("cno") Integer cno, @Param("esignedDate") String esignedDate);
    
    @Modifying
    @Transactional
    @Query(value = " UPDATE pr_acknowledgement_letters pal SET ack_letter = :ackLetterPath, pa_name = :paName, "
            + " pa_mobile_no = :paMobileNo, entered_by = :enteredBy, entered_ip = :enteredIp, entered_on = CURRENT_TIMESTAMP "
            + " WHERE pal.cno = :cno AND pal.other_const = :otherConst "
            + " AND cast(pal.esigned_date AS date) = to_date(:esignedDate,'DD-MM-YYYY') "
            + " AND pal.is_qr_scanned IS TRUE ", nativeQuery = true)
    Integer updateAckDetailsForUploadWithOtherConst(@Param("ackLetterPath") String ackLetterPath, @Param("paName") String paName, @Param("paMobileNo") String paMobileNo, @Param("enteredBy") String enteredBy, @Param("enteredIp") String enteredIp, @Param("cno") Integer cno, @Param("otherConst") String otherConst, @Param("esignedDate") String esignedDate);

}

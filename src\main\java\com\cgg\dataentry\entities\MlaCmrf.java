package com.cgg.dataentry.entities;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "mla_cmrf")
public class MlaCmrf {

    @Id
    @Column(name = "mla_cmrf_no")
    private String mlaCmrfNo;
    
    @Column(name = "doc_verif_amt")
    private Integer docVerifAmt;
    
    @Column(name = "income_no")
    private String incomeNo;

    @Column(name = "patient_name")
    private String patientName;

    @Column(name = "time_stamp")
    private Date timeStamp;

    @Column(name = "user_id")
    private String userId;

    @Column(name = "recommended_by")
    private String recommendedBy;

    @Column(name = "status")
    private String status;

	@Column(name = "aadhar_no")
    private String aadharNo;

    @Column(name = "father_son_of")
    private String fatherSonOf;

    @Column(name = "cmrf_no")
    private String cmrfNo;

    @Column(name = "cmrf_priority")
    private String cmrfPriority;

    @Column(name = "entered_on")
    private LocalDateTime enteredOn;

    @Column(name = "delete_flag")
    private boolean deleteFlag ;

    @Column(name = "updated_on")
    private LocalDateTime updatedOn;

    @Column(name = "patient_ip")
    private String patientIp;

    @Column(name = "hosp_code")
    private Integer hospCode;

    @Column(name = "patient_ip_status")
    private String patientIpStatus;

    @Column(name = "rej_reasons")
    private String rejReasons;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "verified_by_deo")
    private String verifiedByDeo;

    @Column(name = "deo_verified_date")
    private LocalDateTime deoVerifiedDate;

    @Column(name = "deo_rej_reasons")
    private String deoRejReasons;

    @Column(name = "admission_no")
    private String admissionNo;

    @Column(name = "hos_verified_date")
    private LocalDateTime hosVerifiedDate;

    @Column(name = "patient_ip_upd_date")
    private LocalDateTime patientIpUpdDate;

    @Column(name = "pat_address")
    private String patAddress;

    @Column(name = "pat_district")
    private Integer patDistrict;

    @Column(name = "pat_mandal")
    private Integer patMandal;

    @Column(name = "mobile_no")
    private Long mobileNo;

    @Column(name = "age")
    private String age;

    @Column(name = "purpose")
    private String purpose;

    @Column(name = "pincode")
    private Integer pincode;

    @Column(name = "ip_address")
    private String ipAddress;

    @Column(name = "patient_ip_updated_by")
    private String patientIpUpdatedBy;

    @Column(name = "deo_verified_by")
    private String deoVerifiedBy;

    @Column(name = "old_fsc_no")
    private String oldFscNo;

    @Column(name = "new_fsc_no")
    private String newFscNo;

    @Column(name = "gender")
    private String gender;

    @Column(name = "upload_path")
    private String uploadPath;

    @Column(name = "hard_copy_received")
    private Boolean hardCopyReceived;
    
    @Column(name="bank_dist")
    private String bankDist;
    
    @Column(name = "bank_name")
    private String bankName;
    
    @Column(name = "bank_acc_no")
    private String bankAccNo;
    
    @Column(name="bank_ifsc")
    private String bankIfsc;
    
    @Column(name = "bank_branch")
    private String bankBranch;
    
    @Column(name = "bank_acc_holder_name")
    private String bankAccHolderName;
    
    @Column(name = "hosp_bills_copy")
    private Boolean hospBillsCopy;

    @Column(name = "inc_cer_copy")
    private Boolean incCerCopy;

    @Column(name = "fsc_copy")
    private Boolean fscCopy;

    @Column(name = "mla_letter_copy")
    private Boolean mlaLetterCopy;

    @Column(name = "bank_pass_copy")
    private Boolean bankPassCopy;

    @Column(name = "aadhar_copy")
    private Boolean aadharCopy;
    
    @Column(name = "deo_upd_by")
    private String deoUpdBy;
    
	@Column(name = "deo_upd_dt")
    private LocalDateTime deoUpdDt;

    @Column(name = "deo_upd_ipaddr")
    private String deoUpdIpaddr;
    
    @Column(name = "deo_upd_remarks")
    private String remarks;

    @Column(name = "pat_village")
    private Integer patVillage;
    
    @Column(name = "deo_upd_reasons")
    private String reason;
    
    @Column(name = "hosp_pend_res")
    private String hospPendRes;
    
    @Column(name = "pending_reasons")
    private String pendingReasons;
    
    @Column(name = "hosp_ver_by")
    private String hospVerBy;
    
    @Column(name = "hosp_ver_ipaddr")
    private String hospVerIpaddr;
    
    @Column(name = "cmrf_ent_by")
    private String cmrfEntBy;
    
    @Column(name = "cmrf_ent_dt")
    private LocalDateTime cmrfEntDt;
    
    @Column(name = "cmrf_ent_ipaddr")
    private String cmrfEntIpaddr;
    
    @Column(name = "cheque_dt")
    private LocalDateTime chequeDt;
    
    @Column(name = "token_hash")
    private String tokenHash;
    
    @Column(name = "bank_passbook")
    private Boolean bankPassbook;
    
    @Column(name = "fsc_ration_card")
    private Boolean fscRationCard;
    
    @Column(name = "hospital_bills")
    private Boolean hospitalBills;
    
    @Column(name = "income_certificate")
    private Boolean incomeCertificate;
    
    @Column(name = "deo_upd_others_remarks")
    private String deoUpdOthersRemarks;
    
    @Column(name = "hosp_bill_amt")
    private BigDecimal hospBillAmt;
    
    @Column(name = "treat_par_id")
    private Integer treatParId;
    
    @Column(name = "treat_sub_id")
    private Integer treatSubId;
    
    @Column(name = "treat_proc_id")
    private Integer treatProcId;
    
    @Column(name = "status_upd_res")
    private String statusUpdRes;
    
    @Column(name = "is_qr_scanned")
    private Boolean isQrScanned;
    
    @Column(name = "qr_scanned_by")
    private String qrScannedBy;
    
    @Column(name = "qr_scanned_timestamp")
    private LocalDateTime qrScannedTimestamp;
    
    @Column(name = "deo_pen_reasons")
    private String deoPenReasons;
    
    @Column(name = "is_special_flg")
    private boolean isSpecialFlag;
    
    @Column(name = "special_flg_updated_on")
    private LocalDateTime specialFlgUpdatedOn;
    
    @Column(name = "special_flg_updated_by")
    private String specialFlgUpdatedBy;
    
    @Column(name = "special_flg_ref_by")
    private String specialFlgReferredBy;

	@Column(name = "batch_serial_no")
	private String batchSerialNo;

	@Column(name = "batch_name")
	private String batchName;

    @Column(name = "bill_amt_upd_by")
    private String billAmtUpdBy;

	public String getReason() {
		return reason;
	}
	public void setReason(String reason) {
		this.reason = reason;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	public String getMlaCmrfNo() {
		return mlaCmrfNo;
	}
	public void setMlaCmrfNo(String mlaCmrfNo) {
		this.mlaCmrfNo = mlaCmrfNo;
	}
	public String getIncomeNo() {
		return incomeNo;
	}
	public void setIncomeNo(String incomeNo) {
		this.incomeNo = incomeNo;
	}
	public String getPatientName() {
		return patientName;
	}
	public void setPatientName(String patientName) {
		this.patientName = patientName;
	}
	public Date getTimeStamp() {
		return timeStamp;
	}
	public void setTimeStamp(Date timeStamp) {
		this.timeStamp = timeStamp;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getRecommendedBy() {
		return recommendedBy;
	}
	public void setRecommendedBy(String recommendedBy) {
		this.recommendedBy = recommendedBy;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getAadharNo() {
		return aadharNo;
	}
	public void setAadharNo(String aadharNo) {
		this.aadharNo = aadharNo;
	}
	public String getFatherSonOf() {
		return fatherSonOf;
	}
	public void setFatherSonOf(String fatherSonOf) {
		this.fatherSonOf = fatherSonOf;
	}
	public String getCmrfNo() {
		return cmrfNo;
	}
	public void setCmrfNo(String cmrfNo) {
		this.cmrfNo = cmrfNo;
	}
	public String getCmrfPriority() {
		return cmrfPriority;
	}
	public void setCmrfPriority(String cmrfPriority) {
		this.cmrfPriority = cmrfPriority;
	}
	public LocalDateTime getEnteredOn() {
		return enteredOn;
	}
	public void setEnteredOn(LocalDateTime enteredOn) {
		this.enteredOn = enteredOn;
	}
	public boolean isDeleteFlag() {
		return deleteFlag;
	}
	public void setDeleteFlag(boolean deleteFlag) {
		this.deleteFlag = deleteFlag;
	}
	public LocalDateTime getUpdatedOn() {
		return updatedOn;
	}
	public void setUpdatedOn(LocalDateTime updatedOn) {
		this.updatedOn = updatedOn;
	}
	public String getPatientIp() {
		return patientIp;
	}
	public void setPatientIp(String patientIp) {
		this.patientIp = patientIp;
	}
	public Integer getHospCode() {
		return hospCode;
	}
	public void setHospCode(Integer hospCode) {
		this.hospCode = hospCode;
	}
	public String getPatientIpStatus() {
		return patientIpStatus;
	}
	public void setPatientIpStatus(String patientIpStatus) {
		this.patientIpStatus = patientIpStatus;
	}
	public String getRejReasons() {
		return rejReasons;
	}
	public void setRejReasons(String rejReasons) {
		this.rejReasons = rejReasons;
	}
	public String getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	public String getVerifiedByDeo() {
		return verifiedByDeo;
	}
	public void setVerifiedByDeo(String verifiedByDeo) {
		this.verifiedByDeo = verifiedByDeo;
	}
	public LocalDateTime getDeoVerifiedDate() {
		return deoVerifiedDate;
	}
	public void setDeoVerifiedDate(LocalDateTime deoVerifiedDate) {
		this.deoVerifiedDate = deoVerifiedDate;
	}
	public String getDeoRejReasons() {
		return deoRejReasons;
	}
	public void setDeoRejReasons(String deoRejReasons) {
		this.deoRejReasons = deoRejReasons;
	}
	public String getAdmissionNo() {
		return admissionNo;
	}
	public void setAdmissionNo(String admissionNo) {
		this.admissionNo = admissionNo;
	}
	public LocalDateTime getHosVerifiedDate() {
		return hosVerifiedDate;
	}
	public void setHosVerifiedDate(LocalDateTime hosVerifiedDate) {
		this.hosVerifiedDate = hosVerifiedDate;
	}
	public LocalDateTime getPatientIpUpdDate() {
		return patientIpUpdDate;
	}
	public void setPatientIpUpdDate(LocalDateTime patientIpUpdDate) {
		this.patientIpUpdDate = patientIpUpdDate;
	}
	public String getPatAddress() {
		return patAddress;
	}
	public void setPatAddress(String patAddress) {
		this.patAddress = patAddress;
	}
	public Integer getPatDistrict() {
		return patDistrict;
	}
	public void setPatDistrict(Integer patDistrict) {
		this.patDistrict = patDistrict;
	}
	public Integer getPatMandal() {
		return patMandal;
	}
	public void setPatMandal(Integer patMandal) {
		this.patMandal = patMandal;
	}
	public Long getMobileNo() {
		return mobileNo;
	}
	public void setMobileNo(Long mobileNo) {
		this.mobileNo = mobileNo;
	}
	public String getAge() {
		return age;
	}
	public void setAge(String age) {
		this.age = age;
	}
	public String getPurpose() {
		return purpose;
	}
	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}
	public Integer getPincode() {
		return pincode;
	}
	public void setPincode(Integer pincode) {
		this.pincode = pincode;
	}
	public String getIpAddress() {
		return ipAddress;
	}
	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
	public String getPatientIpUpdatedBy() {
		return patientIpUpdatedBy;
	}
	public void setPatientIpUpdatedBy(String patientIpUpdatedBy) {
		this.patientIpUpdatedBy = patientIpUpdatedBy;
	}
	public String getDeoVerifiedBy() {
		return deoVerifiedBy;
	}
	public void setDeoVerifiedBy(String deoVerifiedBy) {
		this.deoVerifiedBy = deoVerifiedBy;
	}
	public String getOldFscNo() {
		return oldFscNo;
	}
	public void setOldFscNo(String oldFscNo) {
		this.oldFscNo = oldFscNo;
	}
	public String getNewFscNo() {
		return newFscNo;
	}
	public void setNewFscNo(String newFscNo) {
		this.newFscNo = newFscNo;
	}
	public String getGender() {
		return gender;
	}
	public void setGender(String gender) {
		this.gender = gender;
	}
	public String getUploadPath() {
		return uploadPath;
	}
	public void setUploadPath(String uploadPath) {
		this.uploadPath = uploadPath;
	}
	
	public Boolean getHardCopyReceived() {
		return hardCopyReceived;
	}
	public void setHardCopyReceived(Boolean hardCopyReceived) {
		this.hardCopyReceived = hardCopyReceived;
	}
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	public String getBankAccNo() {
		return bankAccNo;
	}
	public void setBankAccNo(String bankAccNo) {
		this.bankAccNo = bankAccNo;
	}
	public String getBankIfsc() {
		return bankIfsc;
	}
	public void setBankIfsc(String bankIfsc) {
		this.bankIfsc = bankIfsc;
	}
	public String getBankBranch() {
		return bankBranch;
	}
	public void setBankBranch(String bankBranch) {
		this.bankBranch = bankBranch;
	}
	public Boolean getHospBillsCopy() {
		return hospBillsCopy;
	}
	public void setHospBillsCopy(Boolean hospBillsCopy) {
		this.hospBillsCopy = hospBillsCopy;
	}
	public Boolean getIncCerCopy() {
		return incCerCopy;
	}
	public void setIncCerCopy(Boolean incCerCopy) {
		this.incCerCopy = incCerCopy;
	}
	public Boolean getFscCopy() {
		return fscCopy;
	}
	public void setFscCopy(Boolean fscCopy) {
		this.fscCopy = fscCopy;
	}
	public Boolean getMlaLetterCopy() {
		return mlaLetterCopy;
	}
	public void setMlaLetterCopy(Boolean mlaLetterCopy) {
		this.mlaLetterCopy = mlaLetterCopy;
	}
	public Boolean getBankPassCopy() {
		return bankPassCopy;
	}
	public void setBankPassCopy(Boolean bankPassCopy) {
		this.bankPassCopy = bankPassCopy;
	}
	public Boolean getAadharCopy() {
		return aadharCopy;
	}
	public void setAadharCopy(Boolean aadharCopy) {
		this.aadharCopy = aadharCopy;
	}
	public String getDeoUpdBy() {
		return deoUpdBy;
	}
	public void setDeoUpdBy(String deoUpdBy) {
		this.deoUpdBy = deoUpdBy;
	}
	public LocalDateTime getDeoUpdDt() {
		return deoUpdDt;
	}
	public void setDeoUpdDt(LocalDateTime deoUpdDt) {
		this.deoUpdDt = deoUpdDt;
	}
	public String getDeoUpdIpaddr() {
		return deoUpdIpaddr;
	}
	public void setDeoUpdIpaddr(String deoUpdIpaddr) {
		this.deoUpdIpaddr = deoUpdIpaddr;
	}
	public String getBankDist() {
		return bankDist;
	}
	public void setBankDist(String bankDist) {
		this.bankDist = bankDist;
	}
	public String getHospPendRes() {
		return hospPendRes;
	}
	public void setHospPendRes(String hospPendRes) {
		this.hospPendRes = hospPendRes;
	}
	public String getDeoPenReasons() {
		return deoPenReasons;
	}
	public void setDeoPenReasons(String deoPenReasons) {
		this.deoPenReasons = deoPenReasons;
	}
	public Integer getDocVerifAmt() {
		return docVerifAmt;
	}
	public void setDocVerifAmt(Integer docVerifAmt) {
		this.docVerifAmt = docVerifAmt;
	}
	public String getBankAccHolderName() {
		return bankAccHolderName;
	}
	public void setBankAccHolderName(String bankAccHolderName) {
		this.bankAccHolderName = bankAccHolderName;
	}
    public Integer getPatVillage() {
		return patVillage;
	}
	public void setPatVillage(Integer patVillage) {
		this.patVillage = patVillage;
	}
	public String getPendingReasons() {
		return pendingReasons;
	}
	public void setPendingReasons(String pendingReasons) {
		this.pendingReasons = pendingReasons;
	}
	public String getHospVerBy() {
		return hospVerBy;
	}
	public void setHospVerBy(String hospVerBy) {
		this.hospVerBy = hospVerBy;
	}
	public String getHospVerIpaddr() {
		return hospVerIpaddr;
	}
	public void setHospVerIpaddr(String hospVerIpaddr) {
		this.hospVerIpaddr = hospVerIpaddr;
	}
	public String getCmrfEntBy() {
		return cmrfEntBy;
	}
	public void setCmrfEntBy(String cmrfEntBy) {
		this.cmrfEntBy = cmrfEntBy;
	}
	public LocalDateTime getCmrfEntDt() {
		return cmrfEntDt;
	}
	public void setCmrfEntDt(LocalDateTime cmrfEntDt) {
		this.cmrfEntDt = cmrfEntDt;
	}
	public String getCmrfEntIpaddr() {
		return cmrfEntIpaddr;
	}
	public void setCmrfEntIpaddr(String cmrfEntIpaddr) {
		this.cmrfEntIpaddr = cmrfEntIpaddr;
	}
	public LocalDateTime getChequeDt() {
		return chequeDt;
	}
	public void setChequeDt(LocalDateTime chequeDt) {
		this.chequeDt = chequeDt;
	}
	public String getTokenHash() {
		return tokenHash;
	}
	public void setTokenHash(String tokenHash) {
		this.tokenHash = tokenHash;
	}
	public Boolean getBankPassbook() {
		return bankPassbook;
	}
	public void setBankPassbook(Boolean bankPassbook) {
		this.bankPassbook = bankPassbook;
	}
	public Boolean getFscRationCard() {
		return fscRationCard;
	}
	public void setFscRationCard(Boolean fscRationCard) {
		this.fscRationCard = fscRationCard;
	}
	public Boolean getHospitalBills() {
		return hospitalBills;
	}
	public void setHospitalBills(Boolean hospitalBills) {
		this.hospitalBills = hospitalBills;
	}
	public Boolean getIncomeCertificate() {
		return incomeCertificate;
	}
	public void setIncomeCertificate(Boolean incomeCertificate) {
		this.incomeCertificate = incomeCertificate;
	}
	public String getDeoUpdOthersRemarks() {
		return deoUpdOthersRemarks;
	}
	public void setDeoUpdOthersRemarks(String deoUpdOthersRemarks) {
		this.deoUpdOthersRemarks = deoUpdOthersRemarks;
	}
	public BigDecimal getHospBillAmt() {
		return hospBillAmt;
	}
	public void setHospBillAmt(BigDecimal hospBillAmt) {
		this.hospBillAmt = hospBillAmt;
	}
	public Integer getTreatParId() {
		return treatParId;
	}
	public void setTreatParId(Integer treatParId) {
		this.treatParId = treatParId;
	}
	public Integer getTreatSubId() {
		return treatSubId;
	}
	public void setTreatSubId(Integer treatSubId) {
		this.treatSubId = treatSubId;
	}
	public Integer getTreatProcId() {
		return treatProcId;
	}
	public void setTreatProcId(Integer treatProcId) {
		this.treatProcId = treatProcId;
	}
	public String getStatusUpdRes() {
		return statusUpdRes;
	}
	public void setStatusUpdRes(String statusUpdRes) {
		this.statusUpdRes = statusUpdRes;
	}
	public Boolean getIsQrScanned() {
		return isQrScanned;
	}
	public void setIsQrScanned(Boolean isQrScanned) {
		this.isQrScanned = isQrScanned;
	}
	public String getQrScannedBy() {
		return qrScannedBy;
	}
	public void setQrScannedBy(String qrScannedBy) {
		this.qrScannedBy = qrScannedBy;
	}
	public LocalDateTime getQrScannedTimestamp() {
		return qrScannedTimestamp;
	}
	public void setQrScannedTimestamp(LocalDateTime qrScannedTimestamp) {
		this.qrScannedTimestamp = qrScannedTimestamp;
	}

	
	public boolean isSpecialFlag() {
		return isSpecialFlag;
	}
	public void setSpecialFlag(boolean isSpecialFlag) {
		this.isSpecialFlag = isSpecialFlag;
	}
	public LocalDateTime getSpecialFlgUpdatedOn() {
		return specialFlgUpdatedOn;
	}
	public void setSpecialFlgUpdatedOn(LocalDateTime specialFlgUpdatedOn) {
		this.specialFlgUpdatedOn = specialFlgUpdatedOn;
	}
	public String getSpecialFlgUpdatedBy() {
		return specialFlgUpdatedBy;
	}
	public void setSpecialFlgUpdatedBy(String specialFlgUpdatedBy) {
		this.specialFlgUpdatedBy = specialFlgUpdatedBy;
	}
	
	public String getSpecialFlgReferredBy() {
		return specialFlgReferredBy;
	}
	public void setSpecialFlgReferredBy(String specialFlgReferredBy) {
		this.specialFlgReferredBy = specialFlgReferredBy;
	}
	public String getBatchSerialNo() {
		return batchSerialNo;
	}
	public void setBatchSerialNo(String batchSerialNo) {
		this.batchSerialNo = batchSerialNo;
	}
	public String getBatchName() {
		return batchName;
	}
	public void setBatchName(String batchName) {
		this.batchName = batchName;
	}
	public String getBillAmtUpdBy() {
		return billAmtUpdBy;
	}
	public void setBillAmtUpdBy(String billAmtUpdBy) {
		this.billAmtUpdBy = billAmtUpdBy;
	}
	@Override
	public String toString() {
		return "MlaCmrf [mlaCmrfNo=" + mlaCmrfNo + ", docVerifAmt=" + docVerifAmt + ", incomeNo=" + incomeNo
				+ ", patientName=" + patientName + ", timeStamp=" + timeStamp + ", userId=" + userId
				+ ", recommendedBy=" + recommendedBy + ", status=" + status + ", aadharNo=" + aadharNo
				+ ", fatherSonOf=" + fatherSonOf + ", cmrfNo=" + cmrfNo + ", cmrfPriority=" + cmrfPriority
				+ ", enteredOn=" + enteredOn + ", deleteFlag=" + deleteFlag + ", updatedOn=" + updatedOn
				+ ", patientIp=" + patientIp + ", hospCode=" + hospCode + ", patientIpStatus=" + patientIpStatus
				+ ", rejReasons=" + rejReasons + ", updatedBy=" + updatedBy + ", verifiedByDeo=" + verifiedByDeo
				+ ", deoVerifiedDate=" + deoVerifiedDate + ", deoRejReasons=" + deoRejReasons + ", admissionNo="
				+ admissionNo + ", hosVerifiedDate=" + hosVerifiedDate + ", patientIpUpdDate=" + patientIpUpdDate
				+ ", patAddress=" + patAddress + ", patDistrict=" + patDistrict + ", patMandal=" + patMandal
				+ ", mobileNo=" + mobileNo + ", age=" + age + ", purpose=" + purpose + ", pincode=" + pincode
				+ ", ipAddress=" + ipAddress + ", patientIpUpdatedBy=" + patientIpUpdatedBy + ", deoVerifiedBy="
				+ deoVerifiedBy + ", oldFscNo=" + oldFscNo + ", newFscNo=" + newFscNo + ", gender=" + gender
				+ ", uploadPath=" + uploadPath + ", hardCopyReceived=" + hardCopyReceived + ", bankDist=" + bankDist
				+ ", bankName=" + bankName + ", bankAccNo=" + bankAccNo + ", bankIfsc=" + bankIfsc + ", bankBranch="
				+ bankBranch + ", bankAccHolderName=" + bankAccHolderName + ", hospBillsCopy=" + hospBillsCopy
				+ ", incCerCopy=" + incCerCopy + ", fscCopy=" + fscCopy + ", mlaLetterCopy=" + mlaLetterCopy
				+ ", bankPassCopy=" + bankPassCopy + ", aadharCopy=" + aadharCopy + ", deoUpdBy=" + deoUpdBy
				+ ", deoUpdDt=" + deoUpdDt + ", deoUpdIpaddr=" + deoUpdIpaddr + ", remarks=" + remarks + ", patVillage="
				+ patVillage + ", reason=" + reason + ", hospPendRes=" + hospPendRes + ", pendingReasons="
				+ pendingReasons + ", hospVerBy=" + hospVerBy + ", hospVerIpaddr=" + hospVerIpaddr + ", cmrfEntBy="
				+ cmrfEntBy + ", cmrfEntDt=" + cmrfEntDt + ", cmrfEntIpaddr=" + cmrfEntIpaddr + ", chequeDt=" + chequeDt
				+ ", tokenHash=" + tokenHash + ", bankPassbook=" + bankPassbook + ", fscRationCard=" + fscRationCard
				+ ", hospitalBills=" + hospitalBills + ", incomeCertificate=" + incomeCertificate
				+ ", deoUpdOthersRemarks=" + deoUpdOthersRemarks + ", hospBillAmt=" + hospBillAmt + ", treatParId="
				+ treatParId + ", treatSubId=" + treatSubId + ", treatProcId=" + treatProcId + ", statusUpdRes="
				+ statusUpdRes + ", isQrScanned=" + isQrScanned + ", qrScannedBy=" + qrScannedBy
				+ ", qrScannedTimestamp=" + qrScannedTimestamp + ", deoPenReasons=" + deoPenReasons + ", isSpecialFlag="
				+ isSpecialFlag + ", specialFlgUpdatedOn=" + specialFlgUpdatedOn + ", specialFlgUpdatedBy="
				+ specialFlgUpdatedBy + ", specialFlgReferredBy=" + specialFlgReferredBy + ", batchSerialNo="
				+ batchSerialNo + ", batchName=" + batchName + ", billAmtUpdBy=" + billAmtUpdBy + "]";
	}
	
    
}


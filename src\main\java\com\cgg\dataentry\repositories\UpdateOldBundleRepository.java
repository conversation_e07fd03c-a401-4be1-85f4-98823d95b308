package com.cgg.dataentry.repositories;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.cgg.dataentry.entities.MlaCmrf;
import com.cgg.dataentry.model.MlaCmrfEntityModel;


@Repository
public interface UpdateOldBundleRepository extends JpaRepository<MlaCmrf, String> {
	
	
	@Query("SELECT new com.cgg.dataentry.model.MlaCmrfEntityModel(m.mlaCmrfNo, m.cmrfNo, m.patientName, m.fatherSonOf,  m.patAddress,m.purpose, h.hospname, c.cname,c.mlamp, m.isSpecialFlag, m.status, m.batchSerialNo ) " 
		     + " FROM MlaCmrf m " 
		     + " INNER JOIN Hospital h ON m.hospCode = h.hospcode " 
		     + " INNER JOIN Constituency c ON c.cno =CAST(m.recommendedBy AS integer) "
		     + " WHERE m.mlaCmrfNo = :mlaCmrfNo AND m.status != '11'")
	MlaCmrfEntityModel getOldTokenDetails(@Param("mlaCmrfNo") String mlaCmrfNo);

	  @Modifying
      @Transactional
      @Query(value="update mla_cmrf set  batch_serial_no = :batchSerialNumber, batch_name = :batchName,batch_updated_by=:userId,batch_updated_on=now() where mla_cmrf_no = :tokenNo",nativeQuery = true)
      Integer updateBatchDetails(@Param("tokenNo") String tokenNo, @Param("userId") String userId, @Param("batchSerialNumber") String batchSerialNumber, @Param("batchName") String batchName);

	  
	  @Query(value="SELECT status_name FROM status_mst WHERE status_id=:statusNo",nativeQuery = true)
		String getStatusName(@Param("statusNo") int statusNo);
	  
}

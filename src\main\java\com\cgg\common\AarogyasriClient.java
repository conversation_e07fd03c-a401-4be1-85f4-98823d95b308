package com.cgg.common;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import com.cgg.hospital.model.PatientDetail;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.List;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@Component
public class AarogyasriClient {

    private final RestTemplate restTemplate;
//    private final String baseUrl = "https://pp.aarogyasri.telangana.gov.in/asri/cmrf";
//    private String userName="cmrf";
//    private String password="trust";
    
    
    
    private final String baseUrl = "https://asri-apis.rajivaarogyasri.telangana.gov.in/asri/cmrf";
    private String userName="cmrf";
    private String password="Q#eds@43Ws";
    
    private String token;
    public AarogyasriClient() {
        this.restTemplate = new RestTemplate();
    }

    public ApiResponse getToken() {
    	try {
        String url = baseUrl + "/getToken";
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String jsonBody = "{\"userName\":\"" + userName + "\", \"password\":\"" + password + "\"}";
        HttpEntity<String> entity = new HttpEntity<>(jsonBody, headers);

        ResponseEntity<TokenResponse> response = restTemplate.postForEntity(url, entity, TokenResponse.class);
        return parseResponse(response);
	  } catch (HttpClientErrorException ex) {
          return handleClientError(ex);
      } catch (HttpServerErrorException ex) {
          return handleServerError(ex);
      } catch (Exception e) {
          return handleGeneralError(e);
      }
    }

//    public ApiResponse getPatientDetailsByFscId(String fscId) {
//        String url = baseUrl + "/patientDetails?fscId=" + fscId;
//        HttpHeaders headers = new HttpHeaders();
//        headers.add("Authorization", "Bearer " + this.token);
//        HttpEntity<String> entity = new HttpEntity<>(headers);
//
//        ResponseEntity<PatientDetailsResponse> response = restTemplate.exchange(url, HttpMethod.GET, entity, PatientDetailsResponse.class);
//        return parseResponse(response);
//    }
    public ApiResponse getPatientDetailsByFscId(String fscId,String token) {
        try {
            String url = baseUrl + "/patientDetails?fscId=" + fscId;
            HttpHeaders headers = new HttpHeaders();
            headers.add("Authorization", "Bearer " + token);
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<PatientDetailsResponse> response = restTemplate.exchange(url, HttpMethod.GET, entity, PatientDetailsResponse.class);
            return parseResponse(response);
        } catch (HttpClientErrorException ex) {
            return handleClientError(ex);
        } catch (HttpServerErrorException ex) {
            return handleServerError(ex);
        } catch (Exception e) {
            return handleGeneralError(e);
        }
    }

    public ApiResponse getPatientDetailsByAadharId(String aadharId,String token) {
    	 try {
        String url = baseUrl + "/patientDetails?aadharId=" + aadharId;
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Bearer " + token);
        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<PatientDetailsResponse> response = restTemplate.exchange(url, HttpMethod.GET, entity, PatientDetailsResponse.class);
        return parseResponse(response);
    	 } catch (HttpClientErrorException ex) {
             return handleClientError(ex);
         } catch (HttpServerErrorException ex) {
             return handleServerError(ex);
         } catch (Exception e) {
             return handleGeneralError(e);
         }
    }

    private ApiResponse parseResponse(ResponseEntity<? extends ApiResponse> response) {
        if (response.getStatusCode() == HttpStatus.OK) {
            return response.getBody();
        } else {
            // Handle different HTTP statuses or log errors
            ApiResponse errorResponse = new ApiResponse();
            errorResponse.setStatusCode(response.getStatusCodeValue());
            if (response.hasBody()) {
                ApiResponse body = response.getBody();
                errorResponse.setMessage(body.getMessage());
                errorResponse.setResponseObject(body.getResponseObject());
            } else {
                errorResponse.setMessage("Unknown error occurred");
            }
            return errorResponse;
        }
    }

    public void performApiOperations() {
        ApiResponse tokenResponse = getToken();
        System.out.println("Token Response: " + tokenResponse.toString());

        if (tokenResponse.getStatusCode() == 200) {
            this.token = tokenResponse.getMessage(); // Assuming the token is in the message field

            ApiResponse fscResponse = getPatientDetailsByFscId("************",this.token);
            System.out.println("FSC Response: " + fscResponse.toString());

            ApiResponse aadharResponse = getPatientDetailsByAadharId("************",this.token);
            System.out.println("Aadhar Response: " + aadharResponse.toString());
        } else {
            System.out.println("Failed to retrieve token. Status: " + tokenResponse.getStatusCode());
        }
    }

    // Define the ApiResponse, TokenResponse, PatientDetailsResponse classes inside or as static nested classes if preferred
    public static class ApiResponse {
        private int statusCode;
        private String message;
        private Object responseObject;
        private String timestamp;
		public ApiResponse(int value, String string, Object object) {
			// TODO Auto-generated constructor stub
		}
		public ApiResponse() {
			// TODO Auto-generated constructor stub
		}
		public int getStatusCode() {
			return statusCode;
		}
		public void setStatusCode(int statusCode) {
			this.statusCode = statusCode;
		}
		public String getMessage() {
			return message;
		}
		public void setMessage(String message) {
			this.message = message;
		}
		public Object getResponseObject() {
			return responseObject;
		}
		public void setResponseObject(Object responseObject) {
			this.responseObject = responseObject;
		}
		public String getTimestamp() {
			return timestamp;
		}
		public void setTimestamp(String timestamp) {
			this.timestamp = timestamp;
		}
		@Override
		public String toString() {
			return "ApiResponse [statusCode=" + statusCode + ", message=" + message + ", responseObject="
					+ responseObject + ", timestamp=" + timestamp + "]";
		}
        
        
        // Standard getters, setters, and toString()
    }

    public static class TokenResponse extends ApiResponse {
        // Additional fields if necessary
    }

    public static  class PatientDetailsResponse extends ApiResponse {
        private List<PatientDetail> patientDetails;

        public List<PatientDetail> getPatientDetails() {
            return patientDetails;
        }

        public void setPatientDetails(List<PatientDetail> patientDetails) {
            this.patientDetails = patientDetails;
        }
    }

    
    
    
    private ApiResponse handleClientError(HttpClientErrorException ex) {
        // Handle client-side HTTP errors (4xx)
        System.err.println("Client error occurred: " + ex.getMessage());
        return new ApiResponse(HttpStatus.BAD_REQUEST.value(), "Client error: " + ex.getResponseBodyAsString(), null);
    }

    private ApiResponse handleServerError(HttpServerErrorException ex) {
        // Handle server-side HTTP errors (5xx)
        System.err.println("Server error occurred: " + ex.getMessage());
        ApiResponse errorResponse = new ApiResponse();
        errorResponse.setStatusCode(ex.getStatusCode().value());
        try {
            ApiResponse apiResponse = new ObjectMapper().readValue(ex.getResponseBodyAsString(), ApiResponse.class);
            errorResponse.setMessage(apiResponse.getMessage());
            errorResponse.setResponseObject(apiResponse.getResponseObject());
        } catch (IOException e) {
            errorResponse.setMessage("Error parsing error response: " + e.getMessage());
        }
        return errorResponse;
    }

    private ApiResponse handleGeneralError(Exception e) {
        // Handle unexpected errors
        System.err.println("An unexpected error occurred: " + e.getMessage());
        return new ApiResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), "An unexpected error occurred: " + e.getMessage(), null);
    }
}



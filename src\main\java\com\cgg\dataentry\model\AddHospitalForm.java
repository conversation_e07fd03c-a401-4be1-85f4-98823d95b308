package com.cgg.dataentry.model;

public class AddHospitalForm {
	
	private String hospitalName;
	private String hospitalOnCheque;
	private String location;
	private String recognition;
	private String online;
	private String distNo,distName;
	private String mobileNo;
	private String enteredBy;

	public String getDistNo() {
		return distNo;
	}
	public void setDistNo(String distNo) {
		this.distNo = distNo;
	}
	public String getDistName() {
		return distName;
	}
	public void setDistName(String distName) {
		this.distName = distName;
	}
	public String getHospitalName() {
		return hospitalName;
	}
	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName;
	}
	public String getHospitalOnCheque() {
		return hospitalOnCheque;
	}
	public void setHospitalOnCheque(String hospitalOnCheque) {
		this.hospitalOnCheque = hospitalOnCheque;
	}
	public String getLocation() {
		return location;
	}
	public void setLocation(String location) {
		this.location = location;
	}
	public String getRecognition() {
		return recognition;
	}
	public void setRecognition(String recognition) {
		this.recognition = recognition;
	}
	public String getOnline() {
		return online;
	}
	public void setOnline(String online) {
		this.online = online;
	}
	
	public String getMobileNo() {
		return mobileNo;
	}
	public void setMobileNo(String mobileNo) {
		this.mobileNo = mobileNo;
	}
	
	public String getEnteredBy() {
		return enteredBy;
	}
	public void setEnteredBy(String enteredBy) {
		this.enteredBy = enteredBy;
	}
	
	@Override
	public String toString() {
		return "AddHospitalForm [hospitalName=" + hospitalName + ", hospitalOnCheque=" + hospitalOnCheque
				+ ", location=" + location + ", recognition=" + recognition + ", online=" + online + ", distNo="
				+ distNo + ", distName=" + distName + ", mobileNo=" + mobileNo + ", enteredBy=" + enteredBy + "]";
	}
	
	

}

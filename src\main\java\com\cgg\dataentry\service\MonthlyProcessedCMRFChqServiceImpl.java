package com.cgg.dataentry.service;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.text.ParseException;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class MonthlyProcessedCMRFChqServiceImpl implements MonthlyProcessedCMRFChqService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private static final Logger logger = LoggerFactory.getLogger(MonthlyProcessedCMRFChqServiceImpl.class);
    private static final int BATCH_SIZE = 1000; // Adjust based on your needs

    @Override
    public void processExcelAndInsert(String month, String year, MultipartFile file, String userId) throws IOException {
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = WorkbookFactory.create(inputStream)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();

            // Skip header row
            if (rowIterator.hasNext()) rowIterator.next();

            List<Object[]> batchArgs = new ArrayList<>(BATCH_SIZE);
            int rowCount = 0;
            int totalProcessed = 0;

            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                try {
                    Object[] rowData = processRow(month, year, row);
                    if (rowData != null) {
                        Object[] finalRowData = Arrays.copyOf(rowData, rowData.length + 1);
                        finalRowData[rowData.length] = userId; // Append userId to the end
                        batchArgs.add(finalRowData);           // Add extended array to batch
                        rowCount++;
                    }

                    // Execute batch when size reaches BATCH_SIZE
                    if (rowCount >= BATCH_SIZE) {
                        executeBatch(batchArgs);
                        totalProcessed += rowCount;
                        logger.info("Processed {} rows (total: {})", rowCount, totalProcessed);
                        batchArgs.clear();
                        rowCount = 0;
                    }
                } catch (Exception e) {
                    logger.error("Error processing row {}: {}", row.getRowNum() + 1, e.getMessage());
                    // Continue with next row
                }
            }

            // Process remaining rows
            if (!batchArgs.isEmpty()) {
                executeBatch(batchArgs);
                totalProcessed += batchArgs.size();
                logger.info("Processed final batch of {} rows (total: {})", batchArgs.size(), totalProcessed);
            }

            logger.info("Successfully processed {} rows in total", totalProcessed);

        } catch (Exception e) {
            logger.error("Error processing Excel file", e);
            throw new IOException("Failed to process Excel file: " + e.getMessage(), e);
        }
    }

    private Object[] processRow(String month, String year, Row row) {
        // Get cells (0-based index)
        Cell chequeDateCell = row.getCell(1);
        Cell chequeNoCell = row.getCell(2);
        Cell chequeAmtCell = row.getCell(3);

        // Skip row if any required cell is empty
        if (chequeDateCell == null || chequeNoCell == null || chequeAmtCell == null) {
            logger.warn("Skipping row {} due to missing data", row.getRowNum() + 1);
            return null;
        }

        // Parse values
        String formattedDate = parseDateCell(chequeDateCell);
        String chequeNo = parseChequeNumber(chequeNoCell);
        BigDecimal chequeAmt = parseAmountCell(chequeAmtCell);

        return new Object[] {
            Integer.parseInt(month),
            Integer.parseInt(year),
            formattedDate,
            chequeNo,
            chequeAmt
        };
    }

    private void executeBatch(List<Object[]> batchArgs) {
    	jdbcTemplate.batchUpdate(
    		    "INSERT INTO cmrf_cheque_processed_payments " +
    		    "(month, year, cheque_date, cheque_no, cheque_amt, entered_by, entered_on) " +
    		    "VALUES (?, ?, ?::date, ?, ?, ?, CURRENT_TIMESTAMP)",
    		    batchArgs
    		);
    }
    private String parseDateCell(Cell cell) {
        try {
            String dateStr;
            if (cell.getCellType() == CellType.NUMERIC) {
                // Handle numeric date (Excel internal date format)
                Date javaDate = cell.getDateCellValue();
                SimpleDateFormat outputFormat = new SimpleDateFormat("dd-MM-yyyy");
                return outputFormat.format(javaDate);
            } else if (cell.getCellType() == CellType.STRING) {
                // Handle string date in MM-DD-YYYY format (with possible leading dash)
                dateStr = cell.getStringCellValue().trim();
                if (dateStr.startsWith("-")) {
                    dateStr = dateStr.substring(1); // Remove leading dash
                }
                
                 String[] possibleFormats = { "dd-MM-yyyy", "MM-dd-yyyy", "dd/MM/yyyy", "d/M/yyyy", "M/d/yyyy", "yyyy-MM-dd" };
                for (String fmt : possibleFormats) {
                    try {
                        SimpleDateFormat inputFormat = new SimpleDateFormat(fmt);
                        inputFormat.setLenient(false);
                        Date date = inputFormat.parse(dateStr);
                        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");
                        return outputFormat.format(date);
                    } catch (ParseException ignored) {
                        // Try next format
                    }
                }
                throw new IllegalArgumentException("Unsupported date format: " + dateStr);
            }
            throw new IllegalArgumentException("Date must be in MM-DD-YYYY format (numeric or string)");
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid date format: " + cell.toString());
        }
    }

    private String parseChequeNumber(Cell cell) {
        if (cell.getCellType() == CellType.STRING) {
            return cell.getStringCellValue().trim();
        } else if (cell.getCellType() == CellType.NUMERIC) {
            // Handle negative cheque numbers
            double num = cell.getNumericCellValue();
            if (num == (long) num) {
                return String.valueOf((long) num); // Integer value
            }
            return String.valueOf(num); // Decimal value
        }
        throw new IllegalArgumentException("Cheque number must be string or numeric");
    }

    private BigDecimal parseAmountCell(Cell cell) {
        try {
            if (cell.getCellType() == CellType.NUMERIC) {
                return BigDecimal.valueOf(cell.getNumericCellValue());
            } else if (cell.getCellType() == CellType.STRING) {
                String amountStr = cell.getStringCellValue().trim();
                if (amountStr.isEmpty()) {
                    return null;
                }
                // Handle Indian number format (1,50,000.00) and negative values
                amountStr = amountStr.replace(",", "");
                
                // Handle negative amounts
                boolean isNegative = amountStr.startsWith("-");
                if (isNegative) {
                    amountStr = amountStr.substring(1);
                }
                
                BigDecimal amount = new BigDecimal(amountStr);
                return isNegative ? amount.negate() : amount;
            }
            throw new IllegalArgumentException("Amount must be numeric or properly formatted string");
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid amount format: " + cell.toString());
        }
    }
}
package com.cgg.dataentry.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.dataentry.dao.UpdateCmrfEntryDao;
import com.cgg.dataentry.model.UpdateCmrfEntryForm;

@Service
public class UpdateCmrfEntryServiceImpl implements UpdateCmrfEntryService {
	
	@Autowired
	private UpdateCmrfEntryDao updateCmrfEntryDao;
	
	public List<UpdateCmrfEntryForm> getRecommendedDetails()throws Exception{
		
		return updateCmrfEntryDao.getRecommendedDetails();
	}
	public List<UpdateCmrfEntryForm> getHospitalList()throws Exception{
		
		return updateCmrfEntryDao.getHospitalList();
	}
	public List<UpdateCmrfEntryForm> getDistricts()throws Exception{
		
		return updateCmrfEntryDao.getDistricts();
	}

	/*
	 * public String saveCmrfDetails(AddCmrfEntryForm addCmrfEntryForm, Map<String,
	 * Object> model) throws Exception{ System.out.println("service"); return
	 * addCmrfEntryDao.saveCmrfDetails(addCmrfEntryForm,model); }
	 */
public List<UpdateCmrfEntryForm> getMandals(String distCode)throws Exception{
		
		return updateCmrfEntryDao.getMandals(distCode);
	}
public String getInwardData(String mlaCmrfNo) throws Exception{
	System.out.println("service");
	return updateCmrfEntryDao.getInwardData( mlaCmrfNo);
}
public String getTokenBankData(String mlaCmrfNo) throws Exception{
	System.out.println("service");
	return updateCmrfEntryDao.getTokenBankData( mlaCmrfNo);
}
@Override
public String getCmrfData(String cmrfVal) throws Exception {
	return updateCmrfEntryDao.getCmrfData(cmrfVal);
}
@Override
public int updateCmrfDetails(UpdateCmrfEntryForm updateCmrfForm, Map<String, Object> model,HttpServletRequest request) {
	// TODO Auto-generated method stub
	return updateCmrfEntryDao.updateCmrfDetails(updateCmrfForm, model,request);
}
public String getCmrfLocData(String cmrfLocNo) throws Exception{
	System.out.println("service");
	return updateCmrfEntryDao.getCmrfLocData( cmrfLocNo);
}
public UpdateCmrfEntryForm getCmrfValues(String cmrfVal) throws Exception{
	System.out.println("service");
	return updateCmrfEntryDao.getCmrfValues( cmrfVal);
}
public List<UpdateCmrfEntryForm> getOtherConsList()throws Exception{
	
	return updateCmrfEntryDao.getOtherConsList();
}


}

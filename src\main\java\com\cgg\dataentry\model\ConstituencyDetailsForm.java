package com.cgg.dataentry.model;

public class ConstituencyDetailsForm {
	private String districtId;
	private String distNo,distName;
	private String constituencyNo;
	private String constituencyName;
	private String MlaMpName;
	private String designation;
	private String party;
	private String minsiter;
	private String gender;
	private String address;
	private String cellno;
	private String email;
	private String isActive;
	private String localAddr;
	
	private String prefix;
	
	private String constName;
	private String constNo;
	public String getConstituencyName() {
		return constituencyName;
	}
	public void setConstituencyName(String constituencyName) {
		this.constituencyName = constituencyName;
	}
	public String getDistrictId() {
		return districtId;
	}
	public void setDistrictId(String districtId) {
		this.districtId = districtId;
	}
	public String getDistNo() {
		return distNo;
	}
	public void setDistNo(String distNo) {
		this.distNo = distNo;
	}
	public String getDistName() {
		return distName;
	}
	public void setDistName(String distName) {
		this.distName = distName;
	}
	public String getConstituencyNo() {
		return constituencyNo;
	}
	public void setConstituencyNo(String constituencyNo) {
		this.constituencyNo = constituencyNo;
	}
	public String getConstName() {
		return constName;
	}
	public void setConstName(String constName) {
		this.constName = constName;
	}
	public String getConstNo() {
		return constNo;
	}
	public void setConstNo(String constNo) {
		this.constNo = constNo;
	}
	public String getMlaMpName() {
		return MlaMpName;
	}
	public void setMlaMpName(String mlaMpName) {
		MlaMpName = mlaMpName;
	}
	public String getDesignation() {
		return designation;
	}
	public void setDesignation(String designation) {
		this.designation = designation;
	}
	public String getParty() {
		return party;
	}
	public void setParty(String party) {
		this.party = party;
	}
	public String getMinsiter() {
		return minsiter;
	}
	public String getGender() {
		return gender;
	}
	public void setGender(String gender) {
		this.gender = gender;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getCellno() {
		return cellno;
	}
	public void setCellno(String cellno) {
		this.cellno = cellno;
	}
	public String getEmail() {
		return email;
	}
	public void setEmail(String email) {
		this.email = email;
	}
	public String getIsActive() {
		return isActive;
	}
	public void setIsActive(String isActive) {
		this.isActive = isActive;
	}
	public String getLocalAddr() {
		return localAddr;
	}
	public void setLocalAddr(String localAddr) {
		this.localAddr = localAddr;
	}
	public void setMinsiter(String minsiter) {
		this.minsiter = minsiter;
	}
	public String getPrefix() {
		return prefix;
	}
	public void setPrefix(String prefix) {
		this.prefix = prefix;
	}
	
}

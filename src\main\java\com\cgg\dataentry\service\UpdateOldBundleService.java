package com.cgg.dataentry.service;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.dataentry.entities.CmrfOldBundleDetails;
import com.cgg.dataentry.model.MlaCmrfEntityModel;
import com.cgg.dataentry.repositories.CmrfOldBundleDetailsRepository;
import com.cgg.dataentry.repositories.MlaInwardCmrfEditRepository;
import com.cgg.dataentry.repositories.UpdateOldBundleRepository;

@Service
public class UpdateOldBundleService {

	@Autowired
	private UpdateOldBundleRepository updateOldBundleRepository;
	
	 @Autowired
	 CmrfOldBundleDetailsRepository cmrfOlsBundleDtlsRepo;
	

	    @Autowired
		MlaInwardCmrfEditRepository logRepo;

	public MlaCmrfEntityModel getOldTokenDetails(String mlaCmrfNo) {
		return updateOldBundleRepository.getOldTokenDetails(mlaCmrfNo);
	}

	public String getStatusName(String statusNo) {
		 int statusInt = Integer.parseInt(statusNo);
		return updateOldBundleRepository.getStatusName(statusInt);
	}
	
	public String updateOldBundle(String mlaCmrfNo,  String userId){
		 int updateLog = logRepo.insertMlaCmrfLog(mlaCmrfNo,"Updating Old Bundle",userId,"IP");
		 String batchSerialNumber=null;
         if (updateLog > 0) {

            batchSerialNumber = processOldBatch(mlaCmrfNo);
             String batchName = null;
             if (batchSerialNumber != null && !batchSerialNumber.isEmpty()) {
                 batchName = batchSerialNumber.split("_")[0];
                 System.out.println("Batch Name : " + batchName);
             }
             System.out.println("Batch drial : " + batchSerialNumber);

             if (batchSerialNumber != null && batchName != null) {
                int updateApplication = updateOldBundleRepository.updateBatchDetails(mlaCmrfNo, userId, batchSerialNumber, batchName);

                 if (updateApplication > 0) {
             		return batchSerialNumber;
                 }else {
                	 return null;
                 }
	}
         }
		return batchSerialNumber;
         }
	public String processOldBatch(String tokenNo) {
        try {
            // Get the latest batch by ID
        	CmrfOldBundleDetails latestBatch = cmrfOlsBundleDtlsRepo.findTopByOrderByIdDesc();
            System.out.println("Latest Batch: " + latestBatch);

            // Initialize batch name and count for the first entry
            String latestBatchName;
            int currentBatchCount;

            if (latestBatch == null) {
                latestBatchName = "OB1";
                currentBatchCount = 0;
            } else {
                latestBatchName = latestBatch.getBatchName();
                currentBatchCount = latestBatch.getBatchSerialNo();
            }

            System.out.println("Latest batch name: " + latestBatchName);
            System.out.println("Current Batch Count: " + currentBatchCount);

            // Fetch batch size from the property
            String batchSizePropertyValue = cmrfOlsBundleDtlsRepo.cmrfPropertyValueByName("batch_size");
            int batchSize = Integer.parseInt(batchSizePropertyValue);

            // Determine the next batch serial number
            int batchSerialNo = currentBatchCount + 1;
           CmrfOldBundleDetails cmrfOldBundleDetails;

            if (latestBatch == null || batchSerialNo > batchSize) {
                // Create a new batch if no previous batch exists or batch size exceeded
            	System.out.println("latestBatchName----"+latestBatchName);
                int nextBatchNumber = (latestBatch == null) ? 1 : Integer.parseInt(latestBatchName.substring(2)) + 1;
                latestBatchName = "OB" + nextBatchNumber;
                batchSerialNo = 1;

                cmrfOldBundleDetails = new CmrfOldBundleDetails();
                cmrfOldBundleDetails.setBatchName(latestBatchName);
                cmrfOldBundleDetails.setBatchSerialNo(batchSerialNo);
                cmrfOldBundleDetails.setCreatedDate(LocalDateTime.now());
                cmrfOlsBundleDtlsRepo.save(cmrfOldBundleDetails);
            } else {
                // Update the existing batch
                latestBatch.setBatchSerialNo(batchSerialNo);
                cmrfOlsBundleDtlsRepo.save(latestBatch);
                cmrfOldBundleDetails = latestBatch;
            }

            System.out.println("Batch details processed successfully!");
            return latestBatchName + "_" + batchSerialNo;
        } catch (Exception e) {
            e.printStackTrace();
            return "Error While Processing Batch.";
        }
    }

}

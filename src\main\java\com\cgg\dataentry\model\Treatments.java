package com.cgg.dataentry.model;

import java.io.Serializable;

public class Treatments implements Serializable {

    public Integer healthCareServiceId;
    public String healthCareServiceName;
    public Integer procedureId;
    public String procedureName;
    public Integer subTreatmentId;
    public String subTreatmentName;

    public Integer getHealthCareServiceId() {
        return healthCareServiceId;
    }
    public void setHealthCareServiceId(Integer healthCareServiceId) {
        this.healthCareServiceId = healthCareServiceId;
    }
    public String getHealthCareServiceName() {
        return healthCareServiceName;
    }
    public void setHealthCareServiceName(String healthCareServiceName) {
        this.healthCareServiceName = healthCareServiceName;
    }
    public Integer getProcedureId() {
        return procedureId;
    }
    public void setProcedureId(Integer procedureId) {
        this.procedureId = procedureId;
    }
    public String getProcedureName() {
        return procedureName;
    }
    public void setProcedureName(String procedureName) {
        this.procedureName = procedureName;
    }
    public Integer getSubTreatmentId() {
        return subTreatmentId;
    }
    public void setSubTreatmentId(Integer subTreatmentId) {
        this.subTreatmentId = subTreatmentId;
    }
    public String getSubTreatmentName() {
        return subTreatmentName;
    }
    public void setSubTreatmentName(String subTreatmentName) {
        this.subTreatmentName = subTreatmentName;
    }
}

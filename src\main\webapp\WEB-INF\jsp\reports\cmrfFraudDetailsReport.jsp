<%@ page language="java" import="java.util.*" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<!DOCTYPE HTML>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<title>CMRF Fraud Details Report</title>

<script type="text/javascript" src="https://code.jquery.com/jquery-1.11.3.min.js"></script>
<script src="js/jquery-1.12.4.js"></script>
<script src="js/jquery-ui.js"></script>
<link rel="stylesheet" type="text/css" href="css/dataTables.bootstrap.min.css"/>
<script type="text/javascript" src="js/dataTables.bootstrap.min.js"></script>
<jsp:include page="/WEB-INF/jsp/include_DT.jsp" />  
<link rel="stylesheet" href="css/flatpickr.min.css">
<link rel="stylesheet" href="css/jquery-ui.css">
<script src="js/common/commonValidations.js"></script>

<script type="text/javascript">	
function isNumber(evt) {
     var iKeyCode = (evt.which) ? evt.which : evt.keyCode
     if (iKeyCode != 46 && iKeyCode > 31 && (iKeyCode < 48 || iKeyCode > 57))
         return false;
     return true;
}


$(document).ready(function() {
	$('#myTable').DataTable({
		dom : 'Bfrtip',
		paging : true,
		lengthMenu: [
			[10, 20, 50, 100, -1],
			[10, 20, 50, 100, 'All']
		],
		language : {
			"loadingRecords" : "Please wait - loading..."
		},
		buttons : [
			{
				extend : 'print',
				text: '<i class="fa fa-print"></i>',
				title : 'CMRF Fraud Details Report',
				titleAttr: 'Print',
				orientation : 'landscape',
				pageSize : 'A4',
				customize: function(win) {
					if('not empty statusToDate'){
						var body = $(win.document.body);
						body.css('font-size', '10pt').css('text-align', 'center').prepend(
									`<h4>CMRF / PR Entry Details From <c:out value="${statusFromDate}"></c:out> to <c:out value="${statusToDate}"></c:out></h4>`
						);
					}
					if(!'empty statusToDate'){
						var body = $(win.document.body);
						body.css('font-size', '10pt').css('text-align', 'center').prepend(
									`<h4>CMRF / PR Entry Details For <c:out value="${statusFromDate}"></c:out></h4>`
						);
					}
				}
			},

			{
				extend : 'excelHtml5',
				text: '<i class="fa fa-file-excel-o"></i>',
				title : 'CMRF Fraud Details Report',
				titleAttr: 'Export to Excel',
				orientation : 'landscape',
				pageSize : 'A4',
			},
			
			{
				extend : 'pdfHtml5',
				text: '<i class="fa fa-file-pdf-o"></i>',
				title : 'CMRF Fraud Details Report',
				titleAttr: 'Export to PDF',
				orientation : 'landscape',
				pageSize : 'A4',
				customize: function(doc) {
				    doc.styles.tableHeader.alignment = 'left';
				    doc.styles.tableHeader.fillColor = '#563d7c';
				    doc.styles.tableHeader.color = 'white';
				    doc.styles.tableHeader.fontSize = 12;

				    doc.styles.tableBodyEven = {
				        alignment: 'left',
				        fontSize: 10
				    };
				    doc.styles.tableBodyOdd = {
				        alignment: 'left',
				        fontSize: 10
				    };

				    // Safely check if content[1] is the table
				    if (doc.content && doc.content[1] && doc.content[1].table && doc.content[1].table.widths) {
				        doc.content[1].table.widths = ['25%', '25%', '25%', '25%']; // Adjust as needed
				    }
				}

			},
			
			{
                extend: 'colvis',
                text: '<i class="fa fa-list"></i>',
                titleAttr: 'Show/Hide Columns'
            },
            
			{
                extend: 'pageLength',
                titleAttr: 'Page Length',
            }
		],
		
		initComplete: function () {
              // Focus on the search text box
              $('#myTable_filter input').focus();
         }
	})
});

	

function getReport(){
	let cmrfDateValue = document.getElementById('cmrfDate').value;
    let cmrfToDateValue = document.getElementById('cmrfToDate').value;
    
    let cmrfDateParts = cmrfDateValue.split('-');
    let cmrfToDateParts = cmrfToDateValue.split('-');

	let cmrfDate = new Date(cmrfDateParts[2], cmrfDateParts[1] - 1, cmrfDateParts[0]);  // Y, M-1, D
    let cmrfToDate = new Date(cmrfToDateParts[2], cmrfToDateParts[1] - 1, cmrfToDateParts[0]);  // Y, M-1, D	
			
	if(cmrfDate==null || cmrfDate==''){
			Swal.fire({
				title: "Enter From Date",
				icon: "warning",
				confirmButtonText: "Ok"
			}).then(() => {
				$("#cmrfDate").focus();
			});
			return false;
	}else if(cmrfToDate==null || cmrfToDate==''){
			Swal.fire({
				title: "Enter To Date",
				icon: "warning",
				confirmButtonText: "Ok"
			}).then(() => {
				$("#cmrfToDate").focus();
			});
			return false;	
	} else if(cmrfDate > cmrfToDate){
			Swal.fire({
				title: "To Date should be greater than or equal to From Date.",
				icon: "warning",
				confirmButtonText: "Ok"
			})
			return false;
	}	
  	return true;
}	
</script>
</head>

<body>

	<form:form action="cmrfFraudDetailsReport" modelAttribute="CMRFEntryDetailsReport" method="POST">
		<div class="container complete_wrap">
			<div class="container" style="background: white; margin: 20px auto; width: 55%; border-top: 5px solid #515458; border-radius: 5px; box-shadow: 1px 1px 10px;">
				<h3 align="center">CMRF Fraud Details Report</h3><br>
					<div class="row">
						<div class="col-sm-12">
								<div class="form-group col-sm-6">
									<label for="textfile-id">From Date:</label>
								</div>
								<div class="form-group col-sm-6">
									<form:input path="cmrfDate" id="cmrfDate" placeholder="DD/MM/YYYY" autocomplete="off" class="form-control" tabindex="6" />
								</div>
						</div>
					</div>					
					<div class="row">
						<div class="col-sm-12">
							<div class="form-group col-sm-6">
								<label for="textfile-id">To Date:</label>
							</div>
							<div class="form-group col-sm-6">
								<form:input path="cmrfToDate" id="cmrfToDate" placeholder="DD/MM/YYYY" autocomplete="off" class="form-control" tabindex="6" />
							</div>
						</div>
					</div>
							
					<div class="text-center">
							<button type="submit" class="btn btn-primary" onclick="return getReport();">Submit</button>
					</div>
				<br>
			</div>
				
			<br>
		
			<h4 align="center" style="color: red;font-weight: bold;">
				<c:out value="${msg}"></c:out>
			</h4>
			
			<c:if test="${not empty cmrfFraudDetailsReportData}">
				 <div class="container" style="background: white;border-top: 5px solid #40295b;padding: 20px;margin: 20px auto;">			
				    <table id="myTable" class="display TFtable" align="center">
					  <thead>
						 <tr>
							<c:if test="${not empty cmrfFraudDetailsReportData}">
								<c:if test="${not empty statusToDate}">
									<th colspan="4" style="text-align: center;color: red;">
										CMRF Fraud Details Report from <c:out value="${statusFromDate}"></c:out> to <c:out value="${statusToDate}"></c:out>
									</th>
								</c:if>
								<c:if test="${empty statusToDate}">
									<th colspan="4" style="text-align: center;color: red;">
										CMRF Fraud Details Report For <c:out value="${statusFromDate}"></c:out>
									</th>
								</c:if>
							</c:if>
						</tr>						
						<tr style="background: #563d7c;color: white;">
							<th style="text-align: center;">Sl No.</th>
							<th style="text-align: center;">Name</th>
							<th style="text-align: center;">Mobile No</th>
							<th style="text-align: center;">Fraud Details</th>
							<th style="text-align: center;">Entered Date & Time</th>
						</tr>						
					</thead>
					<tbody>
						<c:forEach items="${cmrfFraudDetailsReportData}" var="cmrfEntryDetails1" varStatus="theCount">
							<tr>
								<td align="center">${theCount.count}</td>
								<td>${cmrfEntryDetails1.name}</td>
								<td>${cmrfEntryDetails1.mobileNo}</td>
								<td>${cmrfEntryDetails1.fraudDetails}</td>
								<td>${cmrfEntryDetails1.enteredDateTime}</td>
							</tr>
						</c:forEach>
					</tbody>					
				</table>
			</div>
		 </c:if>			
	  </div>
   </form:form>
</body>

<script src="js/flatpickr.min.js"></script>

<script>
	document.addEventListener('DOMContentLoaded', function() {
		flatpickr('#cmrfDate', {
			dateFormat: "d-m-Y",
			defaultDate: "today",
			minDate: "07-12-2023",
			maxDate: new Date()
		});
		flatpickr('#cmrfToDate', {
			dateFormat: "d-m-Y",
			defaultDate: "today",
			minDate: "07-12-2023",
			maxDate: new Date()
		});
	});
</script>

<script type="text/javascript">
   $('#myTable').addClass('table-bordered');
</script>
	
</html>

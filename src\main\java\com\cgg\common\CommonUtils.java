package com.cgg.common;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Tuple;
import javax.persistence.TupleElement;

public class CommonUtils {
	public static boolean insertQuery(Connection con,String sql)throws Exception {
		 Statement st=null;
		 boolean flag = false;
		 
		 try {
			con.setAutoCommit(false);
			st = con.createStatement();
			int cnt =st.executeUpdate(sql);
			if(cnt>0){
				con.commit();
				flag = true;
			}else
				con.rollback();
			 
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			con.rollback();
			throw e;
		}
		 finally{
				if(st!=null)st.close();
			}
		 return flag;
	}
	public static String getStringfromQuery(Connection con,String sql) throws Exception
	{
		String result="";
		Statement st=null;
		ResultSet rs= null;
		try
		{			
			st=con.createStatement();
			rs=st.executeQuery(sql);
			if(rs.next())
				result=rs.getString(1);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			throw e;
		}
		 finally{
			 	if(rs!=null)rs.close();
				if(st!=null)st.close();
			}
		

		return result;
	}
	public static String inWords(String no){
		
		String[] singles={"","One","Two","Three","Four","Five","Six","Seven","Eight","Nine"};
		String[] middles={"Ten","Eleven","Twelve","Thirteen","Fourteen","Fifteen","Sixteen","Seventeen","Eighteen","Nineteen"};
		String[] tens={"Ten","Twenty","Thirty","Forty","Fifty","Sixty","Seventy","Eighty","Ninety"};

		String wordstr="";
		int nolength=0;
		int num=0;
	        if(no!=null &&!no.equalsIgnoreCase("null"))
	        num=Integer.parseInt(no);
		nolength=(String.valueOf(num)).length();
	try{
		if(nolength>9){
			wordstr="Amount is Greater than the Range";
			return wordstr;
		}
		int ft=0;
	  do{
		switch(nolength){
				case 1 :
					wordstr+=" "+singles[num];
	                nolength=0;
	                num=0;
	                break;
				case 2 :
					if(num>10 && num<20){
						wordstr+=" "+middles[num%10];
	                    num=0;
	                    nolength=0;
	                }
	                else{
						wordstr+=" "+tens[num/10-1];
	                    num=num%10;
	                    nolength=1;
	                }
	                break;
	            case 3 :
	                wordstr+=" "+singles[num/100]+" Hundred";
	                num=num%100;
	                nolength=(String.valueOf(num).toString()).length();
	                break;
	            case 4 :
	                wordstr+=" "+singles[num/1000]+" Thousand";
	                num=num%1000;
	                nolength=(String.valueOf(num).toString()).length();
	                break;
	            case 5 :
	                ft=num/1000;
	                if(ft>10 && ft<20)
						wordstr+=" "+middles[ft-10]+" Thousand";
	                else {
						wordstr+=" "+tens[ft/10-1];
	                    if(ft%10!=0)
							wordstr+=" "+singles[ft%10]+" Thousand";
						else
							wordstr+=" Thousand";
	                }
	                num=num%1000;
	                nolength=(String.valueOf(num).toString()).length();
	                break;
	            case 6 :
	                wordstr+=" "+singles[num/100000]+" Lakh";
	                num=num%100000;
	                nolength=(String.valueOf(num).toString()).length();
	                break;
	            case 7 :
	                ft=num/100000;
	                if(ft>10 && ft<20)
						wordstr+=" "+middles[ft-10]+" Lakh";
	                else{
						wordstr+=" "+tens[ft/10-1];
	                    if(ft%10!=0)
							wordstr+=" "+singles[ft%10]+" Lakh";
						else
							wordstr+=" Lakh";
	                }
	                num=num%100000;
	                nolength=(String.valueOf(num).toString()).length();
	                break;
	            case 8 :
	                wordstr+=" "+singles[num/10000000]+" Crore";
	                num=num%10000000;
	                nolength=(String.valueOf(num).toString()).length();
	                break;
				case 9 :
	                ft=num/10000000;
	                if(ft>10 && ft<20)
						wordstr+=" "+middles[ft-10]+" Crore";
	                else{
						wordstr+=" "+tens[ft/10-1];
	                    if(ft%10!=0)
							wordstr+=" "+singles[ft%10]+" Crore";
						else
							wordstr+=" Crore";
	                }
	                num=num%10000000;
	                nolength=(String.valueOf(num).toString()).length();
	                break;
		}
	  }while(nolength!=0);
	  wordstr+=" Only";
	}catch(Exception e){
			e.printStackTrace();
	}
	  return wordstr;
	}

	public static String convert(String no){
		int i=3;
		if(no.length()>3){
			no=""+no.substring(0,no.length()-3)+","+no.substring(no.length()-3,no.length());
			String a=no.substring(0,no.length()-4);
		   while(a.length()>2){
			i=i+3;
			no=""+no.substring(0,no.length()-i)+","+no.substring(no.length()-i,no.length());
			a=no.substring(0,no.length()-i-1);
			}
	    }
	return no;
	}
	
	public static boolean updateQuery(Connection con,String sql)throws Exception {
		 Statement st=null;
		 boolean flag = false;
		 
		 try {
			con.setAutoCommit(false);
			st = con.createStatement();
			int cnt =st.executeUpdate(sql);
			if(cnt>0){
				con.commit();
				flag = true;
			}else
				con.rollback();
			 
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			con.rollback();
			throw e;
		}
		 finally{
				if(st!=null)st.close();
			}
		 return flag;
	}
	
	public static void closeCon(Connection con,Statement st,ResultSet rs)throws Exception {
		if(rs!=null) {rs.close();rs=null;}
		if(st!=null) {st.close();st=null;}
		if(con!=null && !con.isClosed()) {con.close();con=null;}
	}
	
	   public static List<Map<String, Object>> convertTuplesToListMap(List<Tuple> tuples) 
		{
		    List<Map<String, Object>> result = new ArrayList<>();
		    for (Tuple single : tuples) 
		    {
		        Map<String, Object> tempMap = new HashMap<>();
		        for (TupleElement<?> key : single.getElements())
		        {
		            tempMap.put(key.getAlias(), single.get(key));
		        }
		        result.add(tempMap);
		    }
		    return result;
		}

	public static String formatDate(String date, String inputFormat, String outputFormat) throws ParseException {
        if (date == null || date.isEmpty()) {
            return ""; // Handle null or empty input
        }
        SimpleDateFormat input = new SimpleDateFormat(inputFormat);
        SimpleDateFormat output = new SimpleDateFormat(outputFormat);
        Date parsedDate = input.parse(date);
        return output.format(parsedDate);
    }
}

package com.cgg.common;

import com.cgg.hospital.model.InwardCmrf;
import com.google.zxing.WriterException;
import com.lowagie.text.*;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;

import java.io.IOException;
import java.io.OutputStream;

import java.awt.Color;

import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

public class mlaInwardPDF {
    public void mlaPDF(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException, WriterException {
        System.out.println("GENERATING PDF");

        HttpSession sess = request.getSession();
        ServletContext context = sess.getServletContext();       
        String context_path=context.getRealPath("/");;
        System.out.println("context path is -------> "+context_path);

        // Retrieve formData object from the request/session
        InwardCmrf formData = (InwardCmrf) request.getAttribute("formData");
        if (formData == null) {
            System.out.println("Form data not found in the request.");
            return;
        }

        // Set content type to PDF
        response.setContentType("application/pdf");
        response.setHeader("Content-Disposition", "attachment; filename=" + formData.getMla_cmrf_no() + ".pdf");

        // Create a new document
        Document document = new Document();
        try {
            // Get output stream to write the PDF content
            OutputStream out = response.getOutputStream();
            PdfWriter.getInstance(document, out);

            document.open();
            
            // Define font styles
            Font boldFont = new Font(Font.HELVETICA, 12, Font.BOLD);
            Font normalFont = new Font(Font.HELVETICA, 8);
            Font titleFont = new Font(Font.HELVETICA, 10, Font.BOLD);
            Font tableHeadings = new Font(Font.HELVETICA, 10, Font.BOLD, new Color(25, 135, 84));
            Font cmrfNoFont = new Font(Font.HELVETICA, 8, Font.BOLD, Color.RED);

            // Create a table with 3 columns for two images and the title
            PdfPTable headTable = new PdfPTable(3);
            headTable.setWidthPercentage(100); // Table width 100% of the page
            headTable.setWidths(new float[]{1f, 4f, 1f}); // Column widths (1:4:1 ratio)

            // Add the left image
            try {
                Image leftImage = Image.getInstance(context_path+"/images/tsemblem.gif");
                leftImage.scaleToFit(70, 70); // Scale image
                leftImage.setAlignment(Image.ALIGN_LEFT); // Align left
                PdfPCell leftImageCell = new PdfPCell(leftImage, false); // Cell without borders
                leftImageCell.setBorder(Rectangle.NO_BORDER);
                headTable.addCell(leftImageCell);
            } catch (IOException e) {
                e.printStackTrace();
            }

            // Add the title as a vertically centered element in the middle column
            PdfPCell titleCell = new PdfPCell();
            titleCell.setBorder(Rectangle.NO_BORDER);
            titleCell.setHorizontalAlignment(Element.ALIGN_CENTER); // Align content horizontally in the cell
            titleCell.setVerticalAlignment(Element.ALIGN_MIDDLE);   // Align content vertically in the cell
            titleCell.setUseAscender(true); // Use ascender to ensure better vertical alignment

            // Create a phrase for the title and set alignment in the paragraph itself
            Paragraph titleParagraph1 = new Paragraph("APPLICATION FORM", titleFont);
            titleParagraph1.setAlignment(Element.ALIGN_CENTER); // Center-align the paragraph text
            titleCell.addElement(titleParagraph1);

            Paragraph titleParagraph2 = new Paragraph("FOR SEEKING FINANCIAL ASSISTANCE / ", titleFont);
            titleParagraph2.setAlignment(Element.ALIGN_CENTER); // Center-align the paragraph text
            titleCell.addElement(titleParagraph2);

            Paragraph titleParagraph3 = new Paragraph("EXGRATIA UNDER \"CHIEF MINISTER'S RELIEF FUND\"", titleFont);
            titleParagraph3.setAlignment(Element.ALIGN_CENTER); // Center-align the paragraph text
            titleCell.addElement(titleParagraph3);

            Paragraph titleParagraph4 = new Paragraph("Government of Telangana", tableHeadings);
            titleParagraph4.setAlignment(Element.ALIGN_CENTER); // Center-align the paragraph text
            titleCell.addElement(titleParagraph4);

            // Add the title cell to the table
            headTable.addCell(titleCell);

            // Add the right image
            try {
                Image rightImage = Image.getInstance(context_path+"/images/CmImageNOH.jpg");
                rightImage.scaleToFit(70, 70); // Scale image
                rightImage.setAlignment(Image.ALIGN_RIGHT); // Align right
                PdfPCell rightImageCell = new PdfPCell(rightImage, false); // Cell without borders
                rightImageCell.setBorder(Rectangle.NO_BORDER);
                headTable.addCell(rightImageCell);
            } catch (IOException e) {
                e.printStackTrace();
            }

            document.add(headTable);

            PdfPTable table = new PdfPTable(4);
            table.setWidthPercentage(100); // Set table width to 100% of the page width
            table.setSpacingBefore(10f); // Add space before the table
            table.setSpacingAfter(10f); // Add space after the table

            // Add table heading spanning both columns
            PdfPCell headerCell = new PdfPCell(new Phrase("APPLICANT DETAILS", tableHeadings));
            headerCell.setColspan(4); // Span across two columns
            headerCell.setHorizontalAlignment(Element.ALIGN_CENTER); // Center align
            table.addCell(headerCell);
            
            table.addCell(new Phrase("CMRF Token Number", titleFont));
            table.addCell(new Phrase(formData.getMla_cmrf_no(), cmrfNoFont));
            
            PdfPCell gapCell = new PdfPCell(new Phrase("", titleFont));
            gapCell.setColspan(2); // Span across two columns
            table.addCell(gapCell);

            table.addCell(new Phrase("Name : ", titleFont));
            table.addCell(new Phrase(formData.getPatientName(), normalFont));

            table.addCell(new Phrase("Gender : ", titleFont));
            table.addCell(new Phrase(formData.getGender(), normalFont));

            table.addCell(new Phrase("Age : ", titleFont));
            table.addCell(new Phrase(formData.getAge(), normalFont));

            table.addCell(new Phrase("Guardian : ", titleFont));
            table.addCell(new Phrase(formData.getFatherName(), normalFont));

            table.addCell(new Phrase("Aadhaar Number : ", titleFont));
            table.addCell(new Phrase(formData.getAadharNo(), normalFont));

            table.addCell(new Phrase("Mobile Number : ", titleFont));
            table.addCell(new Phrase(formData.getMobileNo(), normalFont));

            table.addCell(new Phrase("District : ", titleFont));
            table.addCell(new Phrase(formData.getDistName(), normalFont));

            table.addCell(new Phrase("Mandal : ", titleFont));
            table.addCell(new Phrase(formData.getMandalName(), normalFont));

            table.addCell(new Phrase("Village : ", titleFont));
            table.addCell(new Phrase(formData.getVillName(), normalFont));

            table.addCell(new Phrase("Address : ", titleFont));
            table.addCell(new Phrase(formData.getPatAddress(), normalFont));

            table.addCell(new Phrase("Pincode : ", titleFont));
            table.addCell(new Phrase(formData.getPinCode(), normalFont));
            
            PdfPCell gapCell1 = new PdfPCell(new Phrase("", titleFont));
            gapCell1.setColspan(2); // Span across two columns
            table.addCell(gapCell1);
            
            table.addCell(new Phrase("Income Certificate Number : ", titleFont));
            table.addCell(new Phrase(formData.getIncomeCerNo(), normalFont));
            
            table.addCell(new Phrase("FSC Number (Ration Card) : ", titleFont));
            table.addCell(new Phrase(formData.getNewFscNo(), normalFont));
            
            // Add the table to the document
            document.add(table);

            PdfPTable table2 = new PdfPTable(4);
            table2.setWidthPercentage(100); // Set table width to 100% of the page width
            table2.setSpacingBefore(10f); // Add space before the table
            table2.setSpacingAfter(10f); // Add space after the table

            // Add table heading spanning both columns
            PdfPCell headerCell2 = new PdfPCell(new Phrase("APPLICANT BANK ACCOUNT DETAILS", tableHeadings));
            headerCell2.setColspan(4); // Span across two columns
            headerCell2.setHorizontalAlignment(Element.ALIGN_CENTER); // Center align
            table2.addCell(headerCell2);
            
            table2.addCell(new Phrase("Bank Name : ", titleFont));
            table2.addCell(new Phrase(formData.getBankName(), normalFont));
            
            table2.addCell(new Phrase("Account Number : ", titleFont));
            table2.addCell(new Phrase(formData.getBankAccNo(), normalFont));
            
            table2.addCell(new Phrase("IFSC : ", titleFont));
            table2.addCell(new Phrase(formData.getBankIfsc(), normalFont));
            
            table2.addCell(new Phrase("Branch : ", titleFont));
            table2.addCell(new Phrase(formData.getBankBranch(), normalFont));

            // Add the table to the document
            document.add(table2);

            PdfPTable table3 = new PdfPTable(4);
            table3.setWidthPercentage(100); // Set table width to 100% of the page width
            table3.setSpacingBefore(10f); // Add space before the table
            table3.setSpacingAfter(10f); // Add space after the table

            // Add table heading spanning both columns
            PdfPCell headerCell3 = new PdfPCell(new Phrase("PEOPLE REPRESENTATIVE", tableHeadings));
            headerCell3.setColspan(4); // Span across two columns
            headerCell3.setHorizontalAlignment(Element.ALIGN_CENTER); // Center align
            table3.addCell(headerCell3);
            
            table3.addCell(new Phrase("Recommended By : ", titleFont));
            table3.addCell(new Phrase(formData.getConstName(), normalFont));
            
            table3.addCell(new Phrase("Letter Date : ", titleFont));
            table3.addCell(new Phrase(formData.getCmrfDate(), normalFont));

            // Add the table to the document
            document.add(table3);

            PdfPTable table4 = new PdfPTable(4);
            table4.setWidthPercentage(100); // Set table width to 100% of the page width
            table4.setSpacingBefore(10f); // Add space before the table
            table4.setSpacingAfter(5f); // Add space after the table

            // Add table heading spanning both columns
            PdfPCell headerCell4 = new PdfPCell(new Phrase("HOSPITAL DETAILS", tableHeadings));
            headerCell4.setColspan(4); // Span across two columns
            headerCell4.setHorizontalAlignment(Element.ALIGN_CENTER); // Center align
            table4.addCell(headerCell4);
            
            table4.addCell(new Phrase("Hospital : ", titleFont));
            table4.addCell(new Phrase(formData.getHospName(), normalFont));
            
            table4.addCell(new Phrase("Bill Amount : ", titleFont));
            table4.addCell(new Phrase(formData.getHospBillAmt() + "/-", normalFont));
            
            table4.addCell(new Phrase("IP Number : ", titleFont));
            table4.addCell(new Phrase(formData.getPatientIpNo(), normalFont));
            
            table4.addCell(new Phrase("Adm. No / Bill No / M.R. No / U.M.R. No / UHID No : ", titleFont));
            table4.addCell(new Phrase(formData.getAdmissNo(), normalFont));

            // Add the table to the document
            document.add(table4);

            PdfPTable table5 = new PdfPTable(4);
            table5.setWidthPercentage(100); // Set table width to 100% of the page width
            table5.setSpacingBefore(5f); // Add space before the table
            table5.setSpacingAfter(5f); // Add space after the table

            // Add table heading spanning both columns
            PdfPCell headerCell5 = new PdfPCell(new Phrase("Details of Treatment : ", titleFont));
            headerCell5.setVerticalAlignment(Element.ALIGN_MIDDLE);
            headerCell5.setColspan(2); // Span across two columns
            table5.addCell(headerCell5);

            // Add table heading spanning both columns
            PdfPCell headerCell6 = new PdfPCell(new Phrase(formData.getPurpose(), normalFont));
            headerCell6.setColspan(2); // Span across two columns
            table5.addCell(headerCell6);

            // Add the table to the document
            document.add(table5);

            // Load the FontAwesome .otf font
            Font fontAwesome = FontFactory.getFont(context_path+"/fonts/FontAwesome.otf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 8);

            // Create the table
            PdfPTable table6 = new PdfPTable(4);
            table6.setWidthPercentage(100); // Set table width to 100% of the page width
            table6.setSpacingBefore(10f); // Add space before the table
            table6.setSpacingAfter(10f); // Add space after the table

            // Add table heading spanning both columns
            PdfPCell headerCell7 = new PdfPCell(new Phrase("Documents to be Submitted to CMRF Office: ", titleFont));
            headerCell7.setVerticalAlignment(Element.ALIGN_MIDDLE);
            headerCell7.setColspan(2);
            table6.addCell(headerCell7);

            // Create the iText list to mimic the <ul><li>...</li> structure
            List pdfList = new List(List.UNORDERED); // Unordered list
            pdfList.setListSymbol("");
            pdfList.setIndentationLeft(10f); // Optional: Set left indentation for better appearance

            // FontAwesome icons for check and cross (using Unicode values)
            String faCheck = "\uf00c";  // FontAwesome check mark
            String faTimes = "\uf00d";  // FontAwesome cross

            // Add ListItem for each formData field using FontAwesome icons
            ListItem item1 = new ListItem(); // Create an empty ListItem
            item1.add(new Chunk(formData.getMlaLetter() ? faCheck : faTimes, fontAwesome)); // Add the icon Chunk
            item1.add(new Chunk(" People Representative's Original Recommendation Letter", normalFont)); // Add the text Chunk
            pdfList.add(item1); // Add the ListItem to the list

            ListItem item2 = new ListItem(); // Create an empty ListItem
            item2.add(new Chunk(formData.getHospitalBills() ? faCheck : faTimes, fontAwesome)); // Add the icon Chunk
            item2.add(new Chunk(" Original Hospital Bills", normalFont)); // Add the text Chunk
            pdfList.add(item2); // Add the ListItem to the list

            ListItem item3 = new ListItem(); // Create an empty ListItem
            item3.add(new Chunk(formData.getAadhaarCopy() ? faCheck : faTimes, fontAwesome)); // Add the icon Chunk
            item3.add(new Chunk(" Aadhaar Copy", normalFont)); // Add the text Chunk
            pdfList.add(item3); // Add the ListItem to the list

            ListItem item4 = new ListItem(); // Create an empty ListItem
            item4.add(new Chunk(formData.getFscRationCard() ? faCheck : faTimes, fontAwesome)); // Add the icon Chunk
            item4.add(new Chunk(" New Food Security Card (Ration Card)", normalFont)); // Add the text Chunk
            pdfList.add(item4); // Add the ListItem to the list

            ListItem item5 = new ListItem(); // Create an empty ListItem
            item5.add(new Chunk(formData.getIncomeCertificate() ? faCheck : faTimes, fontAwesome)); // Add the icon Chunk
            item5.add(new Chunk(" Income Certificate (valid for one year)", normalFont)); // Add the text Chunk
            pdfList.add(item5); // Add the ListItem to the list

            ListItem item6 = new ListItem(); // Create an empty ListItem
            item6.add(new Chunk(formData.getBankPassbook() ? faCheck : faTimes, fontAwesome)); // Add the icon Chunk
            item6.add(new Chunk(" Bank Passbook (Copy of First Page)", normalFont)); // Add the text Chunk
            pdfList.add(item6); // Add the ListItem to the list

            // Create the PdfPCell and add the list
            PdfPCell headerCell8 = new PdfPCell();
            headerCell8.setColspan(2); // Span across 3 columns
            headerCell8.addElement(pdfList); // Add the iText list to the cell

            // Finally, add the headerCell8 to the table
            table6.addCell(headerCell8);

            // Add the table to the document
            document.add(table6);

            
            // Create a PdfPTable with 1 column to hold the entire instructions section
            PdfPTable instructionsTable = new PdfPTable(2);
            instructionsTable.setWidthPercentage(100); // Set table width to 100% of the page width
            instructionsTable.setWidths(new float[]{9, 3}); // Set Column widths: 75% and 25%
            instructionsTable.setSpacingBefore(5f); // Add space before the table
            instructionsTable.setSpacingAfter(5f); // Add space after the table

            // Create a PdfPCell to hold the entire "Instructions" section
            PdfPCell instructionsCell = new PdfPCell();
            instructionsCell.setBorderWidth(0.5f); // Set border width
            instructionsCell.setBorderWidthRight(0);
            // instructionsCell.setPadding(10); // Add padding inside the cell

            // Create a paragraph for the heading "Instructions"
            Paragraph instructionsHeading = new Paragraph("Instructions", boldFont);
            instructionsHeading.setAlignment(Element.ALIGN_CENTER); // Center-align the paragraph text

            // Add the heading to the cell
            instructionsCell.addElement(instructionsHeading);

            // Create an unordered list for the main instruction
            List insList = new List(List.UNORDERED); // Unordered list
            insList.setIndentationLeft(10f); // Indentation from the left
            insList.add(new ListItem("Please attach a copy of all the documents mentioned below to the CMRF Office. These are Mandatory.", normalFont));

            // Add the unordered list to the cell
            instructionsCell.addElement(insList);

            // Create an ordered list for the document requirements
            List ins = new List(List.ORDERED); // Ordered list
            ins.setIndentationLeft(20f); // Indentation from the left

            // Add list items to the ordered list
            ins.add(new ListItem("People Representative's Original Recommendation Letter", normalFont));
            ins.add(new ListItem("Original Hospital Bills", normalFont));
            ins.add(new ListItem("Aadhaar Copy", normalFont));
            ins.add(new ListItem("New Ration Card / Income Certificate (valid for one year)", normalFont));
            ins.add(new ListItem("Bank passbook (Copy of First Page)", normalFont));

            // Add the ordered list to the cell
            instructionsCell.addElement(ins);

            // Add the cell to the instructions table
            instructionsTable.addCell(instructionsCell);

            //QR Payload Data
            String qrData = "{\"tokenNumber\" : \""+formData.getMla_cmrf_no().trim()+"\"}";
            //Generate QR
            byte[] qrCodeImage = QRCodeUtil.generateQRCode(qrData, 80, 80);
            // Create the cell for the QR code
            PdfPCell qrCell = new PdfPCell();
            qrCell.setColspan(2); // Span across two columns
            qrCell.setHorizontalAlignment(Element.ALIGN_CENTER);
            qrCell.setBorderWidthLeft(0);
            String prTokenEnteredOnDate =formData.getEnteredOn();
            
            if (qrCodeImage != null) {
                Image qrImage = Image.getInstance(qrCodeImage); // Replace with actual path
                qrImage.scaleToFit(80, 80); // Scale the image if needed
                qrImage.setAlignment(Image.ALIGN_CENTER);
                qrCell.addElement(qrImage);
                
                if (prTokenEnteredOnDate != null && !prTokenEnteredOnDate.trim().isEmpty()) {
                     Font largeFont = new Font(normalFont.getFamily(), 12, Font.NORMAL);
                     Paragraph prTokenEnteredOn = new Paragraph( prTokenEnteredOnDate, largeFont);
                     prTokenEnteredOn.setAlignment(Element.ALIGN_CENTER);
                     prTokenEnteredOn.setSpacingBefore(-12f);
                     qrCell.addElement(prTokenEnteredOn);
                }
                
                table.addCell(qrCell);
            } else {
                qrCell.addElement(new Paragraph("QR Code Here", normalFont));
            }

            instructionsTable.addCell(qrCell);

            // Finally, add the instructions table (with border) to the document
            document.add(instructionsTable);

            document.close();
            out.close();
        } catch (DocumentException e) {
            e.printStackTrace();
        }
    }
}

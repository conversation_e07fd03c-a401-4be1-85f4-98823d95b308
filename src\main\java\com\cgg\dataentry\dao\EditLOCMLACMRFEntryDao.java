package com.cgg.dataentry.dao;

import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.cgg.dataentry.model.EditLOCMLACMRFEntryModel;

public interface EditLOCMLACMRFEntryDao {
	List<EditLOCMLACMRFEntryModel> getRecommendedDetails() throws Exception;
	List<EditLOCMLACMRFEntryModel> getHospitalDetails() throws Exception;
	//Boolean saveLocMlaCmrfEntry(LOCMLACMRFEntryModel locEntryForm) throws Exception;
	List<EditLOCMLACMRFEntryModel> getLocData(String loc_token) throws Exception;
	boolean updateLocDetails(EditLOCMLACMRFEntryModel locEntryForm,HttpServletRequest request);
	public List<EditLOCMLACMRFEntryModel> getDistricts() throws Exception;
}

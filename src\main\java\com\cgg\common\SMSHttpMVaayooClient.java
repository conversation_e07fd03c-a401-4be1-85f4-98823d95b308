package com.cgg.common;


import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
 
//http://api.mvaayoo.com/mvaayooapi/MessageCompose?user=<EMAIL>:RAMANA@123&senderID=AEPASS&receipientno=9&msgtxt=Hi Hariantha
public class SMSHttpMVaayooClient {
/*    static String username = "cgg-epass";
    static String password = "cGG@Epass1";
*/    static String username = "<EMAIL>";
    static String password = "RAMANA@123";
    
    static String senderid = "TSCMRF";
    
   // static String senderid = "AEPASS";
    /*static String username = "cgg-hepass";
    static String password = "hP#m8Sges";
    static String senderid = "HEPASS";*/
  //  static String message = "Test SMS from MSDG, Sorry for inconvenience!";
   // static String mobileNo = "09324596412";
    //static String mobileNos = "09324596412,09324596412";
    // StartTime Format: YYYYMMDD hh:mm:ss
   // static String scheduledTime = "20110701 02:27:00";
    static HttpURLConnection connection = null;

// Method for sending single SMS.
public static HttpURLConnection sendSingleSMS1(String mobileNo, String message,String templateId)throws Exception {
	 HttpURLConnection connection = null;
	 DataInputStream input=null;
	 DataOutputStream output=null;
	 
try {
        String smsservicetype = "singlemsg"; // For single message.
            String query = "user=" + URLEncoder.encode(username) +":" + URLEncoder.encode(password)
//                + "&password=" + URLEncoder.encode(password)
             //   + "&smsservicetype=" + URLEncoder.encode(smsservicetype)
                + "&msgtxt=" + URLEncoder.encode(message) + "&receipientno="
                + URLEncoder.encode(mobileNo) + "&senderID="
                + URLEncoder.encode(senderid);
            //System.out.println("query is "+query);
          
            //URL url = new URL("http://msdgweb.mgov.gov.in/esms/sendsmsrequest");
          //  URL url = new URL("http://api.mvaayoo.com/mvaayooapi/MessageCompose");
            URL url = new URL("http://api.mvaayoo.com/mvaayooapi/MessageCompose?template_id="+templateId);
            connection = (HttpURLConnection) url.openConnection();
            connection.setDoInput(true);
            connection.setDoOutput(true);
            connection.setRequestMethod("POST");
            connection.setFollowRedirects(true);
            
        connection.setRequestProperty("Content-length", String
            .valueOf(query.length()));
        connection.setRequestProperty("Content-Type",
            "application/x-www-form-urlencoded");
        connection.setRequestProperty("User-Agent",
            "Mozilla/4.0 (compatible; MSIE 5.0; Windows 98; DigExt)");
 
        // open up the output stream of the connection
           output = new DataOutputStream(connection.getOutputStream());
 
        // write out the data
        int queryLength = query.length();
        output.writeBytes(query);
        // output.close();
 
        // get ready to read the response from the cgi script
        input = new DataInputStream(connection.getInputStream());
 
        // read in each character until end-of-stream is detected
        for (int c = input.read(); c != -1; c = input.read())
            System.out.print((char) c);
       // input.close();
    } catch (Exception e) {
        System.out.println("Something bad just happened.");
        System.out.println(e);
        e.printStackTrace();
    }finally{
    	if(input!=null)input.close();
    	if(output!=null)output.close();
    }
 
    return connection;
}
 
// method for sending bulk SMS
/*public static HttpURLConnection sendBulkSMS(String mobileNos, String message) {
try {
        String smsservicetype = "bulkmsg"; // For bulk msg
        String query = "username=" + URLEncoder.encode(username)
            + "&password=" + URLEncoder.encode(password)
            + "&smsservicetype=" + URLEncoder.encode(smsservicetype)
            + "&content=" + URLEncoder.encode(message)
+ "&bulkmobno=" + URLEncoder.encode(mobileNos, "UTF-8")
+ "&senderid=" + URLEncoder.encode(senderid);
 
        connection.setRequestProperty("Content-length", String
            .valueOf(query.length()));
        connection.setRequestProperty("Content-Type",
            "application/x-www-form-urlencoded");
        connection.setRequestProperty("User-Agent",
            "Mozilla/4.0 (compatible; MSIE 5.0; Windows 98; DigExt)");
 
        // open up the output stream of the connection
        DataOutputStream output = new DataOutputStream(connection
            .getOutputStream());
 
        // write out the data
        int queryLength = query.length();
        output.writeBytes(query);
        // output.close();
 
        System.out.println("Resp Code:" + connection.getResponseCode());
        System.out.println("Resp Message:" + connection.getResponseMessage());
 
        // get ready to read the response from the cgi script
        DataInputStream input = new DataInputStream(connection
            .getInputStream());
 
        // read in each character until end-of-stream is detected
        for (int c = input.read(); c != -1; c = input.read())
            System.out.print((char) c);
        input.close();
        } catch (Exception e) {
            System.out.println("Something bad just happened.");
            System.out.println(e);
            e.printStackTrace();
        }
        return connection;
    }
 
// method for sending the scheduled SMS
public static HttpURLConnection sendScheduledSMS(String mobileNos, String message, String scheduledTime) {
 
    try {
        String smsservicetype = "schmsg"; // For Scheduled message.
 
        String query = "username=" + URLEncoder.encode(username)
            + "&password=" + URLEncoder.encode(password)
            + "&smsservicetype=" + URLEncoder.encode(smsservicetype)
            + "&content=" + URLEncoder.encode(message)
+ "&bulkmobno=" + URLEncoder.encode(mobileNos, "UTF-8")
+ "&senderid=" + URLEncoder.encode(senderid) + "&time="
            + URLEncoder.encode(scheduledTime, "UTF-8");
 
        connection.setRequestProperty("Content-length", String
            .valueOf(query.length()));
        connection.setRequestProperty("Content-Type",
            "application/x-www-form-urlencoded");
        connection.setRequestProperty("User-Agent",
            "Mozilla/4.0 (compatible; MSIE 5.0; Windows 98; DigExt)");
 
        // open up the output stream of the connection
        DataOutputStream output = new DataOutputStream(connection
                    .getOutputStream());
 
        // write out the data
        int queryLength = query.length();
        output.writeBytes(query);
        // output.close();
 
        System.out.println("Resp Code:" + connection.getResponseCode());
        System.out.println("Resp Message:"
                    + connection.getResponseMessage());
 
        // get ready to read the response from the cgi script
        DataInputStream input = new DataInputStream(connection
            .getInputStream());
 
        // read in each character until end-of-stream is detected
        for (int c = input.read(); c != -1; c = input.read())
            System.out.print((char) c);
            input.close();
        } catch (Exception e) {
            System.out.println("Something bad just happened.");
            System.out.println(e);
            e.printStackTrace();
        }
        return connection;
    }*/
public static HttpURLConnection sendSingleSMS(String mobileNo, String message,String templateid) throws SQLException {
	
	Connection con=null;
	
	System.out.println("mvayoo");

	try {
	//	con = dataSource.getConnection();
		
		String smsservicetype = "singlemsg"; // For single message.
		/*String query = "user=" + URLEncoder.encode(username) +":" + URLEncoder.encode(password)+ "&msgtxt=" + URLEncoder.encode(message) + "&receipientno="+ URLEncoder.encode(mobileNo) + "&senderID="+ URLEncoder.encode(senderid)
		+"&type=1 &Service Key=31af9092-d353-11eb-9c5d-0abea36e3286&template_id="+templateid;*/
		String query =getSmsJsonRequest(mobileNo,message,templateid);
		//System.out.println("query--"+query);
		//HttpURLConnection connection = null;
		String smsId=null;
		String str="";
  	        	   
		URL url = new URL("https://api.imiconnect.in/resources/v1/messaging");
		connection = (HttpURLConnection) url.openConnection();
		connection.setDoInput(true);
		connection.setDoOutput(true);
		connection.setRequestMethod("POST");
		connection.setFollowRedirects(true);
		connection.setRequestProperty("Content-length", String.valueOf(query.length()));
		connection.setRequestProperty("Content-Type","application/json;charset=UTF-8");
		//connection.setRequestProperty("Key","31af9092-d353-11eb-9c5d-0abea36e3286");
		connection.setRequestProperty("Key","98361b07-dee3-11eb-9c5d-0abea36e3286");
		connection.setRequestProperty("User-Agent","Mozilla/4.0 (compatible; MSIE 5.0; Windows 98; DigExt)");
		connection.setRequestProperty("Accept-Charset","UTF-8");
		connection.setReadTimeout(20000);
		connection.setConnectTimeout(20000);    		
		DataOutputStream output = new DataOutputStream(connection.getOutputStream());
		output.write(query.getBytes("UTF-8"));
		/*smsId=insertSmsJsonRequest(mobileNo,templateid,query,con);
		
		if(smsId!=null && !"".equals(smsId)) {
			StringBuilder sb = new StringBuilder();
    		DataInputStream input = new DataInputStream(connection.getInputStream());
    		for (int c = input.read(); c != -1; c = input.read()) {
    		 sb.append((char) c);
    		}
    		str = sb.toString();
         // String jsonObj= converjsonToJavaArray(str);
    		boolean updcCnt=updateSmsJsonResponse(smsId,str,con);
			
		}*/
		
	System.out.println(connection.getResponseCode());
	}catch (Exception e) {
		System.out.println("Something bad just happened.");
		e.printStackTrace();
	}finally {
		
		if(con!=null && !con.isClosed()){con.close();con=null;}
	}

	return connection;
} 

public static String getSmsJsonRequest(String mobileNo, String message,String templateid) {		 
	 JSONObject jo = new JSONObject();	
	 JSONObject jo1 = new JSONObject();		 
	 JSONObject jo2 = new JSONObject();		 
	 JSONObject jo3 = new JSONObject();	
	 JSONObject jo4 = new JSONObject();	

	  try {
		JSONArray ja = new JSONArray();
	    JSONArray ja1 = new JSONArray();
	   
        jo.put("deliverychannel", "sms");		       	       
        jo1.put("type", "4");
        jo1.put("senderid",senderid);
        jo1.put("text", message);		       
        jo2.put("dlt_templateid",templateid);
        jo1.put("extras",jo2);
        jo3.put("sms", jo1); 
        jo.put("channels", jo3);
        ja1.add(mobileNo);
        jo4.put("correlationid", "Test_API_Optional");
        jo4.put("msisdn", ja1);		     
        ja.add(jo4);		       
        jo.put("destination", ja);		    
	    System.out.println(jo.toString());			
	} catch (Exception e) {
		// TODO: handle exception
	}
	
	 return jo.toString();
	 
}

public static String converjsonToJavaArray(String jsonData)
 
{
JSONObject obj=null;
JSONArray arr=null;
	try {		
    System.out.println("JSON Object");  
     obj=(JSONObject)JSONValue.parse(jsonData); 
    arr=(JSONArray)obj.get("response"); 
   
   // System.out.println(arr.get(0));  //this will print {"id":42,...sities ..
    JSONArray arr1=(JSONArray)obj.get(arr.get(0)); 
    List<Object>list=new ArrayList<Object>();   
    for(int i=0;i<arr.size();i++) {
        JSONObject index = (JSONObject) arr.get(i);
        String data = (String) index.get("code");
        String transid = (String) index.get("transid");
        String description = (String) index.get("description");
        String correlationid = (String) index.get("correlationid");
       
       
    }
    
	} catch (Exception e) {
		// TODO Auto-generated catch block
		e.printStackTrace();
	} 
	
	return arr.toString();
}

public static String insertSmsJsonRequest(String mobileNo,String templateid,String jsonReq,Connection con) throws Exception {
	String jsonReqSql=null;
	int insCnt=0;
	String smsId=null;
	// Connection con = DatabasePlugin.connect();
	 boolean flag=false;
	 //smsId =DatabasePlugin.getStringfromQueryNew("select nextval('sms_imicon_seq')",null); 
	// smsId = CommonUtils.getStringfromQuery(con, "select nextval('sms_imicon_seq')");
	 smsId="123";
	jsonReqSql ="insert into sms_imiconnect_msg(sms_id,template_id,mobile_no," +
			  "sms_req_msg,sms_req_date)" + " values" +
			  " ('"+smsId+"','"+templateid+"','"+mobileNo+"','"+jsonReq+"',now())"; 
	//flag=CommonUtils.insertQuery(con, jsonReqSql); 
	flag=true;
	if(flag)
		return smsId;
	else 
		return null;
	
}
public static boolean updateSmsJsonResponse(String smsId,String response,Connection con) throws Exception {
	// Connection con = DatabasePlugin.connect();
	String jsonResSql=null;
	int updCnt=0;
	boolean flag=false;
	jsonResSql="update sms_imiconnect_msg set sms_res_msg='"+response+"',sms_res_date=now() where"
			+ " sms_id='"+smsId+"'";
	//updCnt=DatabasePlugin.executeUpdate_dost(jsonResSql); 
//	flag=CommonUtils.updateQuery(con, jsonResSql);
	
	return flag;
	
}
}


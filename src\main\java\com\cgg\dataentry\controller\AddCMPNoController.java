package com.cgg.dataentry.controller;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;

import com.cgg.dataentry.model.AddCMPNo;
import com.cgg.dataentry.service.AddCMPNoService;

@Controller
@RequestMapping(value = "/addCMPNo")
public class AddCMPNoController 
{
	@Autowired
	AddCMPNoService addCMPNoService;
	
	@RequestMapping(method = RequestMethod.GET)
	ModelAndView addCMPNo(@ModelAttribute("AddCMPNo") AddCMPNo addCMPNo) throws Exception
	{
		ModelAndView mav = new ModelAndView();
		mav.addObject("addCMPNo", addCMPNo);
		mav.addObject("cmpList", addCMPNoService.getCmpNo(addCMPNo));
		mav.setViewName("addCmpNo");
		return mav;
	}
	@RequestMapping(method = RequestMethod.POST)
	ModelAndView updSanctionDate(@ModelAttribute("AddCMPNo") AddCMPNo addCMPNo,HttpServletRequest request) throws Exception
	{
		ModelAndView mav = new ModelAndView();
		mav.addObject("msg", addCMPNoService.updSanctionDate(addCMPNo,request));
		mav.addObject("cmpList", addCMPNoService.getCmpNo(addCMPNo));
		mav.setViewName("addCmpNo");
		addCMPNo.setCMPDate("");
		return mav;
	}
}
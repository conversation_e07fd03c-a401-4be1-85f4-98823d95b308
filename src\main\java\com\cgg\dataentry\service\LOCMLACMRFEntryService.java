package com.cgg.dataentry.service;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.cgg.dataentry.model.LOCMLACMRFEntryModel;
import com.cgg.dataentry.model.Treatments;

public interface LOCMLACMRFEntryService {

	List<LOCMLACMRFEntryModel> getRecommendedDetails(HttpSession session) throws Exception;
	
	List<LOCMLACMRFEntryModel> getHospitalDetails(HttpSession session) throws Exception;

	String saveLocMlaCmrfEntry(LOCMLACMRFEntryModel locEntryForm,HttpServletRequest request) throws Exception;
	
	String saveMlaLocCmrfEntry(LOCMLACMRFEntryModel locEntryForm,HttpServletRequest request) throws Exception;

	List<LOCMLACMRFEntryModel> getLocData(String loc_token) throws Exception;

	boolean updateLocDetails(LOCMLACMRFEntryModel locEntryForm);

	public List<LOCMLACMRFEntryModel> getDistricts() throws Exception;
	
	public List<LOCMLACMRFEntryModel> getMandals(String distCode) throws Exception;

	public List<Treatments> getHealthCareServices() throws Exception;

	public List<Treatments> getSubTreatmentsByHcsId(Integer treatParId) throws Exception;

	public List<Treatments> getProceduresBySubId(Integer treatParId,Integer treatSubId) throws Exception;
}

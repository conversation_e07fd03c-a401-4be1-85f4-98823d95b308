package com.cgg.dataentry.controller;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.cgg.common.Response;
import com.cgg.dataentry.service.MlaInwardCmrfStatusService;

 
@Controller
public class MlaInwardCmrfStatusController {
	
	@Autowired
	MlaInwardCmrfStatusService inwardCmrfStatusService;
	
   
	
	@GetMapping("/getCmrfStatus")
	public String getCmrfStatus(@RequestParam("tokenStatus") String tokenStatus,Map<String, Object> model,HttpServletRequest request) {
 	 Response res= inwardCmrfStatusService.getAllStagesStatus(tokenStatus, request);

		 if(res.getStatus()!=HttpStatus.OK) {
			 model.put("msg", res.getMessage());
				 return  "getCmrfStatus";
		 }
				
		 System.out.println(res.getData());
  		 model.put("cmrfTokenDetails", res.getData());
 		  
		return "getCmrfStatus";
	} 
	
	@GetMapping("/getCmrfStatusTree")
	public String getCmrfStatusTree(
		// @RequestParam("tokenStatus") String tokenStatus,Map<String, Object> model,HttpServletRequest request
		) {
 	//  Response res= inwardCmrfStatusService.getAllStagesStatus(tokenStatus, request);

	// 	 if(res.getStatus()!=HttpStatus.OK) {
	// 		 model.put("msg", res.getMessage());
	// 			 return  "getCmrfStatus";
	// 	 }
				
	// 	 System.out.println(res.getData());
  	// 	 model.put("cmrfTokenDetails", res.getData());
 		  
		return "getStatusTree";
	} 
	
	

}
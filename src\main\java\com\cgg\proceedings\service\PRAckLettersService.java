package com.cgg.proceedings.service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Tuple;

import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.cgg.common.ApplicationConstants;
import com.cgg.common.CommonFunctions;
import com.cgg.common.CommonUtils;
import com.cgg.proceedings.model.PRAckLettersForm;
import com.cgg.proceedings.repositories.PRAckLettersRepository;
import com.cgg.reports.entities.uploadAckLetterForm;

@Service
public class PRAckLettersService {

    @Autowired
    PRAckLettersRepository repo;

    private static final Logger logger = LoggerFactory.getLogger(PRAckLettersService.class);

    public List<?> getPRAckLetters(PRAckLettersForm prAckForm) throws Exception {
        try {
            logger.info("Fetching PR Acknowledgement Form..." + prAckForm.toString());

            String fromDate = prAckForm.getFromDate();
            String toDate = prAckForm.getToDate();

            DateTimeFormatter inputFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd"); // incoming format
            DateTimeFormatter outputFormat = DateTimeFormatter.ofPattern("dd-MM-yyyy"); // desired format

            LocalDate fromLocalDate = LocalDate.parse(fromDate, inputFormat);
            LocalDate toLocalDate = LocalDate.parse(toDate, inputFormat);

            String dateFrom = fromLocalDate.format(outputFormat);
            String dateTo = toLocalDate.format(outputFormat);

            logger.info("Date From: " + dateFrom + ", Date To: " + dateTo);
            List<Tuple> records = repo.getPRAckLetters(dateFrom, dateTo);
            return CommonUtils.convertTuplesToListMap(records);
        } catch (Exception e) {
            logger.error("Error fetching PR Acknowledgement letters: " + e.getMessage(), e);
            throw e;
        }
    }

    public List<PRAckLettersForm> getCmpDetails(String dateFrom, String dateTo, String cno, String otherConst) throws Exception {
        try {
            List<Tuple> resultTuples = null;

            if (cno != null && "998".equals(cno) && otherConst != null && !"-".equals(otherConst)) {
                resultTuples = repo.getCmpDetailsForOtherConst(dateFrom, dateTo, cno, otherConst);
            } else {
                resultTuples = repo.getCmpDetails(dateFrom, dateTo, cno);
            }
            // List<Tuple> resultTuples = repo.getCmpDetails(dateFrom, dateTo, cno);

            List<PRAckLettersForm> resultList = new ArrayList<>();

            for (Tuple tuple : resultTuples) {
                PRAckLettersForm prAckLettersForm = new PRAckLettersForm();
                prAckLettersForm.setOther_const(tuple.get("other_const", String.class));
                prAckLettersForm.setCno(tuple.get("cno") != null ? tuple.get("cno").toString() : null);
                prAckLettersForm.setCmp_no(tuple.get("cmp_no", String.class));
                prAckLettersForm.setCname(tuple.get("cname", String.class));
                prAckLettersForm.setEsigned_date(tuple.get("esigned_date", String.class));
                prAckLettersForm.setCheques_cnt(tuple.get("cheques_cnt") != null ? tuple.get("cheques_cnt").toString() : null);
                resultList.add(prAckLettersForm);
            }

            return resultList;

        } catch (Exception e) {
            throw new Exception("Error fetching CMP details: " + e.getMessage(), e);
        }
    }

    public List<PRAckLettersForm> getPRCmpDetails(String dateFrom, String dateTo) throws Exception {
        try {
            List<Tuple> resultTuples = repo.getPRCmpDetails(dateFrom, dateTo);

            List<PRAckLettersForm> resultList = new ArrayList<>();

            for (Tuple tuple : resultTuples) {
                PRAckLettersForm prAckLettersForm = new PRAckLettersForm();
                prAckLettersForm.setOther_const(tuple.get("other_const", String.class));
                prAckLettersForm.setCno(tuple.get("cno") != null ? tuple.get("cno").toString() : null);
                prAckLettersForm.setCmp_no(tuple.get("cmp_no", String.class));
                prAckLettersForm.setCname(tuple.get("cname", String.class));
                prAckLettersForm.setEsigned_date(tuple.get("esigned_date", String.class));
                prAckLettersForm.setCheques_cnt(tuple.get("cheques_cnt") != null ? tuple.get("cheques_cnt").toString() : null);
                resultList.add(prAckLettersForm);
            }

            return resultList;

        } catch (Exception e) {
            throw new Exception("Error fetching CMP details: " + e.getMessage(), e);
        }
    }

	public Map<String, String> uploadAckLetter(uploadAckLetterForm uploadAckLetterForm) throws Exception {
		Map<String, String> responseMap = new HashMap<>();

		try {
            Integer cno = uploadAckLetterForm.getCno();
            String otherConst = uploadAckLetterForm.getOtherConst();
            String otherConstNo = null;
            String esignedFromDate = String.valueOf(uploadAckLetterForm.getEsignedFromDate());
            String esignedToDate = String.valueOf(uploadAckLetterForm.getEsignedToDate());
            String paName = uploadAckLetterForm.getPaName();
            String paMobileNo = uploadAckLetterForm.getPaMobileNo();
            String enteredBy = uploadAckLetterForm.getEnteredBy();
            String enteredIp = uploadAckLetterForm.getEnteredIp();

            logger.info("Esigned From Date: " + esignedFromDate + ", Esigned To Date: " + esignedToDate + ", CNO: " + cno + ", Other Const: " + otherConst);

            if (CommonFunctions.validateData(esignedFromDate) &&
                CommonFunctions.validateData(esignedToDate) &&
                cno != null &&
                CommonFunctions.validateData(otherConst)) {

                String resultEsignedDate = null;
                if (cno == 998 && CommonFunctions.validateData(otherConst) && !"-".equals(otherConst)) {
                    resultEsignedDate = repo.getAckDetailsForUploadWithOtherConst(esignedFromDate, esignedToDate, cno, otherConst);
                } else {
                    resultEsignedDate = repo.getAckDetailsForUpload(esignedFromDate, esignedToDate, cno);
                }

                if (CommonFunctions.validateData(resultEsignedDate)) {
                    String[] dateParts = resultEsignedDate.split("-");
                    if (dateParts.length < 3) {
                        responseMap.put("status", "false");
                        responseMap.put("message", "Invalid date format from DB.");
                        return responseMap;
                    }
                    String dateVal = dateParts[0] + dateParts[1] + dateParts[2].substring(2, 4);

                    // Sanitize otherConst (if needed for folder name)
                    if (CommonFunctions.validateData(otherConst) && otherConst.contains("-")) {
                        String[] parts = otherConst.split("-");
                        if (parts.length > 1) {
                            otherConstNo = parts[1]; // safely get second part
                        }
                    }

                    String ackLetterPath = uploadFile(uploadAckLetterForm.getAckLetter(), dateVal, cno, otherConstNo);
                    if (ackLetterPath == null) {
                        responseMap.put("status", "false");
                        responseMap.put("message", "Error in file uploading.");
                        return responseMap;
                    }

                    int updateResult;
                    if (cno == 998 && CommonFunctions.validateData(otherConst) && !"-".equals(otherConst)) {
                        updateResult = repo.updateAckDetailsForUploadWithOtherConst(
                                ackLetterPath, paName, paMobileNo, enteredBy, enteredIp, cno, otherConst, resultEsignedDate
                        );
                    } else {
                        updateResult = repo.updateAckDetailsForUpload(
                                ackLetterPath, paName, paMobileNo, enteredBy, enteredIp, cno, resultEsignedDate
                        );
                    }

                    if (updateResult > 0) {
                        responseMap.put("status", "true");
                        responseMap.put("message", "Acknowledgement letter uploaded successfully.");
                    } else {
                        responseMap.put("status", "false");
                        responseMap.put("message", "Failed to upload acknowledgement letter.");
                    }
                } else {
                    responseMap.put("status", "false");
                    responseMap.put("message", "Scanned details not found for the acknowledgement letter.");
                }
            } else {
                responseMap.put("status", "false");
                responseMap.put("message", "Missing or invalid input data.");
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            responseMap.put("status", "false");
            responseMap.put("message", "Error: " + ex.getMessage());
        }

		return responseMap;
	}

	public static String uploadFile(MultipartFile file, String dateVal, Integer cno, String otherConstNo) {
		String otherName = "";
        if (file == null || file.isEmpty()) {
        	throw new IllegalArgumentException("File cannot be empty!");
        }
        String originalFileName = file.getOriginalFilename();
		// System.out.println("ORIGINAL FILE NAME : " + originalFileName);
		String fileExtension = FilenameUtils.getExtension(originalFileName);
		// System.out.println("FILE EXTENSION : " + fileExtension);

        // Ensure the upload directory exists
		String uploadPath = ApplicationConstants.ACK_LETTER_PATH + ApplicationConstants.AC_YEAR;
		// System.out.println("UPLOAD PATH : " + uploadPath);
        File dir = new File(uploadPath);
        if (!dir.exists()) {
        	dir.mkdirs();
        }

		if (cno == 998 && !otherConstNo.isEmpty() && !otherConstNo.equals("null")) {
			otherName = "_" + otherConstNo;
		}
        // Construct new file name
        String safeFileName = dateVal + "_" + cno + otherName + "_" + ApplicationConstants.CURRENT_TIMESTAMP + "." + fileExtension;
        String finalPath = ApplicationConstants.BASE_PATH + uploadPath + "/" + safeFileName;
        String DBPath = uploadPath + "/" + safeFileName;
		// System.out.println("Final PATH : " + finalPath);
		// System.out.println("DB PATH : " + DBPath);

        try {
        // Save file
        Files.write(
            Paths.get(finalPath),
            file.getBytes(),
            StandardOpenOption.CREATE
        );
        } catch (IOException e) {
        	throw new RuntimeException("File upload failed", e);
        }

        System.out.println("File uploaded to: " + DBPath);
        return DBPath;
    }
}

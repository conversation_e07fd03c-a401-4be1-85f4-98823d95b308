package com.cgg.dataentry.dao;

import java.net.HttpURLConnection;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.cgg.common.BSNLSMSHttpPostClient;
import com.cgg.common.CommonUtils;
import com.cgg.common.SMSHttpMVaayooClient;
import com.cgg.dataentry.model.AddCmrfEntryForm;
import com.cgg.dataentry.model.AddHospitalForm;

@Repository
public class AddHospitalDaoImpl implements AddHospitalDao{
	
	@Autowired
	DataSource dataSource;

	@Autowired
	JdbcTemplate jdbcTemlate;

	@Override
	public Integer addHospital(AddHospitalForm addHospitalForm, HttpServletRequest request) throws SQLException {
		Connection con = null;
		Statement st = null;
		ResultSet rs = null;
		String sql = null;
		boolean onlineValue;
		Integer hospcode = null;
		String ipAddress = request.getRemoteAddr();
		String templateId=null,templateName=null;
		String smsId=null;		
		// HttpURLConnection connection = null;
		String connection = null;
		int smscnt=0;
		String msg=null;
		int hospCnt=0;
		try {
			String hospitalName = addHospitalForm.getHospitalName().toUpperCase();
			String hospitalOnCheque = addHospitalForm.getHospitalOnCheque();
			String location = addHospitalForm.getLocation();
			String recognition = addHospitalForm.getRecognition();
			String online = addHospitalForm.getOnline();
			String distCode = addHospitalForm.getDistNo();
			String mobileNo =addHospitalForm.getMobileNo();
			String enteredBy=addHospitalForm.getEnteredBy();
			String pwd = null;
			con = dataSource.getConnection();
			st = con.createStatement();
			if(online.equals("1")) {
				onlineValue=true;
			}else {
				onlineValue = false;
			}
			 sql=" select count(1) as cnt from hospital where mobile_no ='"+mobileNo+"' "
				 		+ " and delete_flag='false'";
				 System.out.println("sql--"+sql);
	
				 rs=st.executeQuery(sql);
	        if(rs.next())   
	        {
	        	hospCnt=rs.getInt("cnt");
	        }
	        if(hospCnt>0)
	        {
	       	 	 return -1;
	        }	
			sql = "select max(hospcode)+1 as hospcode from hospital";
			st = con.createStatement();
			rs = st.executeQuery(sql);
			if(rs.next()) {
			    hospcode = rs.getInt("hospcode");
			}
			
			sql = "insert into public.hospital( "
					+ "	hospcode, hospplace , recog, hospname,"
					+ " hospname_on_cheque, is_hosp_online,"
					+ " delete_flag, ip_address, entered_on,dist_code,mobile_no,entered_by"
					+ " )"
					+ "	values ("+hospcode+", '"+location+"', '"+recognition+"','"+hospitalName+"','"+hospitalOnCheque+"' , '"+onlineValue+"', 'false', '"+ipAddress+"',now(),"+distCode+",'"+mobileNo+"','"+enteredBy+"');";
			st = con.createStatement();
			int count = st.executeUpdate(sql);
			if(count>0) {
				sql = "select substr(md5(random()::text), 1, 6) as rand_pwd";
				st = con.createStatement();
				rs = st.executeQuery(sql);
				if(rs.next()) {
					pwd = rs.getString("rand_pwd");
				}
				
				 int userId = Integer.parseInt("90"+hospcode);
				sql = "insert into public.hosp_users_mst("
						+ "	user_id, user_role_code, empl_first_name, user_password_old, enter_by, enter_dt, delete_flag,user_pwd, hosp_code) "
						+ "	values ("+userId+",20,'"+hospitalName+"',md5('"+pwd+"'), 'admin', now(), 'false','"+pwd+"',"+hospcode+");";
				st = con.createStatement();
				st.executeUpdate(sql);

				templateId="1007637674893548830";
				templateName="TSCMRF2";

				msg = "Your hospital CMRF LoginId:" + userId + " and pwd:" + pwd
						+ ".Please login to relieffund.telangana.gov.in. CMO";
				/*msg = "Your hospital CMRF LoginId:" + userId + " and pwd:" + userPwd
						+ ".Please login to relieffund.telangana.gov.in.";*/
				rs.close();
				rs=null;	
				rs = st.executeQuery("select nextval('hospital_sms_seq') as smsId");
				while (rs.next()) {
					smsId = rs.getString("smsId");
				}

				sql = "insert into hospital_otp_sms(sms_id,mobile_no,msg,sent_date) values (" + smsId + ",'"
						+mobileNo+ "','" + msg + "',now())";
				smscnt = st.executeUpdate(sql);

				
				  if (smscnt > 0) {
					//   connection = SMSHttpMVaayooClient.sendSingleSMS("91" + mobileNo, msg,templateId);
					  connection = BSNLSMSHttpPostClient.sendBSNLSms(msg, "91" + mobileNo, templateId, templateName);
					  if ("200".equals(connection))
				  {
						  sql = "update hospital_otp_sms set sms_response='" +
				  connection + "' where sms_id='" + smsId + "'"; 
				int  cnt =st.executeUpdate(sql);
				  
				  } }
			}
		}catch (Exception e) {
			 e.printStackTrace(); 
			 hospcode = null;
		}finally {
			if (rs != null) {
				rs.close();
				rs = null;
			}
			if (st != null) {
				st.close();
				st = null;
			}
			if (con != null && !con.isClosed()) {
				con.close();
				con = null;
			}
		}
		return hospcode;
	}

	@Override
	public List<AddHospitalForm> getDistricts() throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<AddHospitalForm> addHospitalDetails = new ArrayList<AddHospitalForm>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			sql="select distno, distname from  district  order by distname";
			System.out.println("sql---"+sql);
			rs=st.executeQuery(sql);
			while(rs.next()) {
				AddHospitalForm addHospitalEntry = new AddHospitalForm();
				addHospitalEntry.setDistNo(rs.getString("distno"));
				addHospitalEntry.setDistName(rs.getString("distname"));
				addHospitalDetails.add(addHospitalEntry);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return addHospitalDetails;
	}

}

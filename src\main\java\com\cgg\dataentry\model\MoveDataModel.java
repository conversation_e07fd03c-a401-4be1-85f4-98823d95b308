package com.cgg.dataentry.model;

import java.io.Serializable;

public class MoveDataModel implements Serializable  {
	
	private String CMRFFrom;
	private String CMRFTo;
	
	//Hospital wise Abstract Report
	String hosp_name,hosp_code,locNo,patientName,address,cname,sanc_date,assured_amt,CMRFFrom_h,CMRFTo_h;
	int sanctioned_cases;
	int sanctioned_amount;
	
	
	
	public String getCMRFFrom_h() {
		return CMRFFrom_h;
	}
	public void setCMRFFrom_h(String cMRFFrom_h) {
		CMRFFrom_h = cMRFFrom_h;
	}
	public String getCMRFTo_h() {
		return CMRFTo_h;
	}
	public void setCMRFTo_h(String cMRFTo_h) {
		CMRFTo_h = cMRFTo_h;
	}
	public String getCMRFFrom() {
		return CMRFFrom;
	}
	public void setCMRFFrom(String cMRFFrom) {
		CMRFFrom = cMRFFrom;
	}
	public String getCMRFTo() {
		return CMRFTo;
	}
	public void setCMRFTo(String cMRFTo) {
		CMRFTo = cMRFTo;
	}
	public String getHosp_name() {
		return hosp_name;
	}
	public void setHosp_name(String hosp_name) {
		this.hosp_name = hosp_name;
	}
	public String getHosp_code() {
		return hosp_code;
	}
	public void setHosp_code(String hosp_code) {
		this.hosp_code = hosp_code;
	}
	public String getLocNo() {
		return locNo;
	}
	public void setLocNo(String locNo) {
		this.locNo = locNo;
	}
	public String getPatientName() {
		return patientName;
	}
	public void setPatientName(String patientName) {
		this.patientName = patientName;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getCname() {
		return cname;
	}
	public void setCname(String cname) {
		this.cname = cname;
	}
	public String getSanc_date() {
		return sanc_date;
	}
	public void setSanc_date(String sanc_date) {
		this.sanc_date = sanc_date;
	}
	public String getAssured_amt() {
		return assured_amt;
	}
	public void setAssured_amt(String assured_amt) {
		this.assured_amt = assured_amt;
	}
	public int getSanctioned_cases() {
		return sanctioned_cases;
	}
	public void setSanctioned_cases(int sanctioned_cases) {
		this.sanctioned_cases = sanctioned_cases;
	}
	public int getSanctioned_amount() {
		return sanctioned_amount;
	}
	public void setSanctioned_amount(int sanctioned_amount) {
		this.sanctioned_amount = sanctioned_amount;
	}
	
		

}

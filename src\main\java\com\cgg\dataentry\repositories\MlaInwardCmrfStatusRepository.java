package com.cgg.dataentry.repositories;

import java.util.List;
import java.util.Optional;

import javax.persistence.Tuple;
import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.cgg.dataentry.entities.MlaCmrf;
import com.cgg.dataentry.entities.MlaCmrfM;

@Repository
public interface MlaInwardCmrfStatusRepository extends JpaRepository<MlaCmrfM, String>{
	 Optional<MlaCmrfM> findBytokenHash(String tokenHash);
}

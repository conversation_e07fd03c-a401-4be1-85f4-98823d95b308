package com.cgg.Exceptions;

import java.nio.file.AccessDeniedException;

import javax.servlet.http.HttpServletRequest;

public class HandleAccessDenied extends AccessDeniedException {
    private static final long serialVersionUID = 1L;
    private String requestUrl;

    public HandleAccessDenied(String msg, HttpServletRequest req) {
        super(msg);
        this.requestUrl = req.getRequestURL().toString();
    }

    public String getRequestUrl() {
        return requestUrl;
    }
}

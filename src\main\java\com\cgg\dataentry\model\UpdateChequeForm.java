package com.cgg.dataentry.model;

public class UpdateChequeForm {
	
	private String cmrfNo;
	private String cmrfYear;
	private String patName;
	private String patAddress;
	private String chequeNo;
	private String chequeDate; 
	private String fatherSonOf;
	private String revalidatedDate;
	private String correctedName;
	private String isNewChequeIssued;
	private String newChequeNo;
	private String remarks;
	private String othersRemarks;
	private String chequeAmt;
	private String chequeUpdatedDate;
	private String dateFrom;
	private String dateTo;
	private String cmpDate;
	private String amount = null;
	private String formatAmt;
	private String amtInWords;
	private String letterDate=null;
	private String benfName=null;
	private String formatDate=null;
	private String ipAddr=null;
	private String userId=null;
	private String bank_acc_no=null;
	private String bank_ifsc=null;
	private String enteredBy;
	private String userName;
	private String correctedAccountNo;
	private Boolean soApproved;
	private String bankAccHolName;
	// private Boolean isChqReprinted;
	
	public String getChequeUpdatedDate() {
		return chequeUpdatedDate;
	}
	public void setChequeUpdatedDate(String chequeUpdatedDate) {
		this.chequeUpdatedDate = chequeUpdatedDate;
	}
	public String getCmrfNo() {
		return cmrfNo;
	}
	public void setCmrfNo(String cmrfNo) {
		this.cmrfNo = cmrfNo;
	}
	public String getPatName() {
		return patName;
	}
	public void setPatName(String patName) {
		this.patName = patName;
	}
	public String getChequeAmt() {
		return chequeAmt;
	}
	public void setChequeAmt(String chequeAmt) {
		this.chequeAmt = chequeAmt;
	}
	public String getPatAddress() {
		return patAddress;
	}
	public void setPatAddress(String patAddress) {
		this.patAddress = patAddress;
	}
	public String getChequeNo() {
		return chequeNo;
	}
	public void setChequeNo(String chequeNo) {
		this.chequeNo = chequeNo;
	}
	public String getChequeDate() {
		return chequeDate;
	}
	public void setChequeDate(String chequeDate) {
		this.chequeDate = chequeDate;
	}
	public String getFatherSonOf() {
		return fatherSonOf;
	}
	public void setFatherSonOf(String fatherSonOf) {
		this.fatherSonOf = fatherSonOf;
	}
	public String getRevalidatedDate() {
		return revalidatedDate;
	}
	public void setRevalidatedDate(String revalidatedDate) {
		this.revalidatedDate = revalidatedDate;
	}
	public String getCorrectedName() {
		return correctedName;
	}
	public void setCorrectedName(String correctedName) {
		this.correctedName = correctedName;
	}
	public String getIsNewChequeIssued() {
		return isNewChequeIssued;
	}
	public void setIsNewChequeIssued(String isNewChequeIssued) {
		this.isNewChequeIssued = isNewChequeIssued;
	}
	public String getNewChequeNo() {
		return newChequeNo;
	}
	public void setNewChequeNo(String newChequeNo) {
		this.newChequeNo = newChequeNo;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	public String getOthersRemarks() {
		return othersRemarks;
	}
	public void setOthersRemarks(String othersRemarks) {
		this.othersRemarks = othersRemarks;
	}
	public String getCmrfYear() {
		return cmrfYear;
	}
	public void setCmrfYear(String cmrfYear) {
		this.cmrfYear = cmrfYear;
	}
	public String getDateFrom() {
		return dateFrom;
	}
	public void setDateFrom(String dateFrom) {
		this.dateFrom = dateFrom;
	}
	public String getDateTo() {
		return dateTo;
	}
	public void setDateTo(String dateTo) {
		this.dateTo = dateTo;
	}
	public String getCmpDate() {
		return cmpDate;
	}
	public void setCmpDate(String cmpDate) {
		this.cmpDate = cmpDate;
	}
	public String getAmount() {
		return amount;
	}
	public void setAmount(String amount) {
		this.amount = amount;
	}
	public String getFormatAmt() {
		return formatAmt;
	}
	public void setFormatAmt(String formatAmt) {
		this.formatAmt = formatAmt;
	}
	public String getAmtInWords() {
		return amtInWords;
	}
	public void setAmtInWords(String amtInWords) {
		this.amtInWords = amtInWords;
	}
	public String getLetterDate() {
		return letterDate;
	}
	public void setLetterDate(String letterDate) {
		this.letterDate = letterDate;
	}
	public String getBenfName() {
		return benfName;
	}
	public void setBenfName(String benfName) {
		this.benfName = benfName;
	}
	public String getFormatDate() {
		return formatDate;
	}
	public void setFormatDate(String formatDate) {
		this.formatDate = formatDate;
	}
	public String getIpAddr() {
		return ipAddr;
	}
	public void setIpAddr(String ipAddr) {
		this.ipAddr = ipAddr;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getBank_acc_no() {
		return bank_acc_no;
	}
	public void setBank_acc_no(String bank_acc_no) {
		this.bank_acc_no = bank_acc_no;
	}
	public String getBank_ifsc() {
		return bank_ifsc;
	}
	public void setBank_ifsc(String bank_ifsc) {
		this.bank_ifsc = bank_ifsc;
	}
	
	public String getEnteredBy() {
		return enteredBy;
	}
	public void setEnteredBy(String enteredBy) {
		this.enteredBy = enteredBy;
	}
	
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
		
	public String getCorrectedAccountNo() {
		return correctedAccountNo;
	}
	public void setCorrectedAccountNo(String correctedAccountNo) {
		this.correctedAccountNo = correctedAccountNo;
	}
	public Boolean getSoApproved() {
		return soApproved;
	}
	public void setSoApproved(Boolean soApproved) {
		this.soApproved = soApproved;
	}
	// public Boolean getIsChqReprinted() {
	// 	return isChqReprinted;
	// }
	// public void setIsChqReprinted(Boolean isChqReprinted) {
	// 	this.isChqReprinted = isChqReprinted;
	// }
	public String getBankAccHolName() {
		return bankAccHolName;
	}
	public void setBankAccHolName(String bankAccHolName) {
		this.bankAccHolName = bankAccHolName;
	}
	@Override
	public String toString() {
		return "UpdateChequeForm [cmrfNo=" + cmrfNo + ", cmrfYear=" + cmrfYear + ", patName=" + patName
				+ ", patAddress=" + patAddress + ", chequeNo=" + chequeNo + ", chequeDate=" + chequeDate
				+ ", fatherSonOf=" + fatherSonOf + ", revalidatedDate=" + revalidatedDate + ", correctedName="
				+ correctedName + ", isNewChequeIssued=" + isNewChequeIssued + ", newChequeNo=" + newChequeNo
				+ ", remarks=" + remarks + ", othersRemarks=" + othersRemarks + ", chequeAmt=" + chequeAmt
				+ ", chequeUpdatedDate=" + chequeUpdatedDate + ", dateFrom=" + dateFrom + ", dateTo=" + dateTo
				+ ", cmpDate=" + cmpDate + ", amount=" + amount + ", formatAmt=" + formatAmt + ", amtInWords="
				+ amtInWords + ", letterDate=" + letterDate + ", benfName=" + benfName + ", formatDate=" + formatDate
				+ ", ipAddr=" + ipAddr + ", userId=" + userId + ", bank_acc_no=" + bank_acc_no + ", bank_ifsc="
				+ bank_ifsc + ", enteredBy=" + enteredBy + ", userName=" + userName + ", correctedAccountNo="
				+ correctedAccountNo + ", soApproved=" + soApproved + ", bankAccHolName=" + bankAccHolName + "]";
	}
	
	
}

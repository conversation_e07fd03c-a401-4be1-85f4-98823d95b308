package com.cgg.dataentry.repositories;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.cgg.dataentry.entities.LocCmrf;

@Repository
public interface LocCmrfRepository extends JpaRepository<LocCmrf, String> {

    @Modifying
	@Transactional
	@Query(value = "INSERT INTO public.log_loc_cmrf("
			+ "	loc_no, patient_name, father_name, address, assured_amt, hosp_code, purpose, time_stamp, user_id, hospital_name, recommended_by, prev_loc_no, prev_assured_amt, vip_letter_dt, logged_by, logged_on, logged_ipaddress, logged_remarks, updated_by, updated_on, ipaddress, is_qr_scanned, qr_scanned_by, qr_scanned_timestamp, qr_scanned_ip_address, is_whatsapp_sent, pat_district, mobile_no, aadhaar_no ) "
			+ "SELECT loc_no, patient_name, father_name, address, assured_amt, hosp_code, purpose, time_stamp, user_id, hospital_name, recommended_by, prev_loc_no, prev_assured_amt, vip_letter_dt, :logged_by, now(), :logged_ipaddress, :log_type, updated_by, updated_on, ipaddress, is_qr_scanned, qr_scanned_by, qr_scanned_timestamp, qr_scanned_ip_address, is_whatsapp_sent, pat_district, mobile_no, aadhaar_no "
			+" from loc_cmrf where loc_no= :cmrfLocNo ;", nativeQuery = true)
	Integer insertLocCmrfLog(
			@Param("cmrfLocNo") String cmrfLocNo,
			@Param("log_type") String log_type,
			@Param("logged_by") String logged_by,
			@Param("logged_ipaddress") String logged_ipaddress
			);

    @Modifying
	@Transactional
	@Query(value = "INSERT INTO public.loc_mla_cmrf_log(" +
	               "mla_loc_no, patient_name, father_son_of, purpose, assured_amt, " +
	               "recommended_by, hosp_code, status, loc_no, entered_on, delete_flag, " +
	               "updated_on, rej_reasons, address, vip_letter_dt, ip_address, " +
	               "logged_timestamp, logged_ipaddress, logged_by, logged_remarks, " +
	               "updated_by, aadhaar_no, mobile_no, treat_par_id, treat_sub_id, " +
	               "treat_proc_id, pat_district, patient_ip, opcr_no, hod_verified_by, " +
	               "hod_verified_date, hod_verified_ip, supdt_verified_by, " +
	               "supdt_verified_ip, supdt_verified_date, aarogyasree_covered, " +
	               "bed_charges, investig_charges, drugs_disp_charges, surg_proc_charges, " +
	               "implant_charges, misc_charges, treat_dept_id, entered_by, " +
	               "pending_reasons, old_fsc_no, new_fsc_no, income_cer_no, gender, age, " +
	               "pat_mandal, pincode) " +
	               
	               "SELECT mla_loc_no, patient_name, father_son_of, purpose, assured_amt, " +
	               "recommended_by, hosp_code, status, loc_no, entered_on, delete_flag, " +
	               "updated_on, rej_reasons, address, vip_letter_dt, ip_address, " +
	               "now(), :loggedIpaddress, " +
	               ":loggedBy, :loggedRemarks, updated_by, aadhaar_no, mobile_no, " +
	               "treat_par_id, treat_sub_id, treat_proc_id, pat_district, patient_ip, " +
	               "opcr_no, hod_verified_by, hod_verified_date, hod_verified_ip, " +
	               "supdt_verified_by, supdt_verified_ip, supdt_verified_date, " +
	               "aarogyasree_covered, bed_charges, investig_charges, drugs_disp_charges, " +
	               "surg_proc_charges, implant_charges, misc_charges, treat_dept_id, " +
	               "entered_by, pending_reasons, old_fsc_no, new_fsc_no, income_cer_no, " +
	               "gender, age, pat_mandal, pincode " +
	               
	               "FROM loc_mla_cmrf WHERE mla_loc_no = :mlaLocNo", nativeQuery = true)
	Integer insertLocMlaCmrfLog(
			@Param("mlaLocNo") String mlaLocNo,
			@Param("loggedRemarks") String loggedRemarks,
			@Param("loggedBy") String loggedBy,
			@Param("loggedIpaddress") String loggedIpaddress
			);
}

package com.cgg.dataentry.dao;

import java.util.List;
import java.util.Map;

import com.cgg.dataentry.model.AddCmrfEntryForm;

public interface AddCmrfEntryDao {
	
	public List<AddCmrfEntryForm> getRecommendedDetails() throws Exception;
	public List<AddCmrfEntryForm> getHospitalList() throws Exception;
	public List<AddCmrfEntryForm> getDistricts() throws Exception;
	public  List<Map<String, Object>> getCmrfDetailsByAadhar(String aadharNo,String mlaCmrfNo) throws Exception ;
	public String saveCmrfDetails(AddCmrfEntryForm addCmrfEntryForm, Map<String, Object> model)throws Exception;
	public List<AddCmrfEntryForm> getMandals(String distCode) throws Exception;
	public String getInwardData(String mlaCmrfNo)throws Exception;
	public String getCmrfLocData(String cmrfLocNo)throws Exception;
	public List<AddCmrfEntryForm> getOtherConsList() throws Exception;
	public Map<String, Object> getCmrfData(String cmrfNo)throws Exception;
}

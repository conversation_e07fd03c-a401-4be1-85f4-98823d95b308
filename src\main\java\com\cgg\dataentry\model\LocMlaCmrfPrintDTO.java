package com.cgg.dataentry.model;

import java.math.BigDecimal;

public class LocMlaCmrfPrintDTO {
	private String mlaLocNo;
	private String patientName;
	private String fatherSonOf;
	private String patientIp;
	private String age;
	private String distName;
	private String specialName;
	private String departmentName;
	private String address;
	private BigDecimal mobileNo;
	private String aadhaarNo;
	private String opcrNo;
	private String purpose;
	private Integer assuredAmt;
	private String aarogyasreeCovered;
	private Integer bedCharges;
	private Integer investigCharges;
	private Integer drugsDispCharges;
	private Integer surgProcCharges;
	private Integer implantCharges;
	private Integer miscCharges;
	private String currentDate;
	private String amountInWords;
	private Integer hospCode;
	private String hospName;
	private String gender;

	public String getMlaLocNo() {
		return mlaLocNo;
	}

	public void setMlaLocNo(String mlaLocNo) {
		this.mlaLocNo = mlaLocNo;
	}

	public String getPatientName() {
		return patientName;
	}

	public void setPatientName(String patientName) {
		this.patientName = patientName;
	}

	public String getFatherSonOf() {
		return fatherSonOf;
	}

	public void setFatherSonOf(String fatherSonOf) {
		this.fatherSonOf = fatherSonOf;
	}

	public String getPatientIp() {
		return patientIp;
	}

	public void setPatientIp(String patientIp) {
		this.patientIp = patientIp;
	}

	public String getPurpose() {
		return purpose;
	}

	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}

	public Integer getAssuredAmt() {
		return assuredAmt;
	}

	public void setAssuredAmt(Integer assuredAmt) {
		this.assuredAmt = assuredAmt;
	}

	public String getAadhaarNo() {
		return aadhaarNo;
	}

	public void setAadhaarNo(String aadhaarNo) {
		this.aadhaarNo = aadhaarNo;
	}

	public BigDecimal getMobileNo() {
		return mobileNo;
	}

	public void setMobileNo(BigDecimal mobileNo) {
		this.mobileNo = mobileNo;
	}

	public String getAge() {
		return age;
	}

	public void setAge(String age) {
		this.age = age;
	}

	public String getOpcrNo() {
		return opcrNo;
	}

	public void setOpcrNo(String opcrNo) {
		this.opcrNo = opcrNo;
	}

	public String getAarogyasreeCovered() {
		return aarogyasreeCovered;
	}

	public void setAarogyasreeCovered(String aarogyasreeCovered) {
		this.aarogyasreeCovered = aarogyasreeCovered;
	}

	public Integer getBedCharges() {
		return bedCharges;
	}

	public void setBedCharges(Integer bedCharges) {
		this.bedCharges = bedCharges;
	}

	public Integer getInvestigCharges() {
		return investigCharges;
	}

	public void setInvestigCharges(Integer investigCharges) {
		this.investigCharges = investigCharges;
	}

	public Integer getDrugsDispCharges() {
		return drugsDispCharges;
	}

	public void setDrugsDispCharges(Integer drugsDispCharges) {
		this.drugsDispCharges = drugsDispCharges;
	}

	public Integer getSurgProcCharges() {
		return surgProcCharges;
	}

	public void setSurgProcCharges(Integer surgProcCharges) {
		this.surgProcCharges = surgProcCharges;
	}

	public Integer getImplantCharges() {
		return implantCharges;
	}

	public void setImplantCharges(Integer implantCharges) {
		this.implantCharges = implantCharges;
	}

	public Integer getMiscCharges() {
		return miscCharges;
	}

	public void setMiscCharges(Integer miscCharges) {
		this.miscCharges = miscCharges;
	}

	public String getCurrentDate() {
		return currentDate;
	}

	public void setCurrentDate(String currentDate) {
		this.currentDate = currentDate;
	}

	public String getAmountInWords() {
		return amountInWords;
	}

	public void setAmountInWords(String amountInWords) {
		this.amountInWords = amountInWords;
	}

	public String getDistName() {
		return distName;
	}

	public void setDistName(String distName) {
		this.distName = distName;
	}

	public String getSpecialName() {
		return specialName;
	}

	public void setSpecialName(String specialName) {
		this.specialName = specialName;
	}

	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}
	
	public Integer getHospCode() {
		return hospCode;
	}

	public void setHospCode(Integer hospCode) {
		this.hospCode = hospCode;
	}

	public String getHospName() {
		return hospName;
	}

	public void setHospName(String hospName) {
		this.hospName = hospName;
	}

	public String getGender() {
		return gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public LocMlaCmrfPrintDTO(
		    String mlaLocNo2, 
		    String patientName2, 
		    String fatherSonOf2, 
		    String patientIp2,
		    String age2, 
		    String gender2, 
		    String distName2, 
		    String specialName2, 
		    String departmentName2, 
		    String address2,
		    String hospName2,
		    Integer hospCode2,
		    BigDecimal mobileNo2, 
		    String aadhaarNo2, 
		    String opcrNo2, 
		    String purpose2, 
		    int assuredAmt2, 
		    String aarogyasreeCovered2, 
		    int bedCharges2, 
		    int investigCharges2, 
		    int drugsDispCharges2,
		    int surgProcCharges2, 
		    int implantCharges2, 
		    int miscCharges2
		) {
		    this.mlaLocNo = mlaLocNo2;
		    this.patientName = patientName2;
		    this.fatherSonOf = fatherSonOf2;
		    this.patientIp = patientIp2;
		    this.age = age2;
		    this.gender = gender2;
		    this.distName = distName2 ;  // Ensure proper conversion of Object to String if needed
		    this.specialName = specialName2;
		    this.departmentName = departmentName2;
		    this.address = address2;
		    this.hospName = hospName2;
		    this.hospCode = hospCode2;
		    this.mobileNo = mobileNo2 != null ? mobileNo2 : BigDecimal.ZERO;  // Default to BigDecimal.ZERO if null
		    this.aadhaarNo = aadhaarNo2;
		    this.opcrNo = opcrNo2;
		    this.purpose = purpose2;
		    this.assuredAmt = assuredAmt2;
		    this.aarogyasreeCovered = aarogyasreeCovered2;
		    this.bedCharges = bedCharges2;
		    this.investigCharges = investigCharges2;
		    this.drugsDispCharges = drugsDispCharges2;
		    this.surgProcCharges = surgProcCharges2;
		    this.implantCharges = implantCharges2;
		    this.miscCharges = miscCharges2;
		}



}

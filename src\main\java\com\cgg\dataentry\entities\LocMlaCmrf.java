package com.cgg.dataentry.entities;

import java.sql.Timestamp;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "loc_mla_cmrf")
public class LocMlaCmrf {
	@Id
    @Column(name = "mla_loc_no")
	private String mlaLocNo;
	private String patientName;
	private String fatherSonOf;
	private String purpose;
	private Integer assuredAmt;
	private String recommendedBy;
	private Integer hospCode;
	private String status;
	private String locNo;
	private Timestamp enteredOn;
	private Boolean deleteFlag;
	private Timestamp updatedOn;
	private String rejReasons;
	private String address;
	private Date vipLetterDt;
	private String enteredBy;
	private String ipAddress;
	private String updatedBy;
	private String pendingReasons;
	private String oldFscNo;
	private String newFscNo;
	private String aadhaarNo;
	private String incomeCerNo;
	private Long mobileNo;
	private String gender;
	private String age;
	private Long patDistrict;
	private Long patMandal;
	private Long pincode;
	//private String uploadPath;
	private Long treatParId;
	private Long treatSubId;
	private Long treatProcId;
	private String opcrNo;
	private String aarogyasreeCovered;
	private Integer bedCharges;
    private Integer investigCharges;
    private Integer drugsDispCharges;
    private Integer surgProcCharges;
    private Integer implantCharges;
    private Integer miscCharges;
    private Long treatDeptId; 
    
	 
	public Integer getBedCharges() {
		return bedCharges;
	}
	public void setBedCharges(Integer bedCharges) {
		this.bedCharges = bedCharges;
	}
	public Integer getInvestigCharges() {
		return investigCharges;
	}
	public void setInvestigCharges(Integer investigCharges) {
		this.investigCharges = investigCharges;
	}
	public Integer getDrugsDispCharges() {
		return drugsDispCharges;
	}
	public void setDrugsDispCharges(Integer drugsDispCharges) {
		this.drugsDispCharges = drugsDispCharges;
	}
	public Integer getSurgProcCharges() {
		return surgProcCharges;
	}
	public void setSurgProcCharges(Integer surgProcCharges) {
		this.surgProcCharges = surgProcCharges;
	}
	public Integer getImplantCharges() {
		return implantCharges;
	}
	public void setImplantCharges(Integer implantCharges) {
		this.implantCharges = implantCharges;
	}
	public Integer getMiscCharges() {
		return miscCharges;
	}
	public void setMiscCharges(Integer miscCharges) {
		this.miscCharges = miscCharges;
	}
	public Long getTreatDeptId() {
		return treatDeptId;
	}
	public void setTreatDeptId(Long treatDeptId) {
		this.treatDeptId = treatDeptId;
	}
	public String getAarogyasreeCovered() {
		return aarogyasreeCovered;
	}
	public void setAarogyasreeCovered(String aarogyasreeCovered) {
		this.aarogyasreeCovered = aarogyasreeCovered;
	}
	public String getOpcrNo() {
		return opcrNo;
	}
	public void setOpcrNo(String opcrNo) {
		this.opcrNo = opcrNo;
	}
	public String getMlaLocNo() {
		return mlaLocNo;
	}
	public void setMlaLocNo(String mlaLocNo) {
		this.mlaLocNo = mlaLocNo;
	}
	public String getPatientName() {
		return patientName;
	}
	public void setPatientName(String patientName) {
		this.patientName = patientName;
	}
	public String getFatherSonOf() {
		return fatherSonOf;
	}
	public void setFatherSonOf(String fatherSonOf) {
		this.fatherSonOf = fatherSonOf;
	}
	public String getPurpose() {
		return purpose;
	}
	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}
	public Integer getAssuredAmt() {
		return assuredAmt;
	}
	public void setAssuredAmt(Integer assuredAmt) {
		this.assuredAmt = assuredAmt;
	}
	public String getRecommendedBy() {
		return recommendedBy;
	}
	public void setRecommendedBy(String recommendedBy) {
		this.recommendedBy = recommendedBy;
	}
	public Integer getHospCode() {
		return hospCode;
	}
	public void setHospCode(Integer hospCode) {
		this.hospCode = hospCode;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getLocNo() {
		return locNo;
	}
	public void setLocNo(String locNo) {
		this.locNo = locNo;
	}
	public Timestamp getEnteredOn() {
		return enteredOn;
	}
	public void setEnteredOn(Timestamp enteredOn) {
		this.enteredOn = enteredOn;
	}
	public Boolean getDeleteFlag() {
		return deleteFlag;
	}
	public void setDeleteFlag(Boolean deleteFlag) {
		this.deleteFlag = deleteFlag;
	}
	public Timestamp getUpdatedOn() {
		return updatedOn;
	}
	public void setUpdatedOn(Timestamp updatedOn) {
		this.updatedOn = updatedOn;
	}
	public String getRejReasons() {
		return rejReasons;
	}
	public void setRejReasons(String rejReasons) {
		this.rejReasons = rejReasons;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public Date getVipLetterDt() {
		return vipLetterDt;
	}
	public void setVipLetterDt(Date vipLetterDt) {
		this.vipLetterDt = vipLetterDt;
	}
	public String getEnteredBy() {
		return enteredBy;
	}
	public void setEnteredBy(String enteredBy) {
		this.enteredBy = enteredBy;
	}
	public String getIpAddress() {
		return ipAddress;
	}
	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
	public String getUpdatedBy() {
		return updatedBy;
	}
	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}
	public String getPendingReasons() {
		return pendingReasons;
	}
	public void setPendingReasons(String pendingReasons) {
		this.pendingReasons = pendingReasons;
	}
	public String getOldFscNo() {
		return oldFscNo;
	}
	public void setOldFscNo(String oldFscNo) {
		this.oldFscNo = oldFscNo;
	}
	public String getNewFscNo() {
		return newFscNo;
	}
	public void setNewFscNo(String newFscNo) {
		this.newFscNo = newFscNo;
	}
	public String getAadhaarNo() {
		return aadhaarNo;
	}
	public void setAadhaarNo(String aadhaarNo) {
		this.aadhaarNo = aadhaarNo;
	}
	public String getIncomeCerNo() {
		return incomeCerNo;
	}
	public void setIncomeCerNo(String incomeCerNo) {
		this.incomeCerNo = incomeCerNo;
	}
	public Long getMobileNo() {
		return mobileNo;
	}
	public void setMobileNo(Long mobileNo) {
		this.mobileNo = mobileNo;
	}
	public String getGender() {
		return gender;
	}
	public void setGender(String gender) {
		this.gender = gender;
	}
	public String getAge() {
		return age;
	}
	public void setAge(String age) {
		this.age = age;
	}
	public Long getPatDistrict() {
		return patDistrict;
	}
	public void setPatDistrict(Long patDistrict) {
		this.patDistrict = patDistrict;
	}
	public Long getPatMandal() {
		return patMandal;
	}
	public void setPatMandal(Long patMandal) {
		this.patMandal = patMandal;
	}
	public Long getPincode() {
		return pincode;
	}
	public void setPincode(Long pincode) {
		this.pincode = pincode;
	}
	/*public String getUploadPath() {
		return uploadPath;
	}
	public void setUploadPath(String uploadPath) {
		this.uploadPath = uploadPath;
	}*/
	public Long getTreatParId() {
		return treatParId;
	}
	public void setTreatParId(Long treatParId) {
		this.treatParId = treatParId;
	}
	public Long getTreatSubId() {
		return treatSubId;
	}
	public void setTreatSubId(Long treatSubId) {
		this.treatSubId = treatSubId;
	}
	public Long getTreatProcId() {
		return treatProcId;
	}
	public void setTreatProcId(Long treatProcId) {
		this.treatProcId = treatProcId;
	}
	
	
}

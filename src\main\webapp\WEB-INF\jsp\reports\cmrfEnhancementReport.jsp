<%@ page language="java" import="java.util.*" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<%
String path = request.getContextPath();
String basePath = path + "/";
%>

<!DOCTYPE HTML>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="">
<meta name="author" content="">
<title> CMRF Enhancement Report</title>

<link rel="stylesheet" href="css/jquery-ui.css">
<link rel="stylesheet" type="text/css" href="css/dataTables.bootstrap.min.css"/>
<script src="js/jquery.min.js"></script>
<script src="js/jquery.validate.js"></script>
<script type="text/javascript" src="js/dataTables.bootstrap.min.js"></script>
<jsp:include page="/WEB-INF/jsp/include_pdfxl.jsp"/>
<jsp:include page="/WEB-INF/jsp/include_DT.jsp" /> 
<link rel="stylesheet" href="css/flatpickr.min.css">
<script src="js/flatpickr.min.js"></script>

<style type="text/css">
	.display thead tr th {
		text-align: center;
	}
	
	.dataTables_wrapper {
		align: center;
	}
	
	.TFtable dataTable no-footer {
		width: 50%;
	}
	
	#contents {
		width: 800px;
	}
	
	#contents table {
		width: 390px;
	}
	
	#table_one {
		float: left;
	}
	
	@media print {
		.print-only {
			display: inline;
		}
		.no-print {
			display: none;
		}
	}
	
	@media screen {
		.print-only {
			display: none;
		}
	}
</style>

<script type="text/javascript">

function validateData() {
		var dateFrom = $('#dateFrom').val();
		var dateTo = $('#dateTo').val();
		var reportType = $('#reportType').val();

		var dateFromObj = parseDate(dateFrom);
		var dateToObj = parseDate(dateTo);

		if (!dateFrom || dateFrom === '') {
			Swal.fire({
				icon : 'error',
				title : 'Error',
				text : 'Enter From Date',
			});
			return false;
		} else if (!dateTo || dateTo === '') {
			Swal.fire({
				icon : 'error',
				title : 'Error',
				text : 'Enter To Date',
			});
			return false;
		} else if (dateFromObj > dateToObj) {
			Swal
					.fire({
						title : "To Date should be greater than or equal to From Date.",
						icon : "warning",
						confirmButtonText : "Ok"
					})
			return false;
		} else if (reportType === '') {
			Swal.fire({
				title : "Please Select the Report Type.",
				icon : "warning",
				confirmButtonText : "Ok"
			})
			return false;
		}
		return true;
}


function parseDate(dateStr) {
	var parts = dateStr.split('-');
	return new Date(parts[2], parts[1] - 1, parts[0]); 
}


document.addEventListener('DOMContentLoaded', function() {
	flatpickr('#dateFrom', {
		dateFormat : "d-m-Y",
		defaultDate : "today",
		minDate : "07-12-2023",
		maxDate : new Date(),
		allowInput : true
	});
	flatpickr('#dateTo', {
		dateFormat : "d-m-Y",
		defaultDate : "today",
		minDate : "07-12-2023",
		maxDate : new Date(),
		allowInput : true
	});
});



$(document).ready(function() {
	$('#deoTokenVerifiedList').DataTable({
		dom : 'Bfrtip',
		"paging" : true,
		lengthMenu : [
				[ 10, 20, 50, 100, -1 ],
				[ 10, 20, 50, 100, 'All' ] ],
		buttons : [
				{
					extend : 'print',
					title : 'CMRF Enhancement Report',
					text : '<i class="fa fa-print"></i>',
					orientation : 'landscape',
					pageSize : 'A4',
					footer : true
				},
				{
					extend : 'excelHtml5',
					title : 'CMRF Enhancement Report',
					text : '<i class="fa fa-file-excel-o"></i>',
					titleAttr : 'Export to Excel',
					orientation : 'landscape',
					pageSize : 'A4',
					footer : true
				},
				{
					extend : 'pdfHtml5',
					title : 'CMRF Enhancement Report',
					text : '<i class="fa fa-file-pdf-o"></i>',
					titleAttr : 'Export to PDF',
					orientation : 'landscape',
					pageSize : 'A4',
					footer : true
				},
				{
					extend : 'colvis',
					text : '<i class="fa fa-list"></i>',
					titleAttr : 'Show/Hide Columns'
				},
				{
					extend : 'pageLength',
					titleAttr : 'Page Length',
				}],
			"footerCallback" : function(row,data, start, end, display) {
					var api = this.api();
					var parseValue = function(val) {
						return typeof val === 'string' ? parseFloat(val.replace(/,/g, '')): (typeof val === 'number' ? val: 0);
					};

					var total1 = api.column(6, {page : 'current'})
					   .data().reduce(function(acc,curr) {
										return parseValue(acc)+ parseValue(curr);
									}, 0);
					$(api.column(6).footer())
					    .html(total1.toLocaleString('en-IN'));

					var total2 = api.column(7, {page : 'current'})
						.data().reduce(function(acc,curr) {
								return parseValue(acc)+ parseValue(curr);
							}, 0);
					$(api.column(7).footer()).html(total2.toLocaleString('en-IN'));
			},			
			initComplete : function() {$('#deoTokenVerifiedList_filter input').focus();}
			});

	
		$('#prWiseList').DataTable({
				dom : 'Bfrtip',
				"paging" : true,
				lengthMenu : [
						[ 10, 20, 50, 100, -1 ],
						[ 10, 20, 50, 100, 'All' ] ],
				pageLength : -1,
				buttons : [
						{
							extend : 'print',
							title : 'CMRF Enhancement Report',
							text : '<i class="fa fa-print"></i>',
							orientation : 'landscape',
							pageSize : 'A4',
							footer : true
						},
						{
							extend : 'excelHtml5',
							title : 'CMRF Enhancement Report',
							text : '<i class="fa fa-file-excel-o"></i>',
							titleAttr : 'Export to Excel',
							orientation : 'landscape',
							pageSize : 'A4',
							footer : true
						},
						{
							extend : 'pdfHtml5',
							title : 'CMRF Enhancement Report',
							text : '<i class="fa fa-file-pdf-o"></i>',
							titleAttr : 'Export to PDF',
							orientation : 'landscape',
							pageSize : 'A4',
							footer : true
						},
						{
							extend : 'colvis',
							text : '<i class="fa fa-list"></i>',
							titleAttr : 'Show/Hide Columns'
						},
						{
							extend : 'pageLength',
							titleAttr : 'Page Length',
						} ],
						"footerCallback" : function(row,data, start, end, display) {
							var api = this.api();
							var parseValue = function(val) {
								return typeof val === 'string' ? parseFloat(val.replace(/,/g, '')): (typeof val === 'number' ? val: 0);
							};

							var total = api.column(2, {page : 'current'})
								.data().reduce(function(acc,curr) {
											return parseValue(acc)+ parseValue(curr);
								}, 0);
							$(api.column(2).footer()).html(total.toLocaleString('en-IN'));

						},
					   initComplete : function() {
							$('#prWiseList_filter input').focus();
					  }
			});
});

</script>
</head>


<body>
	
	<div class="complete_wrap">
		
        <c:if test="${not empty msg}">
	          <br>
	             <div class="col-sm-12">
	               <div style="color:red;font-size: 20px;" class="text-center"><b>${msg}</b></div>
	            </div>
	         <br>
        </c:if>
		
		<div class="container" style="background: white; margin: 20px auto; width: 55%; border-top: 5px solid #515458; border-radius: 5px; box-shadow: 1px 1px 10px;" id="dateForm">
			<h3 class="text-center">CMRF Enhancement Report</h3>
			
			<c:if test="${not empty error}">
                 <div class="alert alert-danger" role="alert">${error}</div>
           </c:if>
			
			<form id="deoVerifiedStatusForm" action="cmrfEnhancementReport" method="POST" modelAttribute="deoVerifiedStatusForm">					
				<div class="row">
					<div class="col-sm-6">
						<div class="form-group" style="margin-top: 20px;">
							<label for="cmrfNo" class="col-sm-6 col-form-label">From Date <span style="color: red;">*</span></label>
							 <input type="text" class="form-control" id="dateFrom" placeholder="dd-mm-yyyy" name="dateFrom">
						</div>
					</div>
					
					<div class="col-sm-6">
						<div class="form-group" style="margin-top: 20px;">
							<label for="cmrfNo" class="col-sm-6 col-form-label">To Date <span style="color: red;">*</span></label>
							<input type="text" class="form-control" id="dateTo" placeholder="dd-mm-yyyy" name="dateTo">
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-sm-6">
						<div class="form-group">
							<label for="date-id">Report Type:</label>
							<select name="reportType" id="reportType" class="form-control">
								<option value="">Select</option>
								<option value="TotalCases">Total Cases</option>
								<option value="PRWise">People Representative Wise Cases</option>
							</select>
						</div>
					</div>
				</div>
						
				<div class="text-center">
					<button class="btn btn-primary" onclick="return validateData();">Submit</button>
				</div>
			<br>
		</form>		
	</div>

	<br/><br/>
		
		
	<c:if test="${not isPRWise and not empty enhancementReportDetailsList}">		
	    <div  style="background: white; margin: 20px; padding: 20px; border-top: 5px solid #40295b;overflow: scroll;" >
			
			<c:if test="${isPRWiseBack}">
				<div style="display: flex; justify-content: end;">
					<button type="button" class="btn btn-success" onclick="window.history.go(-1);">Back</button>
				</div>
			</c:if>
		  
			<h3 style="text-align: center;">CMRF Enhancement Report</h3>
			<h4 style="text-align: right;color:red;">From ${dateFrom} - To ${dateTo}</h4>
								
			<hr>		
			   
			<table border="2" align="center" style="font-size: 13px;" class="display" id="deoTokenVerifiedList">
					<thead style="background: #563d7c;color: white;">
						<tr>
							<th>Sl No.</th>				           
							<th>CMRF No</th>
				            <th>Patient Name</th>
				            <th>Father Son Of</th>
							<th>People Representative Name</th>
							<th>Hospital</th>
				            <th>Requested Amount</th>	
							<th>Sanctioned Amount</th>									           
						</tr>
					</thead>							
					<tbody>
						<c:forEach var="monthlySanReport" items="${enhancementReportDetailsList}" varStatus="row">
							<tr>
							    <td>${row.index+1}</td>
				                <td>${monthlySanReport.cmrf_no}</td>														
				                <td>${monthlySanReport.pat_name}</td>
			               		<td>${monthlySanReport.father_son_of}</td>
								<td>${monthlySanReport.mlamp}</td>
								<td>${monthlySanReport.hospname}</td>
								<td align="right">${monthlySanReport.req_amt}</td>
								<td align="right">${monthlySanReport.sanc_amt}</td>									               				               		           
							</tr>
						</c:forEach>
					</tbody>
					<tfoot>
						<th colspan="6">Total</th>
						<th style="text-align: right;"></th>
						<th style="text-align: right;"></th>						
					</tfoot>											        
			</table>
	     </div>	 
	 </c:if>
	 
	 	 
    <br/><br/>
		
		
	<c:if test="${isPRWise and not empty enhancementReportDetailsList}">
			<div class="container" style="background: white; border-top: 5px solid #40295b;" >
				<h3 style="text-align: center;">People Representative Wise Enhancement Report</h3>
				<h4 style="text-align: right;color:red;">From ${dateFrom} - To ${dateTo}</h4>
				<hr>		
					
				<table border="2" align="center" style="font-size: 13px;" class="display" id="prWiseList">
					<thead style="background: #563d7c;color: white;">
						<tr>
							<th>Sl No.</th>
							<th>People Representative Name & Constituency</th>
							<th>No. of Cases</th>
						</tr>
					</thead>		
					<tbody>
						<c:forEach var="prWiseList" items="${enhancementReportDetailsList}" varStatus="row">
							<tr>
								<td>${row.index+1}</td>
								<td><a href="getPRWiseEnhDetails?cno=${prWiseList.constNo}&dateFrom=${prWiseList.dateFrom}&dateTo=${prWiseList.dateTo}&OtherConst=${prWiseList.otherConst}&hideForm=true">${prWiseList.vipName}</a></td>	
								<td style="text-align: right;">${prWiseList.cases}</td>
							</tr>
						</c:forEach>
					</tbody>
					<tfoot>
						<th colspan="2">Total</th>
						<th style="text-align: right;"></th>
					</tfoot>        
				</table>
			</div>
		</c:if>
			
	</div>
</body>
</html>
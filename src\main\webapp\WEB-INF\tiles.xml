<?xml version="1.0" encoding="ISO-8859-1" ?>
<!DOCTYPE tiles-definitions PUBLIC
       "-//Apache Software Foundation//DTD Tiles Configuration 3.0//EN"
       "http://tiles.apache.org/dtds/tiles-config_3_0.dtd">

<tiles-definitions>
	<definition name="base.definition" template="/WEB-INF/tiles/siteLayout.jsp">
		<put-attribute name="header" value="/WEB-INF/tiles/header.jsp" />
		<put-attribute name="menu" value="/WEB-INF/tiles/menu.jsp" />
		<put-attribute name="footer" value="/WEB-INF/tiles/footer.jsp" />
	</definition>

	<definition name="welcome" extends="base.definition">
		<put-attribute name="title" value="CMRF Welcome"/>
		<put-attribute name="body" value="/WEB-INF/jsp/welcome.jsp"/>
	</definition>
	
	<definition name="changePassword" extends="base.definition">
		<put-attribute name="title" value="Change Password"/>
		<put-attribute name="body" value="/WEB-INF/jsp/changePassword.jsp"/>
	</definition>
	
	<!-- Hospital  :: start -->
	
	
	<definition name="inwardCmrf" extends="base.definition">
		<put-attribute name="title" value="Inward Applicant Details/ Token Generation"/>
		<!-- <put-attribute name="body" value="/WEB-INF/jsp/inwardCmrf.jsp"/> -->
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/inwardCmrf.jsp"/>
	</definition>
	<definition name="mlaInwardCmrf" extends="base.definition">
		<put-attribute name="title" value="Inward Applicant Details/ Token Generation"/>
		<!-- <put-attribute name="body" value="/WEB-INF/jsp/inwardCmrf.jsp"/> -->
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/mlaInwardCmrf.jsp"/>
	</definition>
	<definition name="inwardCMRFEntryStatusReportNew" extends="base.definition">
		<put-attribute name="title" value="CMRF Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/inwardCMRFEntryStatusReportNew.jsp"/>
	</definition>
	<definition name="hospWisePendingReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/hospWisePendingReport.jsp"/>
	</definition>
	<definition name="hospitalDetailsReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/hospitalDetailsReport.jsp"/>
	</definition>	
	<definition name="inwardCMRFReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/inwardCMRFReport.jsp"/>
	</definition>
	<definition name="inwardCMRFReportView" extends="base.definition">
		<put-attribute name="title" value="CMRF Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/inwardCMRFReportView.jsp"/>
	</definition>
	<definition name="updateTokenCmrf" extends="base.definition">
		<put-attribute name="title" value="Update Cmrf Token Details"/>
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/updateTokenCmrf.jsp"/>
	</definition>
	
	<!-- <definition name="UpdateCMRFTokenStatus" extends="base.definition">
		<put-attribute name="title" value="Update Cmrf Token Details"/>
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/UpdateCMRFTokenStatus.jsp"/>
	</definition> -->
	
	<definition name="updPatientIp" extends="base.definition">
		<put-attribute name="title" value="Update Patient IP Details"/>
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/updPatientIp.jsp"/>
	</definition> 
	
	<definition name="updPatientIpDetails" extends="base.definition">
		<put-attribute name="title" value="Update Hospital Patient IP Details"/>
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/updPatientIpDetails.jsp"/>
	</definition>
	
	<definition name="mlaCMRFStatusReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Status Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/mlaCMRFStatusReport.jsp"/>
	</definition>
	
	<definition name="hospUpdDtls" extends="base.definition">
		<put-attribute name="title" value="Hospital Update Details Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/hospUpdDtls.jsp"/>
	</definition>
	
	<definition name="cmrfTokenDetailsReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Token Details Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/cmrfTokenDetailsReport.jsp"/>
	</definition>
	
	<definition name="cmrfTokenReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Token Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/cmrfTokenReport.jsp"/>
	</definition>
	<definition name="cmrfEnquiryReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Enquiry Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/cmrfEnquiryReport.jsp"/>
	</definition>
	<definition name="cmrfEnquiryReportForMLA" extends="base.definition">
		<put-attribute name="title" value="CMRF Enquiry Report For MLA"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/cmrfEnquiryReportForMLA.jsp"/>
	</definition>
	<!--  Hospital :: end -->
	
	
	<!-- CMRF :: start -->
	<definition name="CMRFUpdateDetailsReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Update Details Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/CMRFUpdateDetailsReport.jsp"/>
	</definition>
	
	<definition name="sendData" extends="base.definition">
		<put-attribute name="title" value="Send CMRF Details"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/sendData.jsp"/>
	</definition> 
	
	<definition name="partyWise" extends="base.definition">
		<put-attribute name="title" value="Parywise Details"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/partyWise.jsp"/>
	</definition>
	
	<definition name="partyWiseReport" extends="base.definition">
		<put-attribute name="title" value="Parywise Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/partyWiseReport.jsp"/>
	</definition>
	
	<definition name="cmrfEsignedReport" extends="base.definition">
		<put-attribute name="title" value="CMRF esigned Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/cmrfEsignedReport.jsp"/>
	</definition>
	
	<definition name="vipWiseCasesForm" extends="base.definition">
		<put-attribute name="title" value="VIP wise cases form"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/vipWiseCasesForm.jsp"/>
	</definition>
	
	<definition name="vipWiseSanctionedCasesReport" extends="base.definition">
		<put-attribute name="title" value="VIP wise sanctioned cases Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/vipWiseSanctionedCasesReport.jsp"/>
	</definition>
	
	<definition name="locMLACmrfEntry" extends="base.definition">
		<put-attribute name="title" value="Loc CMRF Entry"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/locMLACmrfEntry.jsp"/>
	</definition>

	<definition name="mlaLoc" extends="base.definition">
		<put-attribute name="title" value="Loc CMRF Entry"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/MlaLocEntry.jsp"/>
	</definition>
	
	<definition name="editLocTokenDetails" extends="base.definition">
		<put-attribute name="title" value="Edit Loc Token Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/editLocTokenDetails.jsp"/>
	</definition>
	
	<definition name="moveCmrfData" extends="base.definition">
		<put-attribute name="title" value="Move CMRF Data"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/moveCmrfData.jsp"/>
	</definition>
	
	<definition name="constituencyQueryReport" extends="base.definition">
		<put-attribute name="title" value="Constituency Query Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/query/constituencyQueryReport.jsp"/>
	</definition>
	
	<definition name="hospitalQueryReport" extends="base.definition">
		<put-attribute name="title" value="Hospital Query Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/query/hospitalQueryReport.jsp"/>
	</definition>
	
	<definition name="mpMlaQueryReport" extends="base.definition">
		<put-attribute name="title" value="Mla Mp Query Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/query/mpMlaQueryReport.jsp"/>
	</definition>
	
	<definition name="LOCReport" extends="base.definition">
		<put-attribute name="title" value="Loc Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/LOCReport.jsp"/>
	</definition>
	
	<definition name="LocDetailedReport" extends="base.definition">
		<put-attribute name="title" value="Loc Detailed Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/LocDetailedReport.jsp"/>
	</definition>
	
	<definition name="LOCLetter" extends="base.definition">
		<put-attribute name="title" value="Loc Letter"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/LOCLetter.jsp"/>
	</definition>
	<definition name="PrintAllLOCLetters" extends="base.definition">
		<put-attribute name="title" value="Loc Letter"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/PrintAllLOCLetters.jsp"/>
	</definition>
	
	<definition name="editLOCDetails" extends="base.definition">
		<put-attribute name="title" value="Edit Loc Details"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/editLOCDetails.jsp"/>
	</definition>
	
	<definition name="LOCAbstractReport" extends="base.definition">
		<put-attribute name="title" value="Loc Abstract Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/LOCAbstractReport.jsp"/>
	</definition>

	<definition name="LOCSanctionedReport" extends="base.definition">
		<put-attribute name="title" value="LOC Sanctioned Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/LOCSanctionedReport.jsp"/>
	</definition>

	<definition name="CMRFSanctionedReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Sanctioned Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/CMRFSanctionedReport.jsp"/>
	</definition>
	
	<definition name="vipLocAbstractReport" extends="base.definition">
		<put-attribute name="title" value="Vip Loc Abstract Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/vipLocAbstractReport.jsp"/>
	</definition>
	
	<definition name="vipWiseChequeIssuesCases" extends="base.definition">
		<put-attribute name="title" value="Vip wise Cheque Issues Cases"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/vipWiseChequeIssuesCases.jsp"/>
	</definition>
	
	<definition name="constituencyDetails" extends="base.definition">
		<put-attribute name="title" value="Constituency Details"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/constituencyDetails.jsp"/>
	</definition>
	
	<definition name="vipWiseChequeIssuesCasesReport" extends="base.definition">
		<put-attribute name="title" value="Vip wise Cheque Issues Cases report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/vipWiseChequeIssuesCasesReport.jsp"/>
	</definition>
	
	<definition name="districtWiseRecommendedCases" extends="base.definition">
		<put-attribute name="title" value="Districtwise Recommended Cases"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/districtWiseRecommendedCases.jsp"/>
	</definition>
	
	<definition name="HospitalWiseIssuedCases" extends="base.definition">
		<put-attribute name="title" value="Hospital Wise Issued Cases"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/HospitalWiseIssuedCases.jsp"/>
	</definition>
	
	<definition name="constituencyWiseRecommended" extends="base.definition">
		<put-attribute name="title" value="Constituency Wise Recommended"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/constituencyWiseRecommended.jsp"/>
	</definition>
	
	<definition name="LOCMLACMRFEntryReport" extends="base.definition">
		<put-attribute name="title" value="Loc Mla CMRF Entry Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/LOCMLACMRFEntryReport.jsp"/>
	</definition>
	
	<definition name="LOCCMRFStatusEntryReport" extends="base.definition">
		<put-attribute name="title" value="Loc CMRF Status entry Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/LOCCMRFStatusEntryReport.jsp"/>
	</definition>
	
	<definition name="locDetailsEntry" extends="base.definition">
		<put-attribute name="title" value="Loc Details Entry"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/locDetailsEntry.jsp"/>
	</definition>
	
	<definition name="locLetter" extends="base.definition">
		<put-attribute name="title" value="Loc letter"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/locLetter.jsp"/>
	</definition>
	
	<definition name="updateLocMlaCmrfEntry" extends="base.definition">
		<put-attribute name="title" value="Update Loc CMRF Details"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/updateLocMlaCmrfEntry.jsp"/>
	</definition>
	
	<definition name="hosLocReport" extends="base.definition">
		<put-attribute name="title" value="Hospital Loc Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/hosLocReport.jsp"/>
	</definition>
	
	<definition name="mlaInwardCmrfEdit" extends="base.definition">
		<put-attribute name="title" value="PR Inward Cmrf Edit"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/mlaInwardCmrfEdit.jsp"/>
	</definition>
	
	<definition name="hospitalReport" extends="base.definition">
		<put-attribute name="title" value="Hospital Loc Details Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/hospitalReport.jsp"/>
	</definition>
	
	<definition name="enhancementLocDetailsEntry" extends="base.definition">
		<put-attribute name="title" value="Enhancement Loc Details"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/enhancementLocDetailsEntry.jsp"/>
	</definition> 
	
	<definition name="enhancementLocLetter" extends="base.definition">
		<put-attribute name="title" value="Enhance,emtl Loc Letter"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/enhancementLocLetter.jsp"/>
	</definition>
	
	<definition name="addCmrfEntry" extends="base.definition">
		<put-attribute name="title" value="Add CMRF Entry"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/addCmrfEntry.jsp"/>
	</definition> 
	
	<definition name="updateCmrfEntry" extends="base.definition">
		<put-attribute name="title" value="Update CMRF Entry"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/updateCmrfEntry.jsp"/>
	</definition> 
	
	<definition name="revenueDirectExgratia" extends="base.definition">
		<put-attribute name="title" value="e-Sign"/>
		<put-attribute name="body" value="/WEB-INF/jsp/proceedings/revenueDirectExgratia.jsp"/>
	</definition>
	
	<definition name="monthWiseSummary" extends="base.definition">
		<put-attribute name="title" value="Month Wise Summary Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/monthWiseSummary.jsp"/>
	</definition>  
	
	<definition name="districtWiseAbstractReport" extends="base.definition">
		<put-attribute name="title" value="Districtwise All Cases Abstract Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/districtWiseAbstractReport.jsp"/>
	</definition>
	
	<definition name="districtWiseAbstractReportDrillDown" extends="base.definition">
		<put-attribute name="title" value="Districtwise All Cases Abstract Drill down Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/districtWiseAbstractReportDrillDown.jsp"/>
	</definition>
	
	<definition name="cmpReport" extends="base.definition">
		<put-attribute name="title" value="CMRF /CMP Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/cmpReport.jsp"/>
	</definition>
	
	<definition name="CMRFPatientTokenStatusReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Patient Token Status Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/CMRFPatientTokenStatusReport.jsp"/>
	</definition> 
	
	<definition name="CMRFDoctorsTokensApprove" extends="base.definition">
		<put-attribute name="title" value="CMRF Patient Token Status Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/CMRFDoctorsTokensApprove.jsp"/>
	</definition> 
	
	<definition name="CMRFPatientTokenStatusForm" extends="base.definition">
		<put-attribute name="title" value="CMRF Patient Token Status Form"/>
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/CMRFPatientTokenStatusForm.jsp"/>
	</definition>
	
	<definition name="districtWiseAbstractForm" extends="base.definition">
		<put-attribute name="title" value="Distric wise Personally Representative Form"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/districtWiseAbstractForm.jsp"/>
	</definition> 
	
	<definition name="DistrictWiseAbstractPRCasesReport" extends="base.definition">
		<put-attribute name="title" value="Distric wise Personally Representative Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/DistrictWiseAbstractPRCasesReport.jsp"/>
	</definition> 
	
	<definition name="DistrictConstituencyWiseDetailsForm" extends="base.definition">
		<put-attribute name="title" value="District Constituency wise Recommended cases Form"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/DistrictConstituencyWiseDetailsForm.jsp"/>
	</definition>
	
	<definition name="DistrictConstituencyWiseDetailsRCReport" extends="base.definition">
		<put-attribute name="title" value="District Constituency wise Recommended cases Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/DistrictConstituencyWiseDetailsRCReport.jsp"/>
	</definition>
	
	<definition name="directExgratia" extends="base.definition">
		<put-attribute name="title" value="Leter to Beneficiary"/>
		<put-attribute name="body" value="/WEB-INF/jsp/proceedings/directExgratia.jsp"/>
	</definition>
	
	<definition name="directExgCmrfList" extends="base.definition">
		<put-attribute name="title" value="Leter to Beneficiary List"/>
		<put-attribute name="body" value="/WEB-INF/jsp/proceedings/directExgCmrfList.jsp"/>
	</definition>
	
	<definition name="beneficiaryCheques" extends="base.definition">
		<put-attribute name="title" value="Leter to Beneficiary Cheques"/>
		<put-attribute name="body" value="/WEB-INF/jsp/proceedings/beneficiaryCheques.jsp"/>
	</definition>
	
	<definition name="officeCopy" extends="base.definition">
		<put-attribute name="title" value="Office Copy"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/officeCopy.jsp"/>
	</definition>
	
	<definition name="cmrfTokensReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Tokem Report / Office Copy"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/cmrfTokensReport.jsp"/>
	</definition>
	
	<definition name="detailedSanctionedCases" extends="base.definition">
		<put-attribute name="title" value="CMRF Tokem Report / Office Copy"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/detailedSanctionedCases.jsp"/>
	</definition>
	
	<definition name="sanctionStatement" extends="base.definition">
		<put-attribute name="title" value="CMRF Sanction Statement"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/sanctionStatement.jsp"/>
	</definition>
	
	<definition name="sanctionStatementReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Sanction Statement Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/sanctionStatementReport.jsp"/>
	</definition>
	
	<definition name="sancAmtUpdReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Sanction Statement Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/sancAmtUpdReport.jsp"/>
	</definition>
	
	<definition name="revenueDraftCopy" extends="base.definition">
		<put-attribute name="title" value="Revenue Copy"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/revenueDraftCopy.jsp"/>
	</definition>
	
	<definition name="revenueDraftCopyReport" extends="base.definition">
		<put-attribute name="title" value="Revenue Copy Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/revenueDraftCopyReport.jsp"/>
	</definition>
	
	<definition name="sanctionedCases" extends="base.definition">
		<put-attribute name="title" value="Sanctioned Cases"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/sanctionedCases.jsp"/>
	</definition>
	
	<definition name="sanctionedCasesReport" extends="base.definition">
		<put-attribute name="title" value="Sanctioned Cases Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/sanctionedCasesReport.jsp"/>
	</definition>
	
	<definition name="MLACMRFConsolidateForm" extends="base.definition">
		<put-attribute name="title" value="CMRF Consolidated Form"/>
		<put-attribute name="body" value="/WEB-INF/jsp/query/MLACMRFConsolidateForm.jsp"/>
	</definition>
	
	<definition name="MLACMRFConsolidateReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Consolidated Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/query/MLACMRFConsolidateReport.jsp"/>
	</definition>
	
	<definition name="CMRFHospitalStatusReportForm" extends="base.definition">
		<put-attribute name="title" value="CMRF Hospital Status Form"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/CMRFHospitalStatusReportForm.jsp"/>
	</definition>
	
	<definition name="monthWiseChequesReceivedByPRForm" extends="base.definition">
		<put-attribute name="title" value="Month Wise Cheques Received By PR"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/monthWiseChequesReceivedByPRForm.jsp"/>
	</definition>
	
	<definition name="CMRFHospitalStatusReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Hospital Status Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/CMRFHospitalStatusReport.jsp"/>
	</definition>
	
	<definition name="MLACMRFEntyDetailsForm" extends="base.definition">
		<put-attribute name="title" value="CMRF Entry Details Form"/>
		<put-attribute name="body" value="/WEB-INF/jsp/query/MLACMRFEntyDetailsForm.jsp"/>  <!-- MLACMRFEntyDetailsForm.jsp -->
	</definition>
	
	<definition name="InwardCMRFPatientStatusReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Patient Status Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/query/inwardCMRFPatientStatusReport.jsp"/>  <!-- MLACMRFPatientStatusReport.jsp -->
	</definition>
	
	<definition name="deoVerifiedPatientStatusForm" extends="base.definition">
		<put-attribute name="title" value="DEO Verified Patient status Form"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/deoVerifiedPatientStatusForm.jsp"/>
	</definition>
	
	<definition name="deoVerifiedPatientStatusReport" extends="base.definition">
		<put-attribute name="title" value="DEO Verified Patient status Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/deoVerifiedPatientStatusReport.jsp"/>
	</definition>
	
	<definition name="CMRFPatientStatusReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Patient Status Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/query/CMRFPatientStatusReport.jsp"/>
	</definition>
	
	<definition name="lettersToHospitals" extends="base.definition">
		<put-attribute name="title" value="Leter to Hospital"/>
		<put-attribute name="body" value="/WEB-INF/jsp/proceedings/lettersToHospitals.jsp"/>
	</definition>
	
	<definition name="letterHospUpdateGrid" extends="base.definition">
		<put-attribute name="title" value="Leter to Hospital List"/>
		<put-attribute name="body" value="/WEB-INF/jsp/proceedings/letterHospUpdateGrid.jsp"/>
	</definition>
	
	<definition name="addCmpNo" extends="base.definition">
		<put-attribute name="title" value="Add Cmp No"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/addCmpNo.jsp"/>
	</definition> 
	
	<definition name="addHospital" extends="base.definition">
		<put-attribute name="title" value="Add Hospital"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/addHospital.jsp"/>
	</definition>

	<definition name="CTRLNoWiseChequeDetails" extends="base.definition">
		<put-attribute name="title" value="Control Number Wise Cheque Details Report" />
		<put-attribute name="body" value="/WEB-INF/jsp/reports/CTRLNoWiseChequeDetails.jsp" />
	</definition>

	<definition name="cmrfAppLetter" extends="base.definition">
		<put-attribute name="title" value="CMRF Application"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/cmrfAppLetter.jsp"/>
	</definition>

	<definition name="cmrfPRLetter" extends="base.definition">
		<put-attribute name="title" value="People Representative's Letter"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/cmrfPRLetter.jsp"/>
	</definition>
	
	<!--  CMRF :: end -->
	<!-- cash book::start -->
	<definition name="CashBookEntriesForm" extends="base.definition">
		<put-attribute name="title" value="Cash book Entries"/>
		<put-attribute name="body" value="/WEB-INF/jsp/donor/CashBookEntriesForm.jsp"/>
	</definition> 
	<definition name="CashBookEntriesReport" extends="base.definition">
		<put-attribute name="title" value="Cash book Entries Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/donor/CashBookEntriesReport.jsp"/>
	</definition> 
	<definition name="CashBookEntry" extends="base.definition">
		<put-attribute name="title" value="Cash book Entry Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/donor/CashBookEntry.jsp"/>
	</definition>
	<definition name="CashBookSearchForm" extends="base.definition">
		<put-attribute name="title" value="Cash book Search Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/donor/CashBookSearchForm.jsp"/>
	</definition>
	<definition name="CashBookSearchReport" extends="base.definition">
		<put-attribute name="title" value="Cash book Search Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/donor/CashBookSearchReport.jsp"/>
	</definition>
	<!-- cash book::end -->
	<!-- donor::start -->
	<definition name="addDonor" extends="base.definition">
		<put-attribute name="title" value="Add Donor"/>
		<put-attribute name="body" value="/WEB-INF/jsp/donor/addDonor.jsp"/>
	</definition>
	<definition name="DLetter" extends="base.definition">
		<put-attribute name="title" value="DLetter"/>
		<put-attribute name="body" value="/WEB-INF/jsp/donor/DLetter.jsp"/>
	</definition>
	<definition name="donatersReport" extends="base.definition">
		<put-attribute name="title" value="Donaters Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/donor/donatersReport.jsp"/>
	</definition>
	<definition name="donatersLettersReport" extends="base.definition">
		<put-attribute name="title" value="Donaters Letters Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/donor/donatersLettersReport.jsp"/>
	</definition>
	<definition name="searchDonor" extends="base.definition">
		<put-attribute name="title" value="Search Donor"/>
		<put-attribute name="body" value="/WEB-INF/jsp/donor/searchDonor.jsp"/>
	</definition>
	<definition name="getSearchDonor" extends="base.definition">
		<put-attribute name="title" value="Get Search Donor"/>
		<put-attribute name="body" value="/WEB-INF/jsp/donor/getSearchDonor.jsp"/>
	</definition>
	<definition name="generateDonorsLets" extends="base.definition">
		<put-attribute name="title" value="Generate Donors Lets"/>
		<put-attribute name="body" value="/WEB-INF/jsp/donor/generateDonorsLets.jsp"/>
	</definition>
	<definition name="updateDonation" extends="base.definition">
		<put-attribute name="title" value="Update Donation"/>
		<put-attribute name="body" value="/WEB-INF/jsp/donor/updateDonation.jsp"/>
	</definition>
	<!-- donor::end -->
	<!-- previous proceedings::start -->
	<definition name="previousHospital" extends="base.definition">
		<put-attribute name="title" value="Previous Hospital"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/previousHospital.jsp"/>
	</definition>
	<definition name="previousHospitalsReport" extends="base.definition">
		<put-attribute name="title" value="Previous Hospitals Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/previousHospitalsReport.jsp"/>
	</definition>
	<definition name="previousDirectExGratia" extends="base.definition">
		<put-attribute name="title" value="Previous Direct ExGratia"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/previousDirectExGratia.jsp"/>
	</definition>
	<definition name="previousUpdateGridForm" extends="base.definition">
		<put-attribute name="title" value="previous Update GridForm"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/previousUpdateGridForm.jsp"/>
	</definition>
	<definition name="DMReportForm" extends="base.definition">
		<put-attribute name="title" value="DMReport Form"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/DMReportForm.jsp"/>
	</definition>
	<definition name="DMReportDisplay" extends="base.definition">
		<put-attribute name="title" value="DM Report Display"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/DMReportDisplay.jsp"/>
	</definition>
	<definition name="DMReportsData" extends="base.definition">
		<put-attribute name="title" value="DM Reports Data"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/DMReportsData.jsp"/>
	</definition>
	<!-- previous proceedings::end -->
	<definition name="PreviousDirectBeneficiaryChequesForm" extends="base.definition">
		<put-attribute name="title" value="Previous Direct Beneficiary Cheques Form"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/PreviousDirectBeneficiaryChequesForm.jsp"/>
	</definition>
	<definition name="PreviousDirectBeneficiaryChequesReport" extends="base.definition">
		<put-attribute name="title" value="Previous Direct Beneficiary Cheques Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/PreviousDirectBeneficiaryChequesReport.jsp"/>
	</definition>
	<definition name="HospitalWiseAllPendingPayments" extends="base.definition">
		<put-attribute name="title" value="Hospital Wise All Pending Payments"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/HospitalWiseAllPendingPayments.jsp"/>
	</definition>
	<definition name="HospitalWiseAllPendingPaymentsReport" extends="base.definition">
		<put-attribute name="title" value="Hospital Wise All Pending Payments Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/HospitalWiseAllPendingPaymentsReport.jsp"/>
	</definition>
	<definition name="StatementReportBetweenDates" extends="base.definition">
		<put-attribute name="title" value="Statement Report Between Dates"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/StatementReportBetweenDates.jsp"/>
	</definition>
	<definition name="StatementStatusReport" extends="base.definition">
		<put-attribute name="title" value="Statement Status Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/StatementStatusReport.jsp"/>
	</definition>
		<definition name="CMRFStatementLetter" extends="base.definition">
		<put-attribute name="title" value="CMRF Statement Letter"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/CMRFStatementLetter.jsp"/>
	</definition>
	<definition name="LettersTomla" extends="base.definition">
		<put-attribute name="title" value="Letters To MLA"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/LettersTomla.jsp"/>
	</definition>
		<definition name="BetweenDatesSanctionStatement" extends="base.definition">
		<put-attribute name="title" value="Between Dates Sanction Statement"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/BetweenDatesSanctionStatement.jsp"/>
	</definition>
	<definition name="BetweenDatesSanctionStatementReport" extends="base.definition">
		<put-attribute name="title" value="Between Dates Sanction StatementReport"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/BetweenDatesSanctionStatementReport.jsp"/>
	</definition>
	
	<definition name="updateCMRFEntry" extends="base.definition">
		<put-attribute name="title" value="update cmrf for report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/updateCMRFEntry.jsp"/>
	</definition>
	<definition name="CMRFEntryQuery" extends="base.definition">
		<put-attribute name="title" value="CMRF Entry Query"/>
		<put-attribute name="body" value="/WEB-INF/jsp/query/CMRFEntryQuery.jsp"/>
	</definition>
	<definition name="hospitalOnlineOfflineReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Entry Query Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/hospitalOnlineOfflineReport.jsp"/>
	</definition>
	<definition name="cmrfEntryDetailsReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Status Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/cmrfEntryDetailsReport.jsp"/>
	</definition>
	<definition name="cmrfFraudDetailsReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Frad Details Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/cmrfFraudDetailsReport.jsp"/>
	</definition>
	<definition name="CMRFHospitalCountReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Hospital Count Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/CMRFHospitalCountReport.jsp"/>
	</definition>
	<definition name="cmrfEsignedReportList" extends="base.definition">
		<put-attribute name="title" value="CMRF Status Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/cmrfEsignedReportList.jsp"/>
	</definition>
	<definition name="CMRFEntryQueryReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Entry Query"/>
		<put-attribute name="body" value="/WEB-INF/jsp/query/CMRFEntryQueryReport.jsp"/>
	</definition>
	<definition name="patientAppStatus" extends="base.definition">
		<put-attribute name="title" value="CMRF Entry Query"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/patientAppStatusReport.jsp"/>
	</definition>
	<definition name="CMRFEntryQueryNew" extends="base.definition">
		<put-attribute name="title" value="CMRF Entry Query"/>
		<put-attribute name="body" value="/WEB-INF/jsp/query/CMRFEntryQueryNew.jsp"/>
	</definition>
	<definition name="CMRFEntryQueryReportNew" extends="base.definition">
		<put-attribute name="title" value="CMRF Entry Query"/>
		<put-attribute name="body" value="/WEB-INF/jsp/query/CMRFEntryQueryReportNew.jsp"/>
	</definition>
	<definition name="updateCheque" extends="base.definition">
		<put-attribute name="title" value="update Cheque"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/updateCheque.jsp"/>

	</definition>
		<definition name="aarogyaSriCmrf" extends="base.definition">
		<put-attribute name="title" value=" Aarogya Sri"/>
 		<put-attribute name="body" value="/WEB-INF/jsp/aarogyasri/aarogyaSriCmrf.jsp"/>
	</definition>
	<definition name="getCmrfStatus" extends="base.definition">
		<put-attribute name="title" value=" Get Cmrf Status"/>
 		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/getCmrfStatus.jsp"/>
	</definition>
	<definition name="prwiseReports" extends="base.definition">
		<put-attribute name="title" value="PR Wise Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/prwiseReports.jsp"/>
	</definition>
	<definition name="addIfsc" extends="base.definition">
		<put-attribute name="title" value="Add Bank Details"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/addIfsc.jsp"/>
	</definition>
	<definition name="printApp" extends="base.definition">
		<put-attribute name="title" value="Print CMRF Application"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/printApp.jsp"/>
	</definition>
	<definition name="allApplications" extends="base.definition">
		<put-attribute name="title" value="All Applications"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/allApplications.jsp"/>
	</definition>
	<definition name="updateSanctionAmt" extends="base.definition">
		<put-attribute name="title" value="Update Sanction Amount"/>
		<put-attribute name="body" value="/WEB-INF/jsp/proceedings/updateSanctionAmt.jsp"/>
	</definition>
	<definition name="ConstituencyReport" extends="base.definition">
		<put-attribute name="title" value="Constituency Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/ConstituencyReport.jsp"/>
	</definition>
	<definition name="AmountVerificationReport" extends="base.definition">
		<put-attribute name="title" value="Amount Verification Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/amtVerficationReport.jsp"/>
	</definition>
	<definition name="deoTokenVerifiedReport" extends="base.definition">
		<put-attribute name="title" value="DEO Token Verified Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/deoTokenVerifiedReport.jsp"/>
	</definition>
	<definition name="hospTokenVerifiedReport" extends="base.definition">
		<put-attribute name="title" value="Hospital Token Verified Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/hospitalTokenVerifiedReport.jsp"/>
	</definition>
	<definition name="updateTokenStatus" extends="base.definition">
		<put-attribute name="title" value="Update status"/>
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/updateTokenStatus.jsp"/>
	</definition>
	<definition name="revChequeDetails" extends="base.definition">
		<put-attribute name="title" value="Cheque Details"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/revChequeDetails.jsp"/>
	</definition>
	<definition name="beneficiaryCheque" extends="base.definition">
		<put-attribute name="title" value="Cheque Reprint"/>
		<put-attribute name="body" value="/WEB-INF/jsp/proceedings/beneficiaryCheque.jsp"/>
	</definition>
	<definition name="printUpdatedCheque" extends="base.definition">
		<put-attribute name="title" value="Update Cheque"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/printUpdatedCheque.jsp"/>
	</definition>
	<definition name="cmrfAccountForm" extends="base.definition">
		<put-attribute name="title" value="Accounts Entry"/>
		<put-attribute name="body" value="/WEB-INF/jsp/proceedings/accountsEntry.jsp"/>
	</definition>
	<definition name="cmrfAccountDetails" extends="base.definition">
		<put-attribute name="title" value="CMRF Account Details Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/cmrfAccountDetailsReport.jsp"/>
	</definition>
	<definition name="editDoctorVerifiedAmount" extends="base.definition">
		<put-attribute name="title" value="Edit Doctor Verified Amount"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/editDoctorVerifiedAmount.jsp"/>
	</definition>
	<definition name="locDahsboard" extends="base.definition">
		<put-attribute name="title" value="LOC Dashboard Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/locDahsboard.jsp"/>
	</definition>
	<definition name="locDashbaordDetailsReport" extends="base.definition">
		<put-attribute name="title" value="LOC Dashboard Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/locDashboardDetailsReport.jsp"/>
	</definition>
	<definition name="revChqRePrintEsign" extends="base.definition">
		<put-attribute name="title" value="e-Sign"/>
		<put-attribute name="body" value="/WEB-INF/jsp/proceedings/revChqRePrintEsign.jsp"/>
	</definition>
	<definition name="chequeReprintReport" extends="base.definition">
		<put-attribute name="title" value="Cheque Reprint Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/chequeReprintReport.jsp"/>
	</definition>
	<definition name="distwiseLocReport" extends="base.definition">
	   <put-attribute name="title" value="District Wise Loc Report"/>
	   <put-attribute name="body" value="/WEB-INF/jsp/reports/distwiseLocReport.jsp"/>
    </definition>
    <definition name="districtwiseLocAbstractReport" extends="base.definition">
	   <put-attribute name="title" value="District Wise Loc Details Report"/>
	   <put-attribute name="body" value="/WEB-INF/jsp/reports/districtwiseLocAbstarctReport.jsp"/>
    </definition>
    <definition name="monthlyReport" extends="base.definition">
		<put-attribute name="title" value="Monthly Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/monthlyReport.jsp"/>
	</definition>
	 <definition name="monthWiseChequesReceivedByPRreport" extends="base.definition">
		<put-attribute name="title" value="Cheques Received By PR Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/monthWiseChequesReceivedByPRReprt.jsp"/>
	</definition>	
	<definition name="mlaLOCPrint" extends="base.definition">
		<put-attribute name="title" value="MLA LOC Print Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/mlaLOCPrint.jsp"/>
	</definition>
	<definition name="raiseIssue" extends="base.definition">
		<put-attribute name="title" value="Raise an Issue"/>
		<put-attribute name="body" value="/WEB-INF/jsp/issueTracker/raiseIssue.jsp"/>
	</definition>
	<definition name="submittedIssue" extends="base.definition">
		<put-attribute name="title" value="Submit Issue"/>
		<put-attribute name="body" value="/WEB-INF/jsp/issueTracker/submittedIssue.jsp"/>
	</definition>
	<definition name="viewTicket" extends="base.definition">
		<put-attribute name="title" value="View Ticket"/>
		<put-attribute name="body" value="/WEB-INF/jsp/issueTracker/viewTicket.jsp"/>
	</definition>
	<definition name="issueTrackerApproval" extends="base.definition">
		<put-attribute name="title" value="Issue Tracker Approval"/>
		<put-attribute name="body" value="/WEB-INF/jsp/issueTracker/issueTrackerApproval.jsp"/>
	</definition>
	<definition name="revChqRePrintSOApprove" extends="base.definition">
		<put-attribute name="title" value="e-Sign"/>
		<put-attribute name="body" value="/WEB-INF/jsp/proceedings/revChqRePrintSOApprove.jsp"/>
	</definition>
	<definition name="updateMlaCmrfSpecialCase" extends="base.definition">
		<put-attribute name="title" value="Update MLA CMRF SpecialCase"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/updateMlaCmrfSpecialCase.jsp"/>
	</definition>
	<definition name="editCheque" extends="base.definition">
		<put-attribute name="title" value="Update MLA CMRF SpecialCase"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/editCheque.jsp"/>
	</definition>
	<definition name="hospMerge" extends="base.definition">
		<put-attribute name="title" value="Hosptial Merge"/>
		<put-attribute name="body" value="/WEB-INF/jsp/hospital/hospMerge.jsp"/>
	</definition>
	<definition name="updateMlaCmrfSpecialcaseReport" extends="base.definition">
		<put-attribute name="title" value="Update Mla Cmrf Specialcase Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/updateMlaCmrfSpecialcaseReport.jsp"/>
	</definition>
	<definition name="PRLOCTrackApplication" extends="base.definition">
		<put-attribute name="title" value="PR LOC Track Application"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/PRLOCTrackApplication.jsp"/>
	</definition>
	<definition name="LOCTrackApplicationView" extends="base.definition">
		<put-attribute name="title" value="PR LOC Track Application"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/LOCTrackApplicationView.jsp"/>
	</definition>
	
	<definition name="bundleReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Bundle Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/bundleReport.jsp"/>
	</definition>
	<definition name="updateOldBundle" extends="base.definition">
		<put-attribute name="title" value="CMRF Bundle Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/updateOldBundle.jsp"/>
	</definition>
	
	<definition name="cmrfEnhancementReport" extends="base.definition">
		<put-attribute name="title" value="CMRF Enhancement Report"/>
		<put-attribute name="body" value="/WEB-INF/jsp/reports/cmrfEnhancementReport.jsp"/>
	</definition>
	<definition name="addMonthlyProcCheq" extends="base.definition">
		<put-attribute name="title" value="Add Monthly Presented Cheques"/>
		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/addMonthlyProcCheq.jsp"/>
	</definition> 
	
	<definition name="prAckLetters" extends="base.definition">
		<put-attribute name="title" value="PR Acknowledgement Letters"/>
 		<put-attribute name="body" value="/WEB-INF/jsp/proceedings/prAckLetters.jsp"/>
	</definition>
	
	<!-- <definition name="getStatusTree" extends="base.definition">
		<put-attribute name="title" value="Status of Application"/>
 		<put-attribute name="body" value="/WEB-INF/jsp/dataentry/getStatusTree.jsp"/>
	</definition> -->

</tiles-definitions>

	
	

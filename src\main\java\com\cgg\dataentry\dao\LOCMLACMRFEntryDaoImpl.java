package com.cgg.dataentry.dao;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.time.Year;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.sql.DataSource;

import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.web.multipart.MultipartFile;

import com.cgg.common.ApplicationConstants;
import com.cgg.common.CommonFunctions;
import com.cgg.common.CommonUtils;
import com.cgg.dataentry.model.LOCMLACMRFEntryModel;
import com.cgg.dataentry.model.Treatments;

@Repository
public class LOCMLACMRFEntryDaoImpl implements LOCMLACMRFEntryDao{

	private static final String ALLOWED_CHARS = "[^a-zA-Z0-9-_]";
	
	@Autowired
	DataSource dataSource;
	
	@Autowired
	JdbcTemplate jdbcTemlate;

	@Override
	public List<LOCMLACMRFEntryModel> getRecommendedDetails(HttpSession session) throws Exception {	
		
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<LOCMLACMRFEntryModel> recommendedList= new ArrayList<LOCMLACMRFEntryModel>();
		String roleId=null;
		String constNo=null;
		String cond="";
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			roleId=(String) session.getAttribute("rolesStr");
			if(roleId!=null && roleId.equals("24")) {
				constNo=(String) session.getAttribute("consNo");
				cond = cond+" and cno = '"+constNo+"' ";
			}
			sql="select cno,mlamp||design||'('||party||'), '||cname as cname from  constituency where active_con =true "+cond+ " order by mlamp,int4(cno) ";
			rs=st.executeQuery(sql);
			while(rs.next()) {
				LOCMLACMRFEntryModel locEntryForm=new LOCMLACMRFEntryModel();
				locEntryForm.setConstNo(rs.getString("cno"));
				locEntryForm.setConstName(rs.getString("cname"));
				recommendedList.add(locEntryForm);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return recommendedList;
	}

	@Override
	public List<LOCMLACMRFEntryModel> getHospitalDetails(HttpSession session) throws Exception {
		List<LOCMLACMRFEntryModel> hospitalList= new ArrayList<LOCMLACMRFEntryModel>();
		String sql="";
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String roleId=null;
		String constNo=null;
		String cond="";
		try {
			
			con=dataSource.getConnection();
			st=con.createStatement();
			roleId=(String) session.getAttribute("rolesStr");
			if(roleId!=null && roleId.equals("24")) {
				//cond = cond+" and hospcode in('2','411','56','3','214','548') ";
				cond = cond+" and hospcode in('2','411') ";
			}else{
				cond = cond+" and hospcode in('2','411','56','3','214','548') ";
			}
			sql="select hospcode, initcap(hospname) as hospname  from   hospital where length(hospname)>0 and  delete_flag='false' "+cond+" order by hospname";
			rs=st.executeQuery(sql);
			while(rs.next()) {
				LOCMLACMRFEntryModel locEntryForm=new LOCMLACMRFEntryModel();
				locEntryForm.setHospCode(rs.getString("hospcode"));  
				locEntryForm.setHospName(rs.getString("hospname")); 
				hospitalList.add(locEntryForm);
			}
		}catch (Exception e) {
			e.printStackTrace();
		}finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return hospitalList;
	}

	@Override
	public String saveLocMlaCmrfEntry(LOCMLACMRFEntryModel locEntryForm,HttpServletRequest request) throws Exception {
		String result = null;
		boolean flag=false;
		String sql="",sql2="",sql3="",max_mla_loc="",mlalocno="",vip_date="",UploadPath="";
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
	    ResultSet rs1 = null;
		int count=0;
		try {
            synchronized (this) {
                sql = "select coalesce(max(int4(split_part(mla_loc_no,'/',1))+1),1) from loc_mla_cmrf where split_part(mla_loc_no,'/',3)=to_char(current_date,'yyyy')";
                con=dataSource.getConnection();
                st = con.createStatement();
                rs = st.executeQuery(sql);
                if (rs.next()) {
                	max_mla_loc = rs.getString(1);
                }    
				//String FILES_UPLOAD_PATH= "/Uploads/tgcmrf/FILES/";
				int currentYear = Year.now().getValue();
				System.out.println(currentYear);
				/*if (locEntryForm.getFilePath() != null) {
					if (locEntryForm.getFilePath().startsWith(FILES_UPLOAD_PATH)) {
						String filename = locEntryForm.getFilePath().substring(FILES_UPLOAD_PATH.length());
						String extension = filename.substring(filename.lastIndexOf('.'));
						UploadPath = FILES_UPLOAD_PATH + max_mla_loc + "TLOC" + currentYear + extension;
					}
				}*/
				String patDistrict = (locEntryForm.getPatDistrict() == null || locEntryForm.getPatDistrict().isEmpty()) ? null : locEntryForm.getPatDistrict();
				String patMandal = (locEntryForm.getPatMandal() == null || locEntryForm.getPatMandal().isEmpty()) ? null : locEntryForm.getPatMandal();
				String pinCode = (locEntryForm.getPinCode() == null || locEntryForm.getPinCode().isEmpty()) ? null : locEntryForm.getPinCode();
				String mobileNo = (locEntryForm.getMobileNo() == null || locEntryForm.getMobileNo().isEmpty()) ? null : locEntryForm.getMobileNo();
                vip_date = locEntryForm.getVipletter_date();
                //String cmcoRefBySql = (locEntryForm.getReferredBy() == null || locEntryForm.getReferredBy().isEmpty()) ? null : locEntryForm.getReferredBy();
                String cmcoRefBySql = (locEntryForm.getReferredBy() == null || locEntryForm.getReferredBy().isEmpty()) ? null : "'" + locEntryForm.getReferredBy() + "'";
               
                // vip_date = Integer.parseInt(locEntryForm.getVipletter_date().substring(8, 10))+"-"+Integer.parseInt(locEntryForm.getVipletter_date().substring(5, 7))+"-"+Integer.parseInt(locEntryForm.getVipletter_date().substring(0, 4));
                if (max_mla_loc != null && !max_mla_loc.equals("")) {
                 //   sql2 = "INSERT INTO public.loc_mla_cmrf(mla_loc_no, patient_name,father_son_of,address,purpose,assured_amt,recommended_by,hosp_code,status,entered_on,vip_letter_dt,entered_by,ip_address,old_fsc_no,new_fsc_no,income_cer_no,gender,mobile_no,age,aadhaar_no,pat_district,pat_mandal,pincode,upload_path,pending_reasons)   VALUES('" + max_mla_loc + "/TLOC/'||to_char(current_date,'yyyy'), '" + locEntryForm.getPatient_name() + "', '" + locEntryForm.getGuardian()+" "+locEntryForm.getFather_name() + "','" + locEntryForm.getAddress() + "','" + locEntryForm.getPurpose() + "'," + locEntryForm.getAssured_amt() + ",  '" + locEntryForm.getRecommendedBy() + "'," + locEntryForm.getHospCode() + "" + ",'" + locEntryForm.getStatus() + "',now(),to_date('" + vip_date + "','yyyy-mm-dd'),'" + locEntryForm.getUserId() +"','"+request.getRemoteAddr()+"','"+locEntryForm.getOldFscNo()+"','"+locEntryForm.getNewFscNo()+"','"+locEntryForm.getIncomeCerNo()+"','"+locEntryForm.getGender()+"',"+mobileNo+",'"+locEntryForm.getAge()+"','"+locEntryForm.getAadharNo()+"',"+patDistrict+","+patMandal+","+pinCode+",'"+UploadPath+"','"+locEntryForm.getPendingReasons()+"')";
                    sql2 = "INSERT INTO public.loc_mla_cmrf(mla_loc_no, patient_name,father_son_of,address,purpose,assured_amt,recommended_by,hosp_code,status,entered_on,vip_letter_dt,entered_by,ip_address,old_fsc_no,new_fsc_no,income_cer_no,gender,mobile_no,age,aadhaar_no,pat_district,pat_mandal,pincode,pending_reasons,treat_par_id,treat_sub_id,treat_proc_id,cmco_referred_By)   VALUES('" + max_mla_loc + "/TLOC/'||to_char(current_date,'yyyy'), '" + locEntryForm.getPatient_name() + "', '" + locEntryForm.getGuardian()+" "+locEntryForm.getFather_name() + "','" + locEntryForm.getAddress() + "','" + locEntryForm.getPurpose() + "'," + locEntryForm.getAssured_amt() + ",  '" + locEntryForm.getRecommendedBy() + "'," + locEntryForm.getHospCode() + "" + ",'" + locEntryForm.getStatus() + "',now(),to_date('" + vip_date + "','yyyy-mm-dd'),'" + locEntryForm.getUserId() +"','"+request.getRemoteAddr()+"','"+locEntryForm.getOldFscNo()+"','"+locEntryForm.getNewFscNo()+"','"+locEntryForm.getIncomeCerNo()+"','"+locEntryForm.getGender()+"',"+mobileNo+",'"+locEntryForm.getAge()+"','"+locEntryForm.getAadharNo()+"',"+patDistrict+","+patMandal+","+pinCode+",'"+locEntryForm.getPendingReasons()+"',"+locEntryForm.getTreatParId()+","+locEntryForm.getTreatSubId()+","+locEntryForm.getTreatProcId()+","+cmcoRefBySql+")";

                }
                count = jdbcTemlate.update(sql2);
                if (count >= 1) {
                    sql3 = "SELECT mla_loc_no  FROM public.loc_mla_cmrf   where mla_loc_no='" + max_mla_loc + "/TLOC/'||to_char(current_date,'yyyy') and delete_flag=false";
                    rs1 = st.executeQuery(sql3);
                    while (rs1.next()) {
                        mlalocno = rs1.getString("mla_loc_no");
                        locEntryForm.setLoc_mla_no(mlalocno);
                        flag = true;
                    } 
                }
            }
        }
        catch (Exception e) {
            e.printStackTrace();
        }
        finally {
        	CommonUtils.closeCon(con, st, rs);
        }
		result = max_mla_loc;
		return result;
	}

	@Override
	public String saveMlaLocCmrfEntry(LOCMLACMRFEntryModel locEntryForm,HttpServletRequest request) throws Exception {
		
			String result = null;
			String sql1="",sql2="",sql3="",max_mla_loc="",mlalocno="";
			Connection con=null;
			Statement st=null;
			ResultSet rs=null;
		    ResultSet rs1 = null;
			int count=0;
			int currentYear=0;
			String locNo=null;
			String extraCols = "";
			String extraVals = "";
			
		try {
			MultipartFile file = locEntryForm.getEstimationLetterFile();
			String estimationLetterPath = null;
			currentYear = Year.now().getValue();
			if ("411".equals(locEntryForm.getHospCode())) {
				locEntryForm.setStatus("6");
			} else {
				locEntryForm.setStatus("5");
			}
		    
		    String patIpValsql = Optional.ofNullable(locEntryForm.getPatientIpNo())
		            .filter(CommonFunctions::validate)
		            .map(val -> "'" + val + "'")
		            .orElse("null");

		    String incomeNoValSql = Optional.ofNullable(locEntryForm.getIncomeCerNo())
		            .filter(CommonFunctions::validate)
		            .map(val -> "'" + val + "'")
		            .orElse("null");

		    String fscNoValSql = Optional.ofNullable(locEntryForm.getNewFscNo())
		            .filter(CommonFunctions::validate)
		            .map(val -> "'" + val + "'")
		            .orElse("null");
		    
		    String cmcoRefBySql = Optional.ofNullable(locEntryForm.getReferredBy())
		            .filter(CommonFunctions::validate)
		            .map(val -> "'" + val + "'")
		            .orElse("null");

		    
            synchronized (this) {
                sql1 = "select coalesce(max(int4(split_part(mla_loc_no,'/',1))+1),1) from loc_mla_cmrf where split_part(mla_loc_no,'/',3)=to_char(current_date,'yyyy')";
                con=dataSource.getConnection();
                st = con.createStatement();
                rs = st.executeQuery(sql1);
                if (rs.next()) {
                	max_mla_loc = rs.getString(1);
                } 

				if (file != null && !file.isEmpty()) {
					try {
						estimationLetterPath = uploadFile2(file, max_mla_loc);
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
                
                if (max_mla_loc != null && !max_mla_loc.equals("")) {
                	locNo=max_mla_loc+"/TLOC/"+currentYear;
                	System.out.println("MLA LOC EntryForm Max No---"+locNo);

					if ("411".equals(locEntryForm.getHospCode()) && CommonFunctions.validate(locEntryForm.getUserId()) && CommonFunctions.validate(request.getRemoteAddr())) {
						extraCols = ", dept_updated_by, dept_updated_date, dept_updated_ip, treat_dept_id";
						extraVals = ",'" + locEntryForm.getUserId() + "', now(), '" + request.getRemoteAddr() + "', 27";
					}
                	
                    sql2 = " INSERT INTO public.loc_mla_cmrf(mla_loc_no, patient_name,father_son_of,address,purpose,recommended_by,"
                    	+ " vip_letter_dt,hosp_code,status,entered_on,entered_by,ip_address,new_fsc_no,income_cer_no,gender,"
                    	+ " mobile_no,age,aadhaar_no,pat_district,pat_mandal,pincode,patient_ip,opcr_no,estimation_letter,cmco_referred_By"+extraCols+")"
                    	+" VALUES('"+locNo+ "','"+locEntryForm.getPatient_name()+ "', "
                    	+" '"+locEntryForm.getGuardian()+" "+locEntryForm.getFather_name()+"',"
                    	+" '"+locEntryForm.getAddress()+"', '"+locEntryForm.getPurpose()+"',"
                    	+" '"+locEntryForm.getRecommendedBy()+"','"+locEntryForm.getVipletter_date()+"',"
                    	+" "+locEntryForm.getHospCode()+",'"+locEntryForm.getStatus()+"',now(),'"+locEntryForm.getUserId()+"',"
                    	+" '"+request.getRemoteAddr()+"',"+fscNoValSql+","+incomeNoValSql+","
                    	+" '"+locEntryForm.getGender()+"',"+locEntryForm.getMobileNo()+",'"+locEntryForm.getAge()+"',"
                    	+" '"+locEntryForm.getAadharNo()+"',"+locEntryForm.getPatDistrict()+","+locEntryForm.getPatMandal()+","
                    	+" "+locEntryForm.getPinCode()+","+patIpValsql+",'"+locEntryForm.getOpCRNo()+"','"+estimationLetterPath+"',"+cmcoRefBySql+extraVals+")";
                    
                    System.out.println("MLA LOC EntryForm insert sql ---"+sql2);
                    
                    count = jdbcTemlate.update(sql2);
                    
                      if (count >= 1) { 
                         sql3 = "SELECT mla_loc_no  FROM public.loc_mla_cmrf where mla_loc_no='"+locNo+"' and delete_flag=false ";
                         rs1 = st.executeQuery(sql3);
                         while (rs1.next()) {
                             mlalocno = rs1.getString("mla_loc_no");
                             locEntryForm.setLoc_mla_no(mlalocno);                      
                          } 
                      }
                 }
                
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
        finally {
            CommonUtils.closeCon(con, st, rs);
        }
		result = max_mla_loc;
		return result;
	}
	
	@Override
	public List<LOCMLACMRFEntryModel> getLocData(String loc_token) throws Exception {
		String sql="";
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		List<LOCMLACMRFEntryModel> locData=new ArrayList<LOCMLACMRFEntryModel>();
		
		if (loc_token != null && !"".equals(loc_token)) {

			sql = "select patient_name,father_son_of,address,purpose,assured_amt,recommended_by,to_char(vip_letter_dt,'dd/mm/yyyy') as vip_letter_dt,hosp_code,status "
					+ "from loc_mla_cmrf  where mla_loc_no='" + loc_token + "'";
			con=dataSource.getConnection();
			st = con.createStatement();
			rs = st.executeQuery(sql);
			if (rs.next()) {
				LOCMLACMRFEntryModel locEntryForm=new LOCMLACMRFEntryModel();
				locEntryForm.setPatient_name(rs.getString("patient_name"));  
				locEntryForm.setFather_name(rs.getString("father_son_of")); 
				locEntryForm.setAddress(rs.getString("address")); 
				locEntryForm.setPurpose(rs.getString("purpose"));
				locEntryForm.setAssured_amt(rs.getString("assured_amt"));
				locEntryForm.setRecommendedBy(rs.getString("recommended_by"));
				locEntryForm.setVipletter_date(rs.getString("vip_letter_dt"));
				locEntryForm.setHospCode(rs.getString("hosp_code"));
				locEntryForm.setStatus(rs.getString("status"));
				locData.add(locEntryForm);
			} 
		} 
		return locData;
	}

	@Override
	public boolean updateLocDetails(LOCMLACMRFEntryModel locEntryForm) {
		boolean flag=false;int count=0;
		String sql="update  public.loc_mla_cmrf set patient_name='"+locEntryForm.getPatient_name()+"', father_son_of='"+locEntryForm.getFather_name()+"',"
                + "address='"+locEntryForm.getAddress()+"', assured_amt="+locEntryForm.getAssured_amt()+", hosp_code="+locEntryForm.getHospCode()+","
                + "purpose='"+locEntryForm.getPurpose()+"',recommended_by="+locEntryForm.getRecommendedBy()+","
                + "vip_letter_dt=to_date('" +locEntryForm.getVipletter_date()+"','dd/mm/yyyy'),status='"+locEntryForm.getStatus()+"'"
                + "  where mla_loc_no='"+locEntryForm.getLoc_token()+"' ";
		count=jdbcTemlate.update(sql);
		if(count>0) {
			flag=true;
		}else {
			flag=false;
		}
		return flag;
	} 

	public List<LOCMLACMRFEntryModel> getDistricts() throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<LOCMLACMRFEntryModel> districts = new ArrayList<LOCMLACMRFEntryModel>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			sql="select distno, distname from  district  order by distname";
			System.out.println("sql---"+sql);
			rs=st.executeQuery(sql);
			while(rs.next()) {
				LOCMLACMRFEntryModel mlaInwardLoc = new LOCMLACMRFEntryModel();
				mlaInwardLoc.setDistNo(rs.getString("distno"));
				mlaInwardLoc.setDistName(rs.getString("distname"));
				districts.add(mlaInwardLoc);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return districts;
	}
	public List<LOCMLACMRFEntryModel> getMandals(String distCode) throws Exception {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		List<LOCMLACMRFEntryModel> mandals = new ArrayList<LOCMLACMRFEntryModel>();
		
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			sql="select mcode, mname from  mandal where distcode='"+distCode + "' order by mname";
			System.out.println("sql---"+sql);
			rs=st.executeQuery(sql);
			while(rs.next()) {
				LOCMLACMRFEntryModel mlaInwardLoc = new LOCMLACMRFEntryModel();
				mlaInwardLoc.setMandalNo(rs.getString("mcode"));
				mlaInwardLoc.setMandalName(rs.getString("mname"));
				mandals.add(mlaInwardLoc);
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			CommonUtils.closeCon(con, st, rs);
		}
		return mandals;

}

	public List<Treatments> getHealthCareServices() throws Exception{
		Connection con = null;
		Statement st = null;
		ResultSet rs = null;
		String sql = null;
		List<Treatments> treatments = new ArrayList<Treatments>();

		try {
			con = dataSource.getConnection();
			st = con.createStatement();
			sql = "select slno,special_name from cmrf_treat_special_mst ";
			System.out.println("SQL--->"+sql);
			rs = st.executeQuery(sql);
			while (rs.next()) {
				Treatments treatment = new Treatments();
                treatment.setHealthCareServiceId(rs.getInt("slno"));
                treatment.setHealthCareServiceName(rs.getString("special_name"));
                treatments.add(treatment);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			CommonUtils.closeCon(con, st, rs);
		}

		return treatments;
	}

	@Override
	public List<Treatments> getSubTreatmentsByHcsId(Integer treatParId) throws Exception {
		Connection con = null;
		Statement st = null;
		ResultSet rs = null;
		String sql = null;
		List<Treatments> treatments = new ArrayList<Treatments>();

		try {
			con = dataSource.getConnection();
			st = con.createStatement();
			sql = "select slno,upper(special_name) as special_name from cmrf_treat_sub_special_mst where parent_id = "+treatParId+" and delete_flag is false order by special_name ";
			System.out.println("SQL--->"+sql);
			rs = st.executeQuery(sql);
			while (rs.next()) {
				Treatments subtTreatments = new Treatments();
				subtTreatments.setSubTreatmentId(rs.getInt("slno"));
				subtTreatments.setSubTreatmentName(rs.getString("special_name"));
				treatments.add(subtTreatments);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			CommonUtils.closeCon(con, st, rs);
		}

		return treatments;
	}

	@Override
	public List<Treatments> getProceduresBySubId(Integer treatParId,Integer treatSubId) throws Exception {
		Connection con = null;
		Statement st = null;
		ResultSet rs = null;
		String sql = null;
		List<Treatments> treatments = new ArrayList<Treatments>();

		try {
			con = dataSource.getConnection();
			st = con.createStatement();
			sql = "select slno,procedure_code,upper(procedure_name) as procedure_name from cmrf_treatments_types where type_par_id = "+treatParId+" and type_sub_id = "+treatSubId+" order by procedure_name";
			System.out.println("SQL--->"+sql);
			rs = st.executeQuery(sql);
			while (rs.next()) {
				Treatments procedures = new Treatments();
				procedures.setProcedureId(rs.getInt("slno"));
				procedures.setProcedureName(rs.getString("procedure_name"));
				treatments.add(procedures);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			CommonUtils.closeCon(con, st, rs);
		}

		return treatments;
	}

	public static String uploadFile2(
        MultipartFile file,
        String locNo
    ) {
        if (file == null || file.isEmpty()) {
        	throw new IllegalArgumentException("File cannot be empty!");
        }
        String originalFileName = file.getOriginalFilename();
		System.out.println("ORIGINAL FILE NAME : " + originalFileName);
		String fileExtension = FilenameUtils.getExtension(originalFileName);
		System.out.println("FILE EXTENSION : " + fileExtension);

        // Ensure the upload directory exists
		String uploadPath = ApplicationConstants.ESTIMATION_LETTER_PATH + ApplicationConstants.AC_YEAR;
		System.out.println("UPLOAD PATH : " + uploadPath);
        File dir = new File(uploadPath);
        if (!dir.exists()) {
        	dir.mkdirs();
        }

        // Construct new file name
        String safeFileName = locNo + "_EstimationLetter_" + ApplicationConstants.AC_YEAR + "." + fileExtension;
        String finalPath = ApplicationConstants.BASE_PATH + uploadPath + "/" + safeFileName;
        String DBPath = uploadPath + "/" + safeFileName;
		System.out.println("Final PATH : " + finalPath);
		System.out.println("DB PATH : " + DBPath);

        try {
        // Save file
        Files.write(
            Paths.get(finalPath),
            file.getBytes(),
            StandardOpenOption.CREATE
        );
        } catch (IOException e) {
        	throw new RuntimeException("File upload failed", e);
        }

        System.out.println("File uploaded to: " + DBPath);
        return DBPath;
    }
}

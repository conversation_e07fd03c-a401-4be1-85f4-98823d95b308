package com.cgg.dataentry.service;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.dataentry.model.MlaCmrfEntityModel;
import com.cgg.dataentry.repositories.MlaInwardCmrfEditRepository;
import com.cgg.dataentry.repositories.UpdateMlaCmrfSpecilaCaseRepository;

@Service
public class UpdateMlaCmrfSpecialCaseService {

	@Autowired
	private UpdateMlaCmrfSpecilaCaseRepository updateMlaCmrfSpecilaCaseRepository;

    @Autowired
	MlaInwardCmrfEditRepository logRepo;

	public MlaCmrfEntityModel getMlaCmrfDetails(String mlaCmrfNo) {
		return updateMlaCmrfSpecilaCaseRepository.getMlaCmrfDetails(mlaCmrfNo);
	}

    public Map<String, String> updateMlaCmrfSpecialCase(String mlaCmrfNo, boolean isSpecialFlag, String userId, String referredBy, HttpServletRequest request) {
        Map<String, String> responseMap = new HashMap<>();
        try {
            // Step 1: Log the action
            int updateLog = logRepo.insertMlaCmrfLog(mlaCmrfNo, "Updating Spl Case Referred By.", userId, request.getRemoteAddr());
            if (updateLog <= 0) {
                responseMap.put("error", "Failed to log the application.");
                return responseMap;
            }

            // Step 3: Update special case info
            int count = updateMlaCmrfSpecilaCaseRepository.updateMlaCmrfSpecialCase(mlaCmrfNo, isSpecialFlag, userId, referredBy);

            if (count > 0) {
                responseMap.put("success", "Special Case Updated Successfully.");
            }
        } catch (Exception e) {
            e.printStackTrace();
            responseMap.put("error", "Exception occurred: " + e.getMessage());
        }
        return responseMap;
    }

}

package com.cgg.common;

import java.util.*;
import java.io.*;

public class GeneralOperations
{
	public ArrayList getResultListMapped(ArrayList arrList, Hashtable mapHash, int mapIndex)
	{
		ArrayList tempList = new ArrayList();
		ArrayList resList = new ArrayList();

		if(mapIndex < 0 && mapIndex >= arrList.size())	return null;
		else
		{
			for(int i=0 ; i<arrList.size() ; i++)
			{
				tempList = (ArrayList)arrList.get(i);
				tempList.set(  mapIndex , mapHash.get( tempList.get(mapIndex) )  );

				resList.add(tempList);
			}
			return resList;
		}
			
	} 
	public String[] getResultSDArrMapped(String[] arr, Hashtable mapHash)
	{
		String[] retArr = new String[arr.length];
		for(int i=0 ; i<arr.length ; i++)
		{
			if( mapHash.get(arr[i]) != null )
				retArr[i] = (String) mapHash.get( arr[i] );
			else
				retArr[i] = arr[i];
		}
		
		return retArr;
	} 
	public String getStringOfArray(String[] strArray,String seperator)
	{
		String str = "";
		for(int i=0;i<strArray.length;i++)
		{
			str += seperator+strArray[i];
		}
		return str.substring(seperator.length());
	}
	public String getStringOfList(ArrayList strArray,String seperator)
	{
		String str = "";
		for(int i=0;i<strArray.size();i++)
		{
			str += seperator+strArray.get(i);
		}
		return str.substring(seperator.length());
	}
	public String[] getSDStrArray(ArrayList list)
	{
		String[] arr = new String[list.size()];

		for(int i=0 ; i<list.size(); i++)
		{
			arr[i] = (String)list.get(i);	
		}
		return arr;
	}
	public String getFileStr(String filePath)
	{
		String xslString = "";
		try
		{
			DataInputStream din = new DataInputStream(new FileInputStream(new File(filePath)));
			String line = null;
			line = (String) din.readLine();
			while (line != null)
			{
				xslString += line + "\n";
				line = (String) din.readLine();
			}
			din.close();
			return xslString;
		}
		catch(Exception e) { System.out.println("Error has occured at generaldata.GeneralOperations.getFileStr : "+e.getMessage()); }
		return null;
	}
	public String replaceStr(String string, String text, String by)
	{
		// Replaces text with by in string
		try
		{
			int strLength = string.length(), txtLength = text.length();
			if ((strLength == 0) || (txtLength == 0)) return string;

			int i = string.indexOf(text);
			if (i == -1) return string;

			String newstr = string.substring(0,i) + by;

			if (i+txtLength < strLength)
			newstr += replaceStr(string.substring(i+txtLength,strLength),text,by);

			return newstr;
		}
		catch(Exception e)
		{
			System.out.println("Error at generaldata.GeneralOperations.replaceStr........"+e.getMessage());
			return "";
		}
	}
}

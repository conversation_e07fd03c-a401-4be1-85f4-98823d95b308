package com.cgg.reports.controller;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import com.cgg.common.ApplicationConstants;
import com.cgg.reports.model.MonthWiseSummary;
import com.cgg.reports.service.MonthWiseChequesReceivedByPRService;


@Controller
@RequestMapping(value = "/monthWisePRCheques")
public class MonthWiseChequesReceivedByPRController {
	
	@Autowired
	private MonthWiseChequesReceivedByPRService monthWiseChequesReceivedByPRService;

	@GetMapping
	public String viewPage(Map<String, Object> model,@ModelAttribute("MonthWiseSummary") MonthWiseSummary MonthWiseSummary,
			HttpServletRequest req) throws Exception {
		
		HttpSession session = req.getSession();
		List<String> validRoleIds = Arrays.asList(ApplicationConstants.CMRF_OSD_ROLE,ApplicationConstants.REV_AUDIT);
		try {
			String userId = (String) session.getAttribute("userid");
			String roleId = (String) session.getAttribute("rolesStr");
			if (userId == null || roleId == null || !validRoleIds.contains(roleId)) {
				return "redirect:/";
			}
		} catch (Exception e) {
			model.put("msg", "Something went wrong "+e.getMessage());
			e.printStackTrace();
		}
		return "monthWiseChequesReceivedByPRForm";
	}
	
	
	@PostMapping
	public String getChequesReceivedByPRMonthReport(@ModelAttribute("MonthWiseSummary") MonthWiseSummary monthWiseSummary, Map<String, Object> model,HttpServletRequest req) throws Exception {

		int year = Integer.parseInt(monthWiseSummary.getYear());
		int month = Integer.parseInt(monthWiseSummary.getMonth());
		model.put("chequesReceivedByPRMonthReportDataFlag", true);

		try {
			LocalDate fromDate = LocalDate.of(year, month, 1);
			YearMonth ym = YearMonth.of(year, month);
			LocalDate toDate = ym.atEndOfMonth();
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
			String dateFrom = fromDate.format(formatter);
			String dateTo = toDate.format(formatter);

			// Get full month name
			DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("MMMM");
			String monthName = fromDate.format(monthFormatter).toUpperCase();
			String monthYear = "- "+monthName + " " + year;
			
			String dateMonthYear = "01-" + monthName + "-" + year;

			List<MonthWiseSummary> chequesReceivedByPRMonthReportData = new ArrayList<MonthWiseSummary>();
			chequesReceivedByPRMonthReportData = monthWiseChequesReceivedByPRService.getChequesReceivedByPRMonthReport(dateFrom, dateTo);

			if (chequesReceivedByPRMonthReportData != null && !chequesReceivedByPRMonthReportData.isEmpty())
			{
				model.put("chequesReceivedByPRMonthReportData", chequesReceivedByPRMonthReportData);
				model.put("monthYear", monthYear);
				model.put("dateMonthYear", dateMonthYear);
			}else{
				model.put("msg", "No Data Found");
			}

		} catch (Exception e) {
			model.put("msg", "Something went wrong "+e.getMessage());
			e.printStackTrace();
		}
		return "monthWiseChequesReceivedByPRreport";
	}

}
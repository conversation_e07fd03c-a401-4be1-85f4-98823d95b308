package com.cgg.dataentry.model;

import java.io.Serializable;

import org.springframework.web.multipart.MultipartFile;

public class LOCMLACMRFEntryModel implements Serializable { 
	
	
	private static final long serialVersionUID = 1L;
	
	private String patient_name;
	private String father_name;
	private String Address;
	private String purpose;
	private String assured_amt;
	private String recommendedBy,constNo,constName;
	private String vipletter_date;
	private String hospCode,hospName;
	private String status;
	private String loc_mla_no;
	private String mla_cmrf_year;
	private String loc_token;
	private String guardian; 
	private String userId;

	private String oldFscNo,newFscNo,incomeCerNo,filePath;

	private String age,aadharNo,hospDistrict,patDistrict=null,patMandal=null,mobileNo=null,gender,pinCode=null;

	private String cmrfPriority;
	private String mandalNo,mandalName;
	private String distNo,distName;
	private String pendingReasons;
	private Integer treatParId,treatSubId,treatProcId;
	
	private String patientIpNo;
	private String opCRNo;
	private String ipAddress;
	private String mlaName;
	private String treatmentDeptName;
	private String currentDate;
	private String currentYear;
	private String enteredOn;
	private MultipartFile EstimationLetterFile;
	private String locNo;
	private String referredBy;
	private String deptUpdatedBy, deptUpdatedDate, deptUpdatedIp;
	
	public String getLocNo() {
		return locNo;
	}
	public void setLocNo(String locNo) {
		this.locNo = locNo;
	}
	public String getTreatmentDeptName() {
		return treatmentDeptName;
	}
	public void setTreatmentDeptName(String treatmentDeptName) {
		this.treatmentDeptName = treatmentDeptName;
	}
	public String getCurrentYear() {
		return currentYear;
	}
	public void setCurrentYear(String currentYear) {
		this.currentYear = currentYear;
	}
	public String getCurrentDate() {
		return currentDate;
	}
	public void setCurrentDate(String currentDate) {
		this.currentDate = currentDate;
	}
	
	public String getMlaName() {
		return mlaName;
	}
	public void setMlaName(String mlaName) {
		this.mlaName = mlaName;
	}
	public String getPendingReasons() {
		return pendingReasons;
	}
	public void setPendingReasons(String pendingReasons) {
		this.pendingReasons = pendingReasons;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getGuardian() {
		return guardian;
	}
	public void setGuardian(String guardian) {
		this.guardian = guardian;
	}
	public String getLoc_token() {
		return loc_token;
	}
	public void setLoc_token(String loc_token) {
		this.loc_token = loc_token;
	}
	public String getMla_cmrf_year() {
		return mla_cmrf_year;
	}
	public void setMla_cmrf_year(String mla_cmrf_year) {
		this.mla_cmrf_year = mla_cmrf_year;
	}
	public String getPatient_name() {
		return patient_name;
	}
	public void setPatient_name(String patient_name) {
		this.patient_name = patient_name;
	}
	public String getFather_name() {
		return father_name;
	}
	public void setFather_name(String father_name) {
		this.father_name = father_name;
	}
	
	public String getAddress() {
		return Address;
	}
	public void setAddress(String address) {
		this.Address = address;
	}
	public String getPurpose() {
		return purpose;
	}
	public void setPurpose(String purpose) {
		this.purpose = purpose;
	}
	public String getAssured_amt() {
		return assured_amt;
	}
	public void setAssured_amt(String assured_amt) {
		this.assured_amt = assured_amt;
	}
	public String getRecommendedBy() {
		return recommendedBy;
	}
	public void setRecommendedBy(String recommendedBy) {
		this.recommendedBy = recommendedBy;
	}
	public String getConstNo() {
		return constNo;
	}
	public void setConstNo(String constNo) {
		this.constNo = constNo;
	}
	public String getConstName() {
		return constName;
	}
	public void setConstName(String constName) {
		this.constName = constName;
	}
	public String getVipletter_date() {
		return vipletter_date;
	}
	public void setVipletter_date(String vipletter_date) {
		this.vipletter_date = vipletter_date;
	}
	public String getHospCode() {
		return hospCode;
	}
	public void setHospCode(String hospCode) {
		this.hospCode = hospCode;
	}
	public String getHospName() {
		return hospName;
	}
	public void setHospName(String hospName) {
		this.hospName = hospName;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getLoc_mla_no() {
		return loc_mla_no;
	}
	public void setLoc_mla_no(String loc_mla_no) {
		this.loc_mla_no = loc_mla_no;
	}
	public String getOldFscNo() {
		return oldFscNo;
	}
	public void setOldFscNo(String oldFscNo) {
		this.oldFscNo = oldFscNo;
	}
	public String getNewFscNo() {
		return newFscNo;
	}
	public void setNewFscNo(String newFscNo) {
		this.newFscNo = newFscNo;
	}
	public String getIncomeCerNo() {
		return incomeCerNo;
	}
	public void setIncomeCerNo(String incomeCerNo) {
		this.incomeCerNo = incomeCerNo;
	}
	public String getFilePath() {
		return filePath;
	}
	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}
	public String getGender() {
		return gender;
	}
	public void setGender(String gender) {
		this.gender = gender;
	}
	public String getAge() {
		return age;
	}
	public void setAge(String age) {
		this.age = age;
	}
	public String getAadharNo() {
		return aadharNo;
	}
	public void setAadharNo(String aadharNo) {
		this.aadharNo = aadharNo;
	}
	public String getMobileNo() {
		return mobileNo;
	}
	public void setMobileNo(String mobileNo) {
		this.mobileNo = mobileNo;
	}
	public String getPatDistrict() {
		return patDistrict;
	}
	public void setPatDistrict(String patDistrict) {
		this.patDistrict = patDistrict;
	}
	public String getPatMandal() {
		return patMandal;
	}
	public void setPatMandal(String patMandal) {
		this.patMandal = patMandal;
	}
	public String getPinCode() {
		return pinCode;
	}
	public void setPinCode(String pinCode) {
		this.pinCode = pinCode;
	}
	public String getHospDistrict() {
		return hospDistrict;
	}
	public void setHospDistrict(String hospDistrict) {
		this.hospDistrict = hospDistrict;
	}
	public String getCmrfPriority() {
		return cmrfPriority;
	}
	public void setCmrfPriority(String cmrfPriority) {
		this.cmrfPriority = cmrfPriority;
	}
	public String getMandalNo() {
		return mandalNo;
	}
	public void setMandalNo(String mandalNo) {
		this.mandalNo = mandalNo;
	}
	public String getMandalName() {
		return mandalName;
	}
	public void setMandalName(String mandalName) {
		this.mandalName = mandalName;
	}
	public String getDistNo() {
		return distNo;
	}
	public void setDistNo(String distNo) {
		this.distNo = distNo;
	}
	public String getDistName() {
		return distName;
	}
	public void setDistName(String distName) {
		this.distName = distName;
	}
	public Integer getTreatParId() {
		return treatParId;
	}
	public void setTreatParId(Integer treatParId) {
		this.treatParId = treatParId;
	}
	public Integer getTreatSubId() {
		return treatSubId;
	}
	public void setTreatSubId(Integer treatSubId) {
		this.treatSubId = treatSubId;
	}
	public Integer getTreatProcId() {
		return treatProcId;
	}
	public void setTreatProcId(Integer treatProcId) {
		this.treatProcId = treatProcId;
	}
	public String getPatientIpNo() {
		return patientIpNo;
	}
	public void setPatientIpNo(String patientIpNo) {
		this.patientIpNo = patientIpNo;
	}
	public String getOpCRNo() {
		return opCRNo;
	}
	public void setOpCRNo(String opCRNo) {
		this.opCRNo = opCRNo;
	}
	public String getIpAddress() {
		return ipAddress;
	}
	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
	public String getEnteredOn() {
		return enteredOn;
	}
	public void setEnteredOn(String enteredOn) {
		this.enteredOn = enteredOn;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	public MultipartFile getEstimationLetterFile() {
		return EstimationLetterFile;
	}
	public void setEstimationLetterFile(MultipartFile estimationLetterFile) {
		EstimationLetterFile = estimationLetterFile;
	}
	public String getReferredBy() {
		return referredBy;
	}
	public void setReferredBy(String referredBy) {
		this.referredBy = referredBy;
	}
	public String getDeptUpdatedBy() {
		return deptUpdatedBy;
	}
	public void setDeptUpdatedBy(String deptUpdatedBy) {
		this.deptUpdatedBy = deptUpdatedBy;
	}
	public String getDeptUpdatedDate() {
		return deptUpdatedDate;
	}
	public void setDeptUpdatedDate(String deptUpdatedDate) {
		this.deptUpdatedDate = deptUpdatedDate;
	}
	public String getDeptUpdatedIp() {
		return deptUpdatedIp;
	}
	public void setDeptUpdatedIp(String deptUpdatedIp) {
		this.deptUpdatedIp = deptUpdatedIp;
	}
		

}

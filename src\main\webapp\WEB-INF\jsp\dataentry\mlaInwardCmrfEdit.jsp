<%@ page language="java" contentType="text/html; charset=ISO-8859-1"
	pageEncoding="ISO-8859-1"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%
String path = request.getContextPath();
//String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
String basePath = path+"/";
%>
<!DOCTYPE html>
<html>
<head>
<meta charset="ISO-8859-1">
<title>Mla Inward Cmrf Edit</title>
<link rel="stylesheet" href="css/jquery-ui.css">
<script type="text/javascript"
	src="https://code.jquery.com/jquery-1.11.3.min.js"></script>
<script src="js/jquery-1.12.4.js"></script>
<script src="js/jquery-ui.js"></script>
<script src="js/common/commonValidations.js"></script>
<script src="js/sweetalert2/sweetalert2.js"></script>
<style type="text/css">
fieldset {
	border: 1px solid #1F3754 !important;
	border-radius: 4px;
	padding-left: 20px !important;
	padding-right: 20px !important;
	color: #1F3754;
	box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px
		6px;
	margin: 10px;
	background: #ededed;
}
.card{
	position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: white;
    background-clip: border-box;
    border-top: 1px solid #c2cfd6;
    border-top-width: 2px;
    border-top-color: rgba(11,69,116,1) !important;
    margin-top: 10px;
    margin-bottom: 10px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
}
.card-header{
    padding: 0rem 1.25rem !important;
    border-color: #a4b7c140 !important;
    border-bottom: 1px solid #a4b7c140 !important;
    height: 30px;
    align-items: center !important;
    font-weight: 700 !important;
    color:  rgba(11,69,116,1) !important;
    margin-top: 0.5rem;
}
.card-body{
	padding: 10px;
}
.col-form-label{
	padding-left: 3px;
	font-size: 13px;
}
  .read-only {
        pointer-events: none;
        background-color: #e9ecef;
    }
    .custom-inline {
    display: inline-block;
    margin-right: 20px; /* Adjust spacing as needed */
    vertical-align: middle; /* Align vertically to middle */
}
    

</style>
</head>
<script type="text/javascript">
	<document className="onload"></document>
	function maskValue(spanId) {
		const span = document.getElementById(spanId);
		if (span) {
			const text = span.textContent.trim();
			if (text && text.length > 4) {
				const visibleStart = text.slice(0, 2);
				const visibleEnd = text.slice(-2);
				const maskedLength = text.length - 4;
				const masked = '*'.repeat(maskedLength);
				span.textContent = visibleStart + masked + visibleEnd;
			}
		}
	}

function chkvalidNo(obj) {
  	if (obj.value != "") {
  		var noofNumbers = (obj.value).length;
  		if (noofNumbers < 10) {
  			Swal.fire({
				title: "Invalid Mobile/Phone number....\nEntered "+ noofNumbers + " Number(s) Only.",
				text: '',
				icon: 'error',
				confirmButtonText: 'Okay'
			}).then((result) => {
                if (result.isConfirmed) {
                    obj.focus(); // Focus the field again after the user clicks 'Okay' on SweetAlert
                }
            });

  			obj.value = "";
  			return false;
  		}
  	} else {
  		return false;
  	}
  	var num = obj.value;
  	var chk = new Array();
  	chk = num.split("");
  	if (chk[0] == "6" || chk[0] == "7" || chk[0] == "8"
  			|| chk[0] == "9") {
  		return true;
  	} else {
  		 Swal.fire({
  	    	  title: "Enter a valid mobile number starting with '6' or '7' or '8' or '9'....",
  	    	  text: '',
  	    	  icon: 'error',
  	    	  confirmButtonText: 'Okay'
  	    	}).then((result) => {
  	                if (result.isConfirmed) {
  	                    obj.focus(); // Focus the field again after the user clicks 'Okay' on SweetAlert
  	                }
  	            });
  	  		obj.value = "";
  	  		return false;
  	}
  }
function alphaNumericOnly(txb) {
    txb.value = txb.value.replace(/[^A-Za-z0-9]/g, '');
}

function getMadalsByDistId(distCode, mandalVal) {
    var dist = $("#patDistrict").val();
    console.log(dist)
    $("#" + mandalVal).html('<option value="">Loading...</option>');
    var url = "addCMRFEntry/getMandals?district_code=" + dist;
    $.post(url, function(data, status) {
        $("#" + mandalVal).html(data);
    });
}

function getHospitalsByDistCode(distCode, hospVal){
	var distt = $("#hospDistrict").val();
	$("#"+hospVal).html('<option value="">Loading...</option>');
	var url = "mlaInwardCmrf/getHospByDistCode?district_code="+distt;
	$.post(url, function(data, status) {
        $("#"+hospVal).html(data);
    });
}

function getBanksByDistName(distName, bankName){
	var distt = $("#bankDistrict").val();
	console.log(distt)
	$("#"+bankName).html('<option value="">Loading...</option>');
	var url = "mlaInwardCmrf/getBanksByDistName?district_name="+distt;
	$.post(url, function(data, status) {
		$("#"+bankName).html(data);
		console.log("getBanksByDistName")
    });
}

function getIFSCByBankName(bankName, bankDist, bankIFSC){
	var bankName = $("#bankName").val();
	var bankDist = $("#bankDistrict").val();
	$("#"+bankIFSC).html('<option value="">Loading...</option>');
	var url = "mlaInwardCmrf/getIFSCByBankName?bank_name="+bankName+"&bank_dist="+bankDist;
	$.post(url, function(data, status) {
        $("#"+bankIFSC).html(data);
    });
}

function getBranchByIFSC(Ifsc, bankBranch){
	var bankIfsc = $("#bankIfsc").val();
	$("#"+bankBranch).html('Loading...');
	var url = "mlaInwardCmrf/getBranchByIFSC?ifsc_code="+bankIfsc;
	$.post(url, function(data, status) {
		$("#"+bankBranch).val(data);
		console.log("branchh")
    });
}

function alphaOnly(txb)
{
	txb.value=txb.value.replace(/[^\A-Za-z. ]/g, '');
}
$(document).ready(function() {
    prefillRelationFields();

	maskValue('originalBankAccNoSpan');
	maskValue('originalBankAccHolNameSpan');

	// Focus on TokenNo input field when document loads
	$('#TokenNo').focus();
});
	function validateForm() {
		var No = $('#TokenNo').val().trim();
		if (No == '') {
			alert('Please enter Token No')
			return false;
		}
		var year = $('#year').val();
	    $('#cmrfTokenNo').val(No + year);
	    return true;

	}
	 function prefillRelationFields() {
	        <%-- Check if backendData.fatherName is not null --%>
	        var father = $('#father').val().trim();
	        if (father != null) {
	            var parts = father.split(" ");
	            if (parts.length > 1) {
	                document.getElementById("guardian").value = parts[0];
	                var fatherName = parts.slice(1).join(" ");
	                document.getElementById("fatherName").value = fatherName;
	            } 
	    }
	 }
	 $( function() {
		    $( "#cmrfDate" ).datepicker({
					changeMonth: true,
					changeYear: true,
					//minDate:"01/06/2014",
					minDate:"01/06/2009",
					maxDate:new Date(),
					dateFormat:"dd/mm/yy",
				//	defaultDate: new Date(),
					//yearRange: "-0:+0"
				});
		    var prefilledDate = '<c:out value="${TokenDetails.timeStamp}"/>';
            if (prefilledDate) {
            	var dateParts = prefilledDate.split(" ")[0].split("-");
                var year = dateParts[0];
                var month = dateParts[1];
                var day = dateParts[2];

                var formattedDate = day + "/" + month + "/" + year;
                $("#cmrfDate").datepicker("setDate", formattedDate);
            }
				
		  } );
	 function getMadalsByDistId(distCode, mandalVal) {
	        var dist = $("#patDistrict").val();
	       
	        $("#" + mandalVal).html('<option value="">Loading...</option>');
	        var url = "addCMRFEntry/getMandals?district_code=" + dist;
	        $.post(url, function(data, status) {
	            $("#" + mandalVal).html(data);
	        });
	    }

		function getHospitalsByDistCode(distCode, hospVal){
			var distt = $("#hospDistrict").val();
			$("#"+hospVal).html('<option value="">Loading...</option>');
			var url = "inwardCmrf/getHospByDistCode?district_code="+distt;
			$.post(url, function(data, status) {
	            $("#"+hospVal).html(data);
	        });
		}
		function getVillByMdlId(mandalId, distId, villVal) {
			var mandal = $("#patMandal").val();
			var dist = $("#patDistrict").val();
			$("#" + villVal).html('<option value="">Loading...</option>');
			var url = "mlaInwardCmrf/getVillages?mandal_code=" + mandal +"&dist_code="+dist;
			$.post(url, function(data, status) {
				console.log(data);
				$("#" + villVal).html(data);
			});
		}
		  function toUpperCaseAllFields() {
	            var inputs = document.querySelectorAll('input[type="text"],textarea');
	            inputs.forEach(function(input) {
	                input.value = input.value.toUpperCase();
	            });
	        }

	        document.addEventListener('input', function(event) {
	            if ((event.target.tagName === 'INPUT' && event.target.type === 'text')||event.target.tagName === 'TEXTAREA') {
	                event.target.value = event.target.value.toUpperCase();
	            }
	        });
	        function getBankAndBranch(input) {
			     
			    var basePath = "<%= basePath %>";
		        var ifsc = input.value;
		        console.log(ifsc)
		        let formData = {
				        ifscCode : ifsc
				     };
		        $("#bankName").val("");
		        $('#bankBranch').val("");
		        if (ifsc.length === 11) {
		        	const container = Swal.getContainer();
			        const backgroundElements = document.querySelectorAll('body > *:not(.swal2-container)');
			        backgroundElements.forEach(element => {
			            element.style.filter = 'blur(4px)';
			        });
			        // Show the SweetAlert loading spinner
			        Swal.fire({
			            title: 'Loading...',
			            text: 'Please wait while we process your request.',
			            allowOutsideClick: false,
			            didOpen: () => {
			   	            Swal.showLoading();
			               
			            }
			        });
		            $.ajax({
		            	 url: basePath+'getIfsc', // Spring Boot endpoint
		                type: 'POST',
		                data: formData,
		                success: function(response) {
		                	 Swal.close();
		               	  backgroundElements.forEach(element => {
		                         element.style.filter = 'none'; // Remove blur effect
		                     });
		                	if (response) {
		                	$("#bankName").val(response.bankName);
		                	$('#bankBranch').val(response.branch);
		                }else {
		                	Swal.fire({
	                            icon: 'error',
	                            title: 'Invalid IFSC / No Data Found.',
	                        });
	                    }},
		                error: function(error) {
		                	 Swal.close();
		                     backgroundElements.forEach(element => {
		                         element.style.filter = 'none'; // Remove blur effect
		                     });
		                	Swal.fire({
	                            icon: 'error',
	                            title: 'Invalid IFSC / No Data Found.',
	                        });
		                }
		            });
		        } else {
		        	 Swal.fire({
		                 icon: 'warning',
		                 title: 'Invalid Input',
		                 text: 'IFSC code must be 11 characters long.',
		             });
		        }
		 }
		function validate(){
			if($("#patientName").val().trim()==""){
	  			alert("Enter Patient Name");
	  			return false;
	  		}
			if($("#age").val()==""){
				alert("Enter Age");
				$("#age").focus();
				return false;
			}
			if($("#guardian").val()=="0"){
	  			alert("Select Guardian");
	  			return false;
	  		}
	  		if($("#fatherName").val().trim()==""){
	  			alert("Enter Guardian Name");
	  			return false;
	  		}
			if($("#aadharNo").val()=="" ){
					alert("Enter Aadhaar No");
					$("#aadharNo").focus();
					return false;
				} 
			if($("#mobileNo").val()=="" ){
				alert("Enter Mobile No");
				$("#mobileNo").focus();
				return false;
			}
			if($("#incomeCerNo").val()=="" && $("#newFscNo").val()==""){
				alert("Either Income No or New FSC No is mandatory");
				return false;
			}
			if($("#patDistrict").val()=="0" || $("#patDistrict").val()==""){
				alert("Select District");
				$("#patDistrict").focus();
				return false;
			}
			if($("#patMandal").val()=="0" || $("#patMandal").val()==""){
				alert("Select Mandal");
				$("#patMandal").focus();
				return false;
			}
			if($("#patVillage").val()=="0" || $("#patVillage").val()==""){
				alert("Select Village");
				$("#patVillage").focus();
				return false;
			}
			if($("#patAddress").val()==""){
				alert("Enter Patient Address");
				$("#patAddress").focus();
				return false;
			} 	
	  		if(($("#pinCode").val()=="")|| ($("#pinCode").val()=="0")){
	  			alert("Enter Pincode");
	  			return false;
	  		}
	  		/*  if ($("#bankDistrict").val() == "0") {
	             alert("Please select Bank District.");
	             $("#bankDistrict").focus();
	             return false; 
	         } */
	         
	         if ($("#bankIfsc").val().trim()==="") {
	             alert("Please Enter IFSC code.");
	             $("#bankIfsc").focus();
	             return false; // Prevent form submission
	         }
	         
	         if ($("#bankName").val().trim()==="") {
	             alert("Please Enter valid IFSC to get Bank Name.");
	             $("#bankName").focus();
	             return false; // Prevent form submission
	         }
	         
	         if ($("#bankBranch").val().trim() === "") {
	             alert("Please Enter Valid IFSC to get Bank Branch");
	             $("#bankBranch").focus();
	             return false; // Prevent form submission
	         }
	         
	         if ($("#bankAccNo").val().trim() === "") {
	             alert("Please enter the Account Number.");
	             $("#bankAccNo").focus();
	             return false; // Prevent form submission
	         }
	  		
	         if ($("#bankAccHolderName").val().trim() === "") {
	             alert("Please enter the Bank Account Holder Name.");
	             $("#bankAccHolderName").focus();
	             return false; // Prevent form submission
	         }
	         
			if($("#cmrfDate").val()==""){
	  			alert("Please enter the date in MLA letter DATE");
	  			return true;
	  		}

			if($("#hospDistrict").val()=="0"){
				alert("Select Hospital District");
				$("#hospDistrict").focus();
				return false;
			}
	  		if($("#hospCode").val()=="0"){
				alert("Select Hospital");
				$("#hospCode").focus();
				return false;
			}
			if($("#patientIpNo").val().trim()=="" && $("#admissNo").val()==""){
				alert("Either IP Number or Admission Number for Hospital Details is Mandatory");
				$("#patientIpNo").focus();
				return false;
			}
			if($("#purpose").val().trim() === ""){
				alert("Enter Details of Treatment");
				$("#purpose").focus();
				return false;
			}
			 if ($("#status").val() == "0" || $("#status").val()=="") {
	             alert("Please select Status.");
	             $("#status").focus();
	             return false; // Prevent form submission
	         }
			 if ($('#reasonGroup').is(':visible') && $("#reason").val().trim() === "") {
		            alert("Please enter reasons.");
		            $("#remarks").focus();
		            return false; // Prevent form submission
		     }
			 if ($('#remarksGroup').is(':visible') && $("#remarks").val().trim() === "") {
		            alert("Please enter remarks.");
		            $("#remarks").focus();
		            return false; // Prevent form submission
		     }
	  		
	  		
			 Swal.fire({
		            title: 'Are you sure?',
		            text: "Do you want to submit the details now?",
		            icon: 'question',
		            showCancelButton: true,
		            confirmButtonColor: '#3085d6',
		            cancelButtonColor: '#d33',
		            confirmButtonText: 'Yes, submit it!'
		        }).then((result) => {
		            if (result.isConfirmed) {
		                $("#inwardCmrf").attr("action", "saveTokenDetails"); // Change to your new action URL
		                document.getElementById("inwardCmrf").submit(); // Submit the form
		            }
		        });
	  	}
		  $(document).ready(function() {
			  $('#status').change(function() {
			        var statusValue = $(this).val();
			        $('#reasonGroup').hide();
			        $('#remarksGroup').hide();

			        if (statusValue === '4' || statusValue === '6') {
			            $('#reasonGroup').show();

			            // Change the options based on the selected status
			            if (statusValue === '4') {
			                $('#reason').html(`
			                    <option value="">--Select Reason--</option>
			                    <option value="People Representative Original Letter">People Representative Original Letter</option>
			                    <option value="Original Hospital Bills">Original Hospital Bills</option>
			                    <option value="Aadhaar Copy">Aadhaar Copy</option>
			                    <option value="Income Certificate / New Ration Card">Income Certificate (valid for one year) / New Ration Card</option>
			                    <option value="Bank Passbook">Bank Passbook (Copy of First Page)</option>
			                    <option value="other">Others</option>
			                `);
			            } else if (statusValue === '6') {
			                $('#reason').html(`
			                    <option value="">--Select Reason--</option>
			                    <option value="Incorrect Ration (FSC) or Income Details">Incorrect Ration (FSC) OR Income Details</option>
			                    <option value="Incorrect Hospital Bills">Incorrect Hospital Bills</option>
			                    <option value="Original Hospital Bills not Submitted">Original Hospital Bills not Submitted</option>
			                    <option value="Original People Representative Letter not Submitted">Original People Representative Letter not Submitted</option>
			                    <option value="other">Others</option>
			                `);
			            }
			        }
			    });

			    $('#reason').change(function() {
			        if ($(this).val() === 'other') {
			            $('#remarksGroup').show();
			        } else {
			            $('#remarksGroup').hide();
			        }
			});
	});
		  

		
		
		 <c:if test="${not empty errors}">
	        // Define function to show SweetAlert
	        function showSweetAlert() {
	            Swal.fire({
	                icon: 'error',
	                title: 'Oops...',
	                html: '<ul>' +
	                      '<c:forEach items="${errors}" var="error">' +
	                          '<li>${error}</li>' +
	                      '</c:forEach>' +
	                      '</ul>',
	                confirmButtonText: 'OK'
	            });
	        }

	        // Call the function to show SweetAlert
	        showSweetAlert();
	    </c:if>
</script>
<body>
	<div class="container" style="min-height: 500px; width: 90%">

		<fieldset>
			<div class="card border-green dash-card z-1">
			<c:if test="${not empty error}">
				<div class="alert alert-danger" role="alert">
					<c:out value="${error}" />
				</div>
			</c:if>
			<c:if test="${not empty success}">
				<div class="alert alert-success" role="alert">
					<c:out value="${success}" />
				</div>
			</c:if>
				<div class="card-header">Edit & Verify Applicant Details</div>
				<form action="getTokenDetails" method="post" id="inwardCmrf">
				    <input type="hidden" value="${TokenDetails.mlaCmrfNo}" name="mla_cmrf_no"/>
					<div class="card-body">
					     
							
					
						<div class="form-group row" style="margin-top: 20px;">
						<input type="hidden" id="cmrfTokenNo" name="cmrfTokenNo">
							<label for="cmrfNo" class="col-sm-2 col-form-label">Enter CMRF
								Token No :<span style="color: red;">*</span>
							</label>
							<div class="col-sm-3">
								<input type="text" class="form-control" id="TokenNo"
									placeholder="Enter Token No" name="TokenNo" value="${No}">
							</div>
							<div class="col-sm-3">
							<select class="form-control" id="year"
											name="year" style="padding: 0px;">
											<option value="/TCMRF/2025">/TCMRF/2025</option>
											<option value="/TCMRF/2024">/TCMRF/2024</option>
											<option value="/TCMRF/2023">/TCMRF/2023</option>
											<option value="/TCMRF/2022">/TCMRF/2022</option>
											<option value="/TCMRF/2021">/TCMRF/2021</option>
										</select>
										</div>
							<div class="col-sm-2">
								<button class="btn btn-primary" onclick="return validateForm()">Submit</button>
							</div>
						</div>
						<c:if test="${not empty TokenDetails}">
						 <div
								style="box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgb(209, 213, 219) 0px 0px 0px 1px inset;; padding: 5px;">
								<h4 class="card-header text-center">Applicant Data pulled from various APIS</h4>
						        <h4 style="font-weight: 600;">Applicant Income Details</h4>
						         <c:if test="${not empty msg}">
        					        <div style="color: red;font-size: 12px;font-weight: 600;" class="text-center">${msg}</div>
        					     </c:if>
        					     <c:if test="${not empty incomeDetails}">
							    <table class="table table-bordered table-striped"  style="font-size: 13px;">
							        <thead>
							        <tr>
							            <th>Income No</th>
							            <th>Approved Date</th>
							            <th>Name</th>
							            <th>Father Name</th>
							            </tr>
							        </thead >
							        <tbody class="text-center">
							            <tr>
							               <td>${incomeDetails.incomeNo}</td>
							               <td>${incomeDetails.incomeDate}</td>
							               <td>${incomeDetails.name}</td>
							               <td>${incomeDetails.fatherName}</td>
							            </tr>
							        </tbody>
							    </table>
						</c:if>
						<h4 style="font-weight: 600">Applicant Aarogya Sri Details(Claimed)</h4>
						   <c:if test="${empty patientDetails && not empty TokenDetails}">
						       <div style="color: red;font-size: 12px;font-weight: 600;" class="text-center">Aarogya Sri Details Not found</div>
						  </c:if>
						<c:if test="${not empty patientDetails}">
						              <div class="table-responsive">
								      <table class="table table-bordered table-striped" style="font-size: 12px;">
						                <thead class="thead-dark">
						                    <tr>
						                        <th style="width: 5%">SNo</th>
						                        <th style="width: 10%">Patient IP</th>
						                        <th style="width: 15%">Name</th>
						                        <th style="width: 10%">Aadhar Number</th>
						                        <th style="width: 10%">Mobile Number</th>
						                        <th style="width: 10%">FSC Number</th>
						                        <th style="width: 10%">Date of Admission</th>
						                        <th style="width: 10%">Date of Discharge</th>
						                        <th style="width: 20%">Hospital Name</th>
						                        <th style="width: 20%">Type of Disease</th>
						                        <th style="width: 10%">Bill Amount</th>
						                        <th style="width: 10%">Claim Amount</th>
						                    </tr>
						                </thead>
						                <tbody>
						                    <c:forEach var="detail" items="${patientDetails}" varStatus="status">
						                    <tr>
						                        <td>${status.index + 1}</td>
						                        <td>${detail.patientIp}</td>
						                        <td>${detail.patientName}</td>
						                        <td>${detail.aadharNumber}</td>
						                        <td>${detail.patientMobileNumber}</td>
						                        <td>${detail.fscNumber}</td>
						                        <td>${detail.dateOfAdmission}</td>
						                        <td>${detail.dateOfDischarge}</td>
						                        <td>${detail.hospitalName}</td>
						                        <td>${detail.typeOfDisease}</td>
						                        <td>${detail.billAmount}</td>
						                        <td>${detail.claimAmount}</td>
						                    </tr>
						                </c:forEach>
						                </tbody>
						            </table>
						            </div>
						</c:if>
						<h4 style="font-weight: 600">Food Security Card Details (Ration Card)</h4>
						<c:if test="${not empty TokenDetails}">
								 <div style="color: red;font-size: 12px;font-weight: 600;" class="text-center">FSC No. not found</div>
						</c:if>
						</div>
				 </c:if>
				 
						
						<c:if test="${not empty TokenDetails}">
						
							<div
								style="box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgb(209, 213, 219) 0px 0px 0px 1px inset;; padding: 5px;">
								<div class="form-group row" style="margin-top: 20px;">
									<label for="patientName" class="col-sm-2 col-form-label">Name
										(as per Aadhar) :<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<input type="text" class="form-control" id="patientName" name="patientName"
											value="${TokenDetails.patientName}" onkeyup="return alphaOnly(this);" onchange="trimSpace(this);">
									</div>
									<label for="gender" class="col-sm-2 col-form-label">
										Gender :<span style="color: red;">*</span>
									</label>
									<div class="col-sm-1 col-form-label">
										<input class="form-check-input" type="radio" id="genderMale"
											name="gender" value="M"
											${TokenDetails.gender == 'M' ? 'checked' : ''}> <label
											class="form-check-label" for="genderMale"
											style="padding: 3px;">Male</label>
									</div>
									<div class="col-sm-1 col-form-label">
										<input class="form-check-input" type="radio" id="genderFemale"
											name="gender" value="F"
											${TokenDetails.gender == 'F' ? 'checked' : ''}> <label
											class="form-check-label" for="genderFemale"
											style="padding: 3px;">Female</label>
									</div>

								</div>
								<div class="form-group row" style="margin-top: 20px;">
									<input type="hidden" name="father" id="father"
										value="${TokenDetails.fatherSonOf}"> <label for="age"
										class="col-sm-2 col-form-label"> Age :<span
										style="color: red;">*</span></label>
									<div class="col-sm-4">
										<input type="text" class="form-control" id="age" name="age"
											value="${TokenDetails.age}" onkeyup="intOnly(this);">
									</div>
									<label for="guardian" class="col-sm-2 col-form-label">
										Relation :<span style="color: red;">*</span>
									</label>

									<div class="col-sm-1">
										<select class="form-control" id="guardian"
											name="guardian" style="padding: 0px;">
											<option value="S/O">S/O</option>
											<option value="D/O">D/O</option>
											<option value="W/O">W/O</option>
											<option value="C/O">C/O</option>
										</select>
									</div>
									<div class="col-sm-3">
										<input type="text" class="form-control" id="fatherName"
											name="fatherName" onkeyup="return alphaOnly(this);" onchange="trimSpace(this);">
									</div>
								</div>
								<div class="form-group row" style="margin-top: 20px;">

									<label for="aadharNo" class="col-sm-2 col-form-label">Aadhar No
										:<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<input type="text" class="form-control" id="aadharNo"
											name="aadharNo" maxlength="12" value="${TokenDetails.aadharNo}" onkeyup="intOnly(this);">
									</div>
									<label for="mobileNo" class="col-sm-2 col-form-label">
										Mobile No :<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<input type="text" class="form-control" id="mobileNo"
											name="mobileNo" maxlength="10" value="${TokenDetails.mobileNo}" onkeyup="intOnly(this);" onchange="chkvalidNo(this)">
									</div>


								</div>
								<div class="form-group row" style="margin-top: 20px;">

									<label for="incomeCerNo" class="col-sm-2 col-form-label">Income Certificate No
										:<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<input type="text" class="form-control" id="incomeCerNo"
											name="incomeCerNo" maxlength="14" value="${TokenDetails.incomeNo}" onkeyup="alphaNumericOnly(this);">
									</div>
									<label for="newFscNo" class="col-sm-2 col-form-label">
										New FSC No :<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<input type="text" class="form-control" id="newFscNo"
											name="newFscNo" maxlength="12" value="${TokenDetails.newFscNo}"  onkeyup="alphaNumericOnly(this);">
									</div>

								</div>
								
								<div class="form-group row" style="margin-top: 20px;">

									<label for="district" class="col-sm-2 col-form-label">District
										:<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<select class="form-control" id="patDistrict" name="patDistrict" onchange="getMadalsByDistId(this,'patMandal')">
											<option value="0">--Select a District--</option>
											<c:forEach var="district" items="${districts}">
												<option value="${district.distNo}"
													<c:if test="${district.distNo == TokenDetails.patDistrict}">
                                                    selected="selected"
                                                 </c:if>>${district.distName}</option>
											</c:forEach>
										</select>
									</div>
									<label for="patMandal" class="col-sm-2 col-form-label">
										Mandal :<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<select class="form-control" id="patMandal" name="patMandal" onchange="getVillByMdlId(this,'patDistrict','patVillage')">
											<option value="0">--Select a Mandal--</option>
											<c:forEach var="mandal" items="${mandals}">
												<option value="${mandal.mandalNo}"
													<c:if test="${mandal.mandalNo == TokenDetails.patMandal}">
                                                    selected="selected"
                                                 </c:if>>${mandal.mandalName}</option>
											</c:forEach>
										</select>
									</div>


								</div>
								<div class="form-group row" style="margin-top: 20px;">
										<label for="patMandal" class="col-sm-2 col-form-label">
										Villages :<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<select class="form-control" id="patVillage" name="patVillage">
											<option value="0">--Select a Villages--</option>
											<c:forEach var="village" items="${villages}">
												<option value="${village.villNo}"
													<c:if test="${village.villNo == TokenDetails.patVillage}">
                                                    selected="selected"
                                                 </c:if>>${village.villName}</option>
											</c:forEach>
										</select>
									</div>
								</div>
								<div class="form-group row" style="margin-top: 20px;">

									<label for="pincode" class="col-sm-2 col-form-label">Pincode
										:<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<input type="text" class="form-control" id="pinCode"
											name="pinCode" maxlength="6"  value="${TokenDetails.pincode}" onkeyup="intOnly(this);">
									</div>
									<label for="patAddress" class="col-sm-2 col-form-label">
										Address :<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<textarea class="form-control" id="patAddress" name="patAddress"
											rows="3" onchange="trimSpace(this);">${TokenDetails.patAddress}</textarea>
									</div>
								</div>
							</div>
							 <div
								style="box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgb(209, 213, 219) 0px 0px 0px 1px inset;; padding: 5px;">
								<h4 class="card-header">Bank Details</h4>
								 <div class="form-group row" style="margin-top: 20px;">
								   <%-- <label for="district" class="col-sm-2 col-form-label">District
										:<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<select class="form-control" id="bankDistrict" name="bankDistrict" onchange="getBanksByDistName(this,'bankName')"">
											<option value="0">--Select a District--</option>
											<c:forEach var="district" items="${districts}">
												<option value="${district.distName}"
													<c:if test="${district.distName == TokenDetails.bankDist}">
                                                    selected="selected"
                                                 </c:if>>${district.distName}</option>
											</c:forEach>
										</select>
									</div> --%>
									<label for="ifsc" class="col-sm-2 col-form-label">IFSC :<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
									<input type="text" class="form-control" id="bankIfsc"
											name="bankIfsc" maxlength="11"  value="${TokenDetails.bankIfsc}" onkeyup="alphaNumericOnly(this);"
										onchange="getBankAndBranch(this);">
										<%-- <select class="form-control" id="bankIfsc" name="bankIfsc" onchange="getBranchByIFSC(this,'bankBranch')">
											<option value="0">--Select IFSC--</option>
											<c:forEach var="ifsc" items="${bankIfsc}">
												<option value="${ifsc.bankIfsc}"
													<c:if test="${ifsc.bankIfsc == TokenDetails.bankIfsc}">
                                                    selected="selected"
                                                 </c:if>>${ifsc.bankIfsc}</option>
											</c:forEach>
										</select> --%>
									</div>
								</div>
									
								
								 <div class="form-group row" style="margin-top: 20px;">
								     <label for="district" class="col-sm-2 col-form-label">Bank Name
										:<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
									<input type="text" class="form-control" id="bankName"
											name="bankName"  value="${TokenDetails.bankName}" readonly="readonly" style="background: white;">
										<%-- <select class="form-control" id="bankName" name="bankName" onchange="getIFSCByBankName(this,'bankDistrict','bankIfsc')">
											<option value="0">--Select a Bank--</option>
											<c:forEach var="bank" items="${bankNames}">
												<option value="${bank.bankName}"
													<c:if test="${bank.bankName == TokenDetails.bankName}">
                                                    selected="selected"
                                                 </c:if>>${bank.bankName}</option>
											</c:forEach>
										</select> --%>
									</div>
									<label for="district" class="col-sm-2 col-form-label">Branch :<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<textarea name="bankBranch" id="bankBranch" readonly="readonly">${TokenDetails.bankBranch}</textarea>
									</div>
								</div>
								<div class="form-group row" style="margin-top: 20px;">
								    <%-- <label for="ifsc" class="col-sm-2 col-form-label">Account No :<span style="color: red;">*</span></label> --%>
									<%-- <div class="col-sm-4"> --%>
										<input type="hidden" disabled="true" name="bankAccNo" id="originalBankAccNo" class="form-control" maxlength="18" onkeyup="intNumOnly(this);" value="${TokenDetails.bankAccNo }" />
									<%-- </div> --%>
									<label for="ifsc" class="col-sm-2 col-form-label">Account No :<span style="color: red;">*</span></label>
									<div class="col-sm-4">
										<span id="originalBankAccNoSpan" style="color: green;">
											<c:choose>
												<c:when test="${not empty TokenDetails.bankAccNo}">${TokenDetails.bankAccNo}</c:when>
												<c:otherwise>-NA-</c:otherwise>
											</c:choose>
										</span>
									</div>

								    <label for="ifsc" class="col-sm-2 col-form-label">Re-enter Account No as per passbook :<span style="color: red;">*</span></label>
									<div class="col-sm-4">
										<input type="text" name="bankAccNo" id="bankAccNo" class="form-control" maxlength="18" onchange="document.getElementById('bankAccNoNote').style.display = (this.value !== document.getElementById('originalBankAccNo').value) ? 'block' : 'none';" onkeyup="return intNumOnly(this);" onselectstart="return false" onpaste="return false;" onCopy="return false" onCut="return false" onDrag="return false" onDrop="return false" autocomplete="off" />
									</div>
									<div class="col-sm-6"></div>
									<label for="note" id="bankAccNoNote" class="col-sm-6 col-form-label" style="color: red;display: none;">Account numbers do not match. Please verify your bank passbook and submit.</label>
								</div>
								<div class="form-group row" style="margin-top: 20px;">
									<input type="hidden" disabled="true" name="bankAccHolderName" id="originalBankAccHolName" class="form-control" maxlength="50" onkeyup="return alphaOnly(this);" value="${TokenDetails.bankAccHolderName }" />

									<label for="ifsc" class="col-sm-2 col-form-label">Bank A/c Holder Name :<span style="color: red;">*</span></label>
									<div class="col-sm-4">
										<span id="originalBankAccHolNameSpan" style="color: green;">
											<c:choose>
												<c:when test="${not empty TokenDetails.bankAccHolderName}">${TokenDetails.bankAccHolderName}</c:when>
												<c:otherwise>-NA-</c:otherwise>
											</c:choose>
										</span>
									</div>
																								
									<label for="ifsc" class="col-sm-2 col-form-label">Bank A/c Holder Name as per passbook :<span style="color: red;">*</span></label>
									<div class="col-sm-4">
											<input type="text" name="bankAccHolderName" id="bankAccHolderName" class="form-control" maxlength="50" onchange="document.getElementById('bankAccNameNote').style.display = (this.value !== document.getElementById('originalBankAccHolName').value) ? 'block' : 'none'; return trimSpace(this);" onkeyup="return alphaOnly(this);" onselectstart="return false" onpaste="return false;" onCopy="return false" onCut="return false" onDrag="return false" onDrop="return false" autocomplete="off" />
									</div>
									<div class="col-sm-6"></div>
									<label for="note" id="bankAccNameNote" class="col-sm-6 col-form-label" style="color: red;display: none;">Account Holder names do not match. Please verify your bank passbook and submit.</label>
								</div>
								</div> 
							<div
								style="box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgb(209, 213, 219) 0px 0px 0px 1px inset;; padding: 5px;">
								<h4 class="card-header">People Representative</h4>
								<div class="form-group row" style="margin-top: 20px;">
									<label for="recommendedby" class="col-sm-2 col-form-label">Recommended
										By :<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<select class="form-control read-only" id="recommendedBy" name="recommendedBy">
											<option value="0">Select Recommended By</option>
											<c:forEach var="recommended" items="${recommendedList}">
												<option value="${recommended.constNo}"
													<c:if test="${recommended.constNo == TokenDetails.recommendedBy}">
                                                    selected="selected"
                                                 </c:if>>${recommended.constName}</option>
											</c:forEach>
										</select>
									</div>
									<label for="cmrfDate" class="col-sm-2 col-form-label">Letter
										Date :<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<input type="text" class="form-control" id="cmrfDate"
											name="cmrfDate"  value="${TokenDetails.timeStamp}">
									</div>

								</div>
							</div>
							<div
								style="box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgb(209, 213, 219) 0px 0px 0px 1px inset;; padding: 5px;">
								<h4 class="card-header">Hospital Details</h4>
								<div class="form-group row" style="margin-top: 20px;">
									<label for="district" class="col-sm-2 col-form-label">District
										:<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<select class="form-control" id="hospDistrict" name="hospDistrict" onchange="getHospitalsByDistCode(this,'hospCode')">
											<option value="0">Select a District</option>
											<c:forEach var="district" items="${districts}">
												<option value="${district.distNo}"
													<c:if test="${district.distNo == hospDistCode}">
                                                    selected="selected"
                                                 </c:if>>${district.distName}</option>
											</c:forEach>
										</select>
									</div>
									<label for="hospital" class="col-sm-2 col-form-label">Hospital
										:<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<select class="form-control" id="hospCode" name="hospCode" >
											<option value="0">Select a Hospital</option>
											<c:forEach var="hospital" items="${hospitalList}">
												<option value="${hospital.hospCode}"
													<c:if test="${hospital.hospCode == TokenDetails.hospCode}">
                                                    selected="selected"
                                                 </c:if>>${hospital.hospName}</option>
											</c:forEach>
										</select>
									</div>
								</div>
								<div class="form-group row" style="margin-top: 20px;">
									<label for="patientIpNo" class="col-sm-2 col-form-label">In-Patient(IP)
										No :
									</label>
									<div class="col-sm-4">
										<input type="text" class="form-control" id="patientIpNo"
											name="patientIpNo" maxlength="20" value="${TokenDetails.patientIp}" onchange="trimSpace(this);">
									</div>
									<label for="admissNo" class="col-sm-2 col-form-label">Admission No / Bill No / M.R. No / U.M.R. No / UHID No :<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<input type="text" class="form-control" id="admissNo"
											name="admissNo" maxlength="20" value="${TokenDetails.admissionNo}" onchange="trimSpace(this);">
									</div>
								</div>
								<div class="form-group row" style="margin-top: 20px;">
									<label for="purpose" class="col-sm-2 col-form-label">Details
										of Treatment :<span style="color: red;">*</span>
									</label>
									<div class="col-sm-4">
										<textarea class="form-control" id="purpose"
											name="purpose" maxlength="150" onchange="trimSpace(this);">${TokenDetails.purpose} </textarea>
									</div>
								</div>
							</div>
							<div
								style="box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgb(209, 213, 219) 0px 0px 0px 1px inset;; padding: 5px;">
								<h4 class="card-header">Documents Submitted</h4>
								<div id="documents" class="row" style="font-size: 13px;">
								<div class="form-check col-md-4">
							        <input type="checkbox" class="form-check-input" name="mlaLetter" id="mlaLetter"
							        <c:if test="${TokenDetails.mlaLetterCopy == 'true'}">checked</c:if>/>
							        <label class="form-check-label" for="mlaLetter" style="margin-top: 5px;">People Representative Original Letter</label>
							    </div>
							     <div class="form-check col-md-4">
							        <input type="checkbox" class="form-check-input" name="hospitalBills" id="hospitalBills"
							        <c:if test="${TokenDetails.hospBillsCopy == 'true'}">checked</c:if>/>
							        <label class="form-check-label" for="hospitalBills" style="margin-top: 5px;">Original Hospital Bills</label>
							    </div>
								
								<div class="form-check col-md-4">
							        <input type="checkbox" class="form-check-input" name="aadhaarCopy" id="aadhaarCopy"
							         <c:if test="${TokenDetails.aadharCopy == 'true'}">checked</c:if>/>
							        <label class="form-check-label" for="aadhaarCopy" style="margin-top: 5px;">Aadhaar Copy</label>
							    </div>
							    <div class="form-check col-md-4">
							        <input type="checkbox" class="form-check-input" name="fscRationCard" id="fscRationCard"
							         <c:if test="${TokenDetails.fscCopy == 'true'}">checked</c:if>/>
							        <label class="form-check-label" for="fscRationCard" style="margin-top: 5px;">New Food Security Card Copy (Ration Card)</label>
							    </div>
							   
							    <div class="form-check col-md-4">
							        <input type="checkbox" class="form-check-input" name="incomeCertificate"  id="incomeCertificate"
							          <c:if test="${TokenDetails.incCerCopy == 'true'}">checked</c:if>/>
							        <label class="form-check-label" for="incomeCertificate" style="margin-top: 5px;">Income Certificate (valid for one year)</label>
							    </div>
							    
							    
							    <div class="form-check col-md-4">
							        <input type="checkbox" class="form-check-input" name="bankPassbook" id="bankPassbook"
							         <c:if test="${TokenDetails.bankPassCopy == 'true'}">checked</c:if>/>
							        <label class="form-check-label" for="bankPassbook" style="margin-top: 5px;">Bank Passbook (Copy of First Page)</label>
							    </div>
							     
							</div>

							</div>
							<div
								style="box-shadow: rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgb(209, 213, 219) 0px 0px 0px 1px inset;; padding: 5px;">
								<h4 class="card-header">Update Status</h4>
								<div class="form-group row" style="margin-top: 20px;">
								  <label for="status" class="col-sm-2 col-form-label">Update Status:<span style="color: red;">*</span>
								 </label>
								 <div class="col-sm-6">
								     <select class="form-control" id="status" name="status">
								            <option value="0">--Select Status--</option>
								            <option value="2">Documents Verification Pending</option>
								            <option value="4">Return to People Representative</option>
								            <option value="6">Rejected</option>
								            <option value="7">Sent for Hospital Verification</option>
								       </select>
								 </div>
								</div>
								<div class="form-group row" style="margin-top: 20px;display: none" id="reasonGroup">
								  <label for="status" class="col-sm-2 col-form-label">Select Reason:<span style="color: red;">*</span>
								 </label>
								 <div class="col-sm-6">
								     <select class="form-control" id="reason" name="reason">
								      </select>
								 </div>
								</div>
								<div class="form-group row" id="remarksGroup" style="display: none; margin-top: 20px;">
								    <label for="remarks" class="col-sm-2 col-form-label">Remarks:<span style="color: red;">*</span></label>
								    <div class="col-sm-6">
								        <textarea class="form-control" id="remarks" name="remarks" rows="3" maxlength="150"></textarea>
								    </div>
								</div>
							</div>
							<div class="text-center">
								<button class="btn btn-primary" onclick="validate()" type="button">Submit</button>
							</div>
						</c:if>
					</div>
				</form>

			</div>
		</fieldset>




	</div>
</body>
</html>
package com.cgg.common;

import java.awt.Color;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDate;
import java.util.Calendar;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.cgg.dataentry.model.LOCMLACMRFEntryModel;
import com.cgg.dataentry.model.LocMlaCmrfPrintDTO;
import com.cgg.hospital.model.InwardCmrf;
import com.lowagie.text.Chunk;
import com.lowagie.text.Document;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.FontFactory;
import com.lowagie.text.Image;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Phrase;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import com.lowagie.text.pdf.draw.LineSeparator;

public class PRTemplatePDF {

    public void generatePDF(HttpServletRequest request, HttpServletResponse response) {
        try {

            HttpSession session=request.getSession();
            ServletContext context = session.getServletContext();       
            String context_path=context.getRealPath("/");;
            System.out.println("context path is -------> "+context_path);
		    String userId = (String)session.getAttribute("userid");
            System.out.println("USER ID in Template PDF : "+ userId);

            // Retrieve formData object from the request/session
            InwardCmrf formData = (InwardCmrf) request.getAttribute("formData");
            if (formData == null) {
                System.out.println("Form data not found in the request.");
                return;
            }

            // Set content type to PDF
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=" + formData.getMla_cmrf_no() + ".pdf");

            // Create a new document
            Document document = new Document();
            OutputStream out = response.getOutputStream();
            PdfWriter.getInstance(document, out);

            document.open();

            // Define fonts
            // Font titleFont = new Font(Font.HELVETICA, 14, Font.BOLD);
            // Font normalFont = new Font(Font.HELVETICA, 10);
            // Font boldFont = new Font(Font.HELVETICA, 11, Font.BOLD);
            
            BaseFont bookmanBaseFont = BaseFont.createFont(context_path+"fonts/bookman-old-style.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            Font normalFont = new Font(bookmanBaseFont, 10);
            Font boldFont = new Font(bookmanBaseFont, 11, Font.BOLD);

            // Add title
            // Paragraph title = new Paragraph("PR Template", titleFont);
            // title.setAlignment(Element.ALIGN_CENTER);
            // document.add(title);

            document.add(new Paragraph("\n")); // Add blank line
            document.add(new Paragraph("\n")); // Add blank line
            document.add(new Paragraph("\n")); // Add blank line
            document.add(new Paragraph("\n")); // Add blank line
            document.add(new Paragraph("\n")); // Add blank line

            if (!"cons_vkr".equals(userId) && !"mlc_ascons6".equals(userId)) {
                // Add content
                Paragraph date = new Paragraph("Date: "+formData.getCurrentDate(), normalFont);
                date.setAlignment(Element.ALIGN_RIGHT);
                document.add(date);
                document.add(new Paragraph("\n"));

                document.add(new Paragraph("To,", boldFont));
                document.add(new Paragraph("The Hon'ble Chief Minister,", boldFont));
                document.add(new Paragraph("Government of Telangana,", boldFont));
                document.add(new Paragraph("Hyderabad.", boldFont));

                document.add(new Paragraph("\n"));

                Paragraph letterNo = new Paragraph("D.O.Lr.No. _________ /"+formData.getCname()+"/"+formData.getMla_cmrf_year()+"/Dt. _________", boldFont);
                letterNo.setAlignment(Element.ALIGN_CENTER);
                document.add(letterNo);

                document.add(new Paragraph("\n"));
            }

            document.add(new Paragraph("Respected Sir,", boldFont));
            document.add(new Paragraph("\n"));

            Paragraph m1 = new Paragraph("     I herewith submit the application of Sri/Smt. "+ formData.getPatientName() +", "+ formData.getFatherName() +", R/o "+ formData.getPatAddress() +" District "+ formData.getDistName() +" seeking financial assistance from Chief Minister's Relief Fund. The following are the details.",normalFont);
            m1.setAlignment(Element.ALIGN_JUSTIFIED);
            document.add(m1);

            document.add(new Paragraph("\n"));

            // Add a table for details
            PdfPTable table = new PdfPTable(6); // Two columns
            table.setWidthPercentage(100);
            // table.setSpacingBefore(5f);
            // table.setSpacingAfter(5f);

            addTableCell(table, "Name & Address:", boldFont);
            addTableCell(table, "Aadhaar Card:", boldFont);
            addTableCell(table, "Mobile No:", boldFont);
            addTableCell(table, "Name of the Hospital:", boldFont);
            addTableCell(table, "Amount Incurred:", boldFont);
            addTableCell(table, "Token Number:", boldFont);
            
            addTableCell(table, formData.getPatientName()+"\n"+formData.getFatherName()+"\n"+formData.getPatAddress(), normalFont);
            addTableCell(table, formData.getAadharNo(), normalFont);
            addTableCell(table, formData.getMobileNo(), normalFont);
            addTableCell(table, formData.getHospName(), normalFont);
            addTableCell(table, formData.getHospBillAmt() + "/-", normalFont);
            addTableCell(table, formData.getMla_cmrf_no(), normalFont);

            document.add(table);

            Paragraph m2 = new Paragraph("     Necessary medical bills in original and copies of Aadhaar card, Ration card / Income Certificate of the applicant are enclosed herewith.", normalFont);
            m2.setAlignment(Element.ALIGN_JUSTIFIED);
            document.add(m2);
            
            document.add(new Paragraph("\n"));
            
            Paragraph m3 = new Paragraph("    As the applicant belongs to a poor family and met the expenditure for treatment by obtaining loans, I would recommend the case for your consideration on humanitarian grounds.", normalFont);
            m3.setAlignment(Element.ALIGN_JUSTIFIED);
            document.add(m3);

            // Add closing
            document.add(new Paragraph("\n"));

            Paragraph regards = new Paragraph("     With Regards,", normalFont);
            // regards.setAlignment(Element.ALIGN_CENTER);
            document.add(regards);
            document.add(new Paragraph("\n"));

            if (!"cons_vkr".equals(userId) && !"mlc_ascons6".equals(userId)) {
                Paragraph yoursSincerely = new Paragraph("Yours sincerely,", normalFont);
                yoursSincerely.setIndentationLeft(document.right() - 150); 
                document.add(yoursSincerely);
            }

            document.add(new Paragraph("\n"));
            document.add(new Paragraph("\n"));
            document.add(new Paragraph("\n"));

            Paragraph prName = null;
            if ("cons_vkr".equals(userId) || "mlc_ascons6".equals(userId)) {
                prName = new Paragraph("(" + formData.getPrName() + ")", boldFont);
            } else {
                prName = new Paragraph("(" + formData.getPrName() + ")", normalFont);
            }
            prName.setAlignment(Element.ALIGN_CENTER);
            prName.setIndentationLeft(document.right() - 200);
            document.add(prName);

            if (!"cons_vkr".equals(userId) && !"mlc_ascons6".equals(userId)) {
                Paragraph cname = new Paragraph(formData.getCname(), normalFont);
                cname.setAlignment(Element.ALIGN_CENTER);
                cname.setIndentationLeft(document.right() - 190);
                document.add(cname);
            }

            if ("cons_vkr".equals(userId) || "mlc_ascons6".equals(userId)) {
                document.add(new Paragraph("\n"));
                document.add(new Paragraph("To,", boldFont));
                document.add(new Paragraph("Sri A.Revanth Reddy Garu,", boldFont));
                document.add(new Paragraph("The Hon'ble Chief Minister,", boldFont));
                document.add(new Paragraph("Government of Telangana,", boldFont));
                document.add(new Paragraph("Hyderabad.", boldFont));
            }

            document.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public void generateLOCPDF(HttpServletRequest request, HttpServletResponse response) {
        try {

            HttpSession session=request.getSession();
		    String userId = (String)session.getAttribute("userid");
            System.out.println("USER ID in Template PDF : "+ userId);

            // Retrieve formData object from the request/session
            LOCMLACMRFEntryModel formData = (LOCMLACMRFEntryModel) request.getAttribute("formData");

            // Set PDF content type
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=\"PR_LOC_Covering_Letter.pdf\"");

            // Create document and writer
            Document document = new Document();
            OutputStream out = response.getOutputStream();
            PdfWriter.getInstance(document, out);

            // Open the document to write
            document.open();

            // Fonts
            Font normalFont = new Font(Font.HELVETICA, 10);
            Font boldFont = new Font(Font.HELVETICA, 11, Font.BOLD);

            // Add blank lines to simulate the spacing
            document.add(new Paragraph("\n"));
            document.add(new Paragraph("\n"));
            document.add(new Paragraph("\n"));
            document.add(new Paragraph("\n"));
            document.add(new Paragraph("\n"));

            // Conditional rendering for user-specific data
            if (!"cons_vkr".equals(userId) && !"mlc_ascons6".equals(userId)) {
                // Add Date
                Paragraph date = new Paragraph("Date: " + formData.getCurrentDate(), normalFont);
                date.setAlignment(Element.ALIGN_RIGHT);
                document.add(date);
                document.add(new Paragraph("\n"));

                // Add the Recipient Information
                document.add(new Paragraph("To,", boldFont));
                document.add(new Paragraph("Sri A.Revanth Reddy Garu,", boldFont));
                document.add(new Paragraph("Hon'ble Chief Minister,", boldFont));
                document.add(new Paragraph("Government of Telangana,", boldFont));
                document.add(new Paragraph("Dr.B.R.AmbedkarTelanganaSecretariat.,", boldFont));
                document.add(new Paragraph("Hyderabad.", boldFont));
                document.add(new Paragraph("\n"));

                // Add Letter Number
                Paragraph letterNo = new Paragraph("D.O.Lr.No. ______ /" + formData.getConstName() + "/" + formData.getCurrentYear() + "/Dt. ______", boldFont);
                letterNo.setAlignment(Element.ALIGN_CENTER);
                document.add(letterNo);
                document.add(new Paragraph("\n"));
            }

            // Add the Salutation
            document.add(new Paragraph("Respected Sir,", boldFont));
            document.add(new Paragraph("\n"));

            // Add the main content
            String mainText = "     I am herewith enclosing the representation of the patient shown below for sanction of financial assistance from LOC. Kindly arrange for sanction at the earliest.";
            Paragraph mainContent = new Paragraph(mainText, normalFont);
            mainContent.setAlignment(Element.ALIGN_JUSTIFIED);
            document.add(mainContent);
            document.add(new Paragraph("\n"));

            // Create a table for patient details
            PdfPTable table = new PdfPTable(2);
            table.setWidthPercentage(100);
            table.setSpacingBefore(10f);
            table.setSpacingAfter(10f);
            
            float[] columnWidths = {2f, 3f}; // 1st column (Name, Father's Name, etc.) gets 2 parts width, 2nd column (Patient details) gets 3 parts width
            table.setWidths(columnWidths);
            
            // Add headers
            addTableCell(table, "Name", boldFont);
            addTableCell(table, formData.getPatient_name(), normalFont);
            addTableCell(table, "Father's Name/Wife", boldFont);
            addTableCell(table, formData.getFather_name(), normalFont);
            addTableCell(table, "Address", boldFont);
            addTableCell(table, formData.getAddress(), normalFont);
            addTableCell(table, "Name of the disease suffering with", boldFont);
            addTableCell(table, formData.getPurpose(), normalFont);
            addTableCell(table, "Surgery / Treatment Required", boldFont);
            addTableCell(table, formData.getTreatmentDeptName(), normalFont);
            addTableCell(table, "Name of the Hospital", boldFont);
            addTableCell(table, formData.getHospName(), normalFont);
            addTableCell(table, "Total Expenditure incurred", boldFont);
            addTableCell(table, "Rs."+formData.getAssured_amt() + "/-", boldFont);
            addTableCell(table, "Remarks", boldFont);
            addTableCell(table, "", normalFont);
            addTableCell(table, "LOC Token Number", boldFont);
            addTableCell(table, formData.getLoc_mla_no(), normalFont);
            

            // Add the table to the document
            document.add(table);

            // Add Closing Remarks
            String closingText = "With regards,";
            Paragraph closing = new Paragraph(closingText, normalFont);
            closing.setAlignment(Element.ALIGN_CENTER);
            document.add(closing);
            document.add(new Paragraph("\n"));

            // Add "Yours sincerely" for non 'cons_vkr' and 'mlc_ascons6' users
            if (!"cons_vkr".equals(userId) && !"mlc_ascons6".equals(userId)) {
                Paragraph yoursSincerely = new Paragraph("Yours sincerely,", normalFont);
                yoursSincerely.setIndentationLeft(document.right() - 150);
                document.add(yoursSincerely);
            }

            // Add MLA Signature
            document.add(new Paragraph("\n"));
            document.add(new Paragraph("\n"));
            document.add(new Paragraph("\n"));
            Paragraph mlaName = new Paragraph("(" + formData.getMlaName() + ")", boldFont);
            mlaName.setAlignment(Element.ALIGN_CENTER);
            mlaName.setIndentationLeft(document.right() - 190);
            document.add(mlaName);

            // Add Constituency name for other users
            if (!"cons_vkr".equals(userId) && !"mlc_ascons6".equals(userId)) {
                Paragraph constituency = new Paragraph(formData.getConstName(), normalFont);
                constituency.setAlignment(Element.ALIGN_CENTER);
                constituency.setIndentationLeft(document.right() - 190);
                document.add(constituency);
            }

            if ("cons_vkr".equals(userId) || "mlc_ascons6".equals(userId)) {
                document.add(new Paragraph("\n"));
                document.add(new Paragraph("To,", boldFont));
                document.add(new Paragraph("Sri A.Revanth Reddy Garu,", boldFont));
                document.add(new Paragraph("The Hon'ble Chief Minister,", boldFont));
                document.add(new Paragraph("Government of Telangana,", boldFont));
                document.add(new Paragraph("Hyderabad.", boldFont));
            }

         // Generate QR Payload Data
            String locMlaNo = formData.getLoc_mla_no() != null ? formData.getLoc_mla_no().trim() : "";
            String qrData = "{\"tokenNumber\" : \"" + locMlaNo + "\"}";

            // Generate QR code image as byte array
            byte[] qrCodeImage = QRCodeUtil.generateQRCode(qrData, 100, 100);

            if (qrCodeImage != null) {
                // Convert byte array to Image
                Image qrImage = Image.getInstance(qrCodeImage);
                qrImage.scaleToFit(80, 80); // Scale properly
                qrImage.setAlignment(Image.ALIGN_CENTER);

                // Create a PDF table with 1 column for QR code
                PdfPTable qrTable = new PdfPTable(1);
                qrTable.setWidthPercentage(30); // Adjust width
                qrTable.setHorizontalAlignment(Element.ALIGN_LEFT); // Align to left of "Yours sincerely"
                
                PdfPCell qrCell = new PdfPCell(qrImage);
                qrCell.setBorder(Rectangle.NO_BORDER);
                qrCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                qrCell.setPadding(5);

                // Add the QR code cell to the table
                qrTable.addCell(qrCell);

                // Add Entered On date below QR code
                String enteredOnDate = formData.getEnteredOn();
                if (enteredOnDate != null && !enteredOnDate.trim().isEmpty()) {
                    Font dateFont = new Font(Font.HELVETICA, 8, Font.NORMAL);
                    Paragraph dateParagraph = new Paragraph(enteredOnDate, dateFont);
                    dateParagraph.setAlignment(Element.ALIGN_CENTER);                                  
                    PdfPCell dateCell = new PdfPCell(dateParagraph);
                    dateCell.setBorder(Rectangle.NO_BORDER);
                    dateCell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    dateCell.setPaddingTop(-10f);
                    qrTable.addCell(dateCell);
                }

                // Add QR code table to document
                document.add(qrTable);
            } else {
                document.add(new Paragraph("QR Code Not Available", normalFont));
            }


            // Close the document
            document.close();


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void addTableCell(PdfPTable table, String text, Font font) {
        PdfPCell cell = new PdfPCell(new Phrase(text, font));
        cell.setBorderWidth(1);
        table.addCell(cell);
    }
    public void generateEstimationLetterPDF(HttpServletRequest request, HttpServletResponse response) {
        try {
            HttpSession sess = request.getSession();
            ServletContext context = sess.getServletContext();       
            String context_path=context.getRealPath("/");
            LocMlaCmrfPrintDTO formData = (LocMlaCmrfPrintDTO) request.getAttribute("formData");

            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=\"Estimation_Letter.pdf\"");

            Document document = new Document();
            OutputStream out = response.getOutputStream();
            PdfWriter.getInstance(document, out);
            document.open();

            Font normalFont = new Font(Font.HELVETICA, 10);
            Font boldFont = new Font(Font.HELVETICA, 11, Font.BOLD);
            Font nims = new Font(Font.HELVETICA, 12, Font.BOLD, new Color(3, 108, 197));
            Font nimsHead = new Font(Font.HELVETICA, 8);
            Font fontAwesome = FontFactory.getFont(context_path+"/fonts/FontAwesome.otf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 8, Font.NORMAL, new Color(3, 108, 197));

            float[] columnWidths = {0.7f, 3f, 2f}; // Column widths
            PdfPTable headTable = new PdfPTable(columnWidths);
            headTable.setWidthPercentage(100); // Set table width to 100%

            // Add left image (left-aligned)
            try {
                Image leftImage = Image.getInstance(context_path + "/images/nimsLogo.png");
                leftImage.scaleAbsolute(100, 100); // Scale image

                PdfPCell leftImageCell = new PdfPCell(leftImage, true);
                leftImageCell.setBorder(Rectangle.NO_BORDER);
                leftImageCell.setHorizontalAlignment(Element.ALIGN_CENTER); // Align left

                headTable.addCell(leftImageCell);
            } catch (IOException e) {
                e.printStackTrace();
            }

            Paragraph nimsHeading1 = new Paragraph("NIZAM'S INSTITUTE OF MEDICAL SCIENCES", nims);
            nimsHeading1.setAlignment(Element.ALIGN_RIGHT);

            Paragraph nimsHeading2 = new Paragraph("(A UNIVERSITY ESTABLISHED UNDER STATE ACT)", nimsHead);
            nimsHeading2.setAlignment(Element.ALIGN_RIGHT);

            PdfPCell centerCell = new PdfPCell();
            centerCell.addElement(nimsHeading1);
            centerCell.addElement(nimsHeading2);
            centerCell.setBorder(Rectangle.NO_BORDER);
            centerCell.setHorizontalAlignment(Element.ALIGN_CENTER); // Center align text
            // centerCell.setVerticalAlignment(Element.ALIGN_MIDDLE); // Vertically center
            headTable.addCell(centerCell);

            Paragraph phone = new Paragraph();
            phone.setIndentationLeft(10f);
            phone.add(new Chunk("\uf098", fontAwesome)); 
            phone.add(new Chunk(" 040-23489000 / 9244  ", nimsHead));
            phone.add(new Chunk("\uf111", fontAwesome)); 
            phone.add(new Chunk(" 040-23396552", nimsHead));
            // document.add(phone);

            Paragraph fax = new Paragraph();
            fax.setIndentationLeft(10f);
            fax.add(new Chunk("\uf02f", fontAwesome)); 
            fax.add(new Chunk(" Fax: 040-23310076", nimsHead));
            // document.add(fax);

            Paragraph location = new Paragraph();
            location.setIndentationLeft(10f);
            location.add(new Chunk("\uf041", fontAwesome)); 
            location.add(new Chunk("  Panjagutta, Hyderabad - 500082, Telangana", nimsHead));
            // document.add(location);

            Paragraph google = new Paragraph();
            google.setIndentationLeft(10f);
            google.add(new Chunk("\uf0ac", fontAwesome)); 
            google.add(new Chunk(" www.nims.edu.in", nimsHead));
            // document.add(google);

            PdfPCell rightCell = new PdfPCell();
            rightCell.addElement(phone);
            rightCell.addElement(fax);
            rightCell.addElement(location);
            rightCell.addElement(google);
            rightCell.setBorder(Rectangle.NO_BORDER);
            rightCell.setHorizontalAlignment(Element.ALIGN_LEFT); // Center align text
            headTable.addCell(rightCell);

            // Add the table to the document
            document.add(headTable);

         // Add some blank lines
            // document.add(new Paragraph("\n\n\n"));

            // Create a paragraph to simulate left and right alignment in one line
         // Create a Paragraph for the date and align it to the right
            Paragraph dateParagraph = new Paragraph("Date: " + formData.getCurrentDate(), normalFont);
            dateParagraph.setAlignment(Element.ALIGN_RIGHT); 
            dateParagraph.setIndentationLeft(20f);
            dateParagraph.setIndentationRight(20f);// Align to the right
            dateParagraph.setSpacingAfter(10f);  // Optional space after the date
            document.add(dateParagraph);

            // Create a Paragraph for "MS office/Esti/ /2024." and align it to the left
          //  Paragraph officeParagraph = new Paragraph("MS office/Esti/ \u00A0\u00A0 /" + Calendar.getInstance().get(Calendar.YEAR) + ".", normalFont);
            Paragraph officeParagraph = new Paragraph("", normalFont);

            officeParagraph.setAlignment(Element.ALIGN_LEFT); 
            officeParagraph.setIndentationLeft(20f);
            officeParagraph.setIndentationRight(20f);// Align to the left
            officeParagraph.setSpacingAfter(10f);  // Optional space after the office text
            document.add(officeParagraph);


            // Title in Center
            Paragraph title = new Paragraph();
            Chunk underlineTitle = new Chunk("ESTIMATION FOR CMRF", boldFont);
            underlineTitle.setUnderline(0.1f, -2f); // Thickness and position of underline
            title.add(underlineTitle);
            title.setAlignment(Element.ALIGN_CENTER);

            document.add(title);
            document.add(new Paragraph("\n"));

            // **Indented Paragraphs (Padding Left & Right)**
            Paragraph introText = new Paragraph(
                String.format(
                    "			This is to inform that Sri/Smt/Kum %s, aged %s years, S/o %s, R/o %s District %s, " +
                    "Mobile Number: %s, Aadhar No: %s,\n NIMS OP CR Number: %s, " +
                    "IP Number %s is diagnosed with %s.",
                    formData.getPatientName(), formData.getAge(), formData.getFatherSonOf(),formData.getAddress(),
                    formData.getDistName(), formData.getMobileNo(), formData.getAadhaarNo(),
                    formData.getOpcrNo(),formData.getPatientIp(), formData.getPurpose()
                ),
                normalFont
            );
            introText.setAlignment(Element.ALIGN_JUSTIFIED);
            introText.setIndentationLeft(20f);
            introText.setIndentationRight(20f);  // **Padding on both sides**
            document.add(introText);
            document.add(new Paragraph("\n"));

            Paragraph treatmentText = new Paragraph(
                String.format(
                    "			The patient requires Treatment/Procedure/Surgery: %s under Department of %s.",
                    formData.getSpecialName(), formData.getDepartmentName()
                ),
                normalFont
            );
            treatmentText.setIndentationLeft(20f);
            treatmentText.setIndentationRight(20f);
            document.add(treatmentText);
            document.add(new Paragraph("\n"));

            Paragraph approxText = new Paragraph("			The approximate expenditure is as follows.", normalFont);
            approxText.setIndentationLeft(20f);
            approxText.setIndentationRight(20f);
            document.add(approxText);
            document.add(new Paragraph("\n"));

            // **Smaller Table (80% Width)**
            PdfPTable table = new PdfPTable(2);
            table.setWidthPercentage(80);  // Reduced Table Width
            table.setHorizontalAlignment(Element.ALIGN_CENTER);
            table.setWidths(new float[]{2f, 2f}); // Column widths

            // Add table header with centered text and reduced border thickness
            addInnerTableCell(table, "Description", boldFont, true, false);
            addInnerTableCell(table, "Amount", boldFont, true, false);


            addInnerTableCell(table, "Bed Charges", normalFont,false, false);
            addInnerTableCell(table, formData.getBedCharges() + "/-", normalFont,false, true);

            addInnerTableCell(table, "Investigation Charges", normalFont,false, false);
            addInnerTableCell(table, formData.getInvestigCharges() + "/-", normalFont,false, true);

            addInnerTableCell(table, "Drugs Dispensation Charges", normalFont,false, false);
            addInnerTableCell(table, formData.getDrugsDispCharges() + "/-", normalFont,false, true);

            addInnerTableCell(table, "Surgical Procedure Charges", normalFont,false, false);
            addInnerTableCell(table, formData.getSurgProcCharges() + "/-", normalFont,false, true);

            addInnerTableCell(table, "Implant Charges", normalFont,false, false);
            addInnerTableCell(table, formData.getImplantCharges() + "/-", normalFont,false, true);

            addInnerTableCell(table, "Miscellaneous Charges", normalFont,false, false);
            addInnerTableCell(table, formData.getMiscCharges() + "/-", normalFont,false, true);

            addInnerTableCell(table, "Total Approximate Amount", boldFont,false, false);
           /* int totalAmount = formData.getBedCharges() + formData.getInvestigCharges()
                    + formData.getDrugsDispCharges() + formData.getSurgProcCharges()
                    + formData.getImplantCharges() + formData.getMiscCharges();*/
            addInnerTableCell(table, formData.getAssuredAmt() + "/-", boldFont,false, true);

            document.add(table);
            document.add(new Paragraph("\n"));

            // **Assured Amount in Words**
            Paragraph amountWords = new Paragraph("Rupees in words: " + formData.getAmountInWords(), boldFont);
            amountWords.setIndentationLeft(20f);
            amountWords.setIndentationRight(20f);
            document.add(amountWords);
            document.add(new Paragraph("\n"));

            float[] columnWidthsForSignature = {2f, 2f, 2f}; // Column widths
            PdfPTable signatureTable = new PdfPTable(columnWidthsForSignature);
            signatureTable.setWidthPercentage(100);

            PdfPCell emptyCellLeft = new PdfPCell(new Phrase(""));
            emptyCellLeft.setBorder(Rectangle.NO_BORDER);
            emptyCellLeft.setHorizontalAlignment(Element.ALIGN_CENTER);

            PdfPCell emptyCellMiddle = new PdfPCell(new Phrase(""));
            emptyCellMiddle.setBorder(Rectangle.NO_BORDER);

            // Load image
            Image signatureImage = Image.getInstance(context_path + "/images/medicalSupdtBG.jpg"); 
            signatureImage.scaleToFit(100, 100); 
            signatureImage.setAlignment(Element.ALIGN_CENTER);

            // Create a cell for the image
            PdfPCell imageCell = new PdfPCell(signatureImage);
            imageCell.setBorder(Rectangle.NO_BORDER);
            imageCell.setHorizontalAlignment(Element.ALIGN_CENTER);

            // Create a cell for the text below the image
            PdfPCell textCell = new PdfPCell(new Phrase("MEDICAL SUPERINTENDENT", boldFont));
            textCell.setBorder(Rectangle.NO_BORDER);
            textCell.setHorizontalAlignment(Element.ALIGN_CENTER);

            // Create a nested table to combine image and text
            PdfPTable nestedTable = new PdfPTable(1);
            nestedTable.addCell(imageCell);
            nestedTable.addCell(textCell);

            // Create a cell to hold the nested table
            PdfPCell signatureCell = new PdfPCell();
            signatureCell.addElement(nestedTable);
            signatureCell.setBorder(Rectangle.NO_BORDER);
            signatureCell.setHorizontalAlignment(Element.ALIGN_CENTER);

            signatureTable.addCell(emptyCellLeft);
            signatureTable.addCell(emptyCellMiddle);
            signatureTable.addCell(signatureCell);

            document.add(Chunk.NEWLINE);
            document.add(Chunk.NEWLINE);

            document.add(signatureTable);
            
            document.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    public void generateENTEstimationLetterPDF(HttpServletRequest request, HttpServletResponse response) {
        try {
            HttpSession sess = request.getSession();
            ServletContext context = sess.getServletContext();       
            String context_path=context.getRealPath("/");
            LocMlaCmrfPrintDTO formData = (LocMlaCmrfPrintDTO) request.getAttribute("formData");

            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=\"ENT_Estimation_Letter.pdf\"");

            Document document = new Document();
            OutputStream out = response.getOutputStream();
            PdfWriter.getInstance(document, out);
            document.open();

            Font normalFont = new Font(Font.HELVETICA, 10);
            Font normalAmtFont = new Font(Font.HELVETICA, 8);
            Font boldFont = new Font(Font.HELVETICA, 11, Font.BOLD);
            Font ent = new Font(Font.HELVETICA, 14, Font.BOLD, new Color(3, 108, 197));
            Font entHead = new Font(Font.HELVETICA, 11, Font.ITALIC, new Color(88, 89, 91));

            float[] columnWidths = {0.7f, 3f, 0.7f}; // Column widths
            PdfPTable headTable = new PdfPTable(columnWidths);
            headTable.setWidthPercentage(100); // Set table width to 100%

            // Add left image (left-aligned)
            try {
                Image leftImage = Image.getInstance(context_path + "/images/entTelanganaLogo.png");
                leftImage.scaleAbsolute(100, 100); // Scale image

                PdfPCell leftImageCell = new PdfPCell(leftImage, true);
                leftImageCell.setBorder(Rectangle.NO_BORDER);
                leftImageCell.setHorizontalAlignment(Element.ALIGN_CENTER); // Align left

                headTable.addCell(leftImageCell);
            } catch (IOException e) {
                e.printStackTrace();
            }

            Paragraph entHeading1 = new Paragraph("GOVT. E.N.T. HOSPITAL, HYDERABAD. T.S.", ent);
            entHeading1.setAlignment(Element.ALIGN_CENTER);

            Paragraph entHeading2 = new Paragraph("Institute for Ear, Nose, Throat and Head & Neck Diseases", entHead);
            entHeading2.setAlignment(Element.ALIGN_CENTER);

            LineSeparator separator = new LineSeparator();
            separator.setOffset(-4f);
            separator.setLineWidth(1f);
            separator.setLineColor(Color.LIGHT_GRAY);
            separator.setPercentage(85f);

            Paragraph address = new Paragraph("koti, Bank Street, Hyderabad - 500001, Telangana State", normalFont);
            address.setAlignment(Element.ALIGN_CENTER);

            Paragraph details = new Paragraph("Ph : 040-******** E-mail : <EMAIL>", normalFont);
            details.setAlignment(Element.ALIGN_CENTER);

            PdfPCell centerCell = new PdfPCell();
            centerCell.addElement(entHeading1);
            centerCell.addElement(entHeading2);
            centerCell.addElement(separator);
            centerCell.addElement(address);
            centerCell.addElement(details);
            centerCell.setBorder(Rectangle.NO_BORDER);
            centerCell.setHorizontalAlignment(Element.ALIGN_CENTER); // Center align text
            // centerCell.setVerticalAlignment(Element.ALIGN_MIDDLE); // Vertically center
            headTable.addCell(centerCell);

            try {
                Image rightImage = Image.getInstance(context_path + "/images/doctorsLogo.png");
                rightImage.scaleAbsolute(100, 80); // Scale image

                PdfPCell rightImageCell = new PdfPCell(rightImage, true);
                rightImageCell.setBorder(Rectangle.NO_BORDER);
                rightImageCell.setHorizontalAlignment(Element.ALIGN_CENTER); // Align left

                headTable.addCell(rightImageCell);
            } catch (IOException e) {
                e.printStackTrace();
            }
            // Add the table to the document
            document.add(headTable);

            LineSeparator separator1 = new LineSeparator();
            separator1.setOffset(-5f);
            separator1.setLineWidth(1f);
            separator1.setLineColor(Color.LIGHT_GRAY);
            document.add(separator1);
            document.add(new Paragraph("\n"));

            float[] columnWidthsDate = {2f, 2f, 2f}; // Column widths
            PdfPTable dateTable = new PdfPTable(columnWidthsDate);
            dateTable.setWidthPercentage(80);
            dateTable.setHorizontalAlignment(Element.ALIGN_CENTER);
            dateTable.setSpacingBefore(10f);

            PdfPCell leftCell = new PdfPCell(new Paragraph("Ref. No.CIC/No.45/S&H/ENT", normalFont));
            leftCell.setBorder(Rectangle.NO_BORDER);
            dateTable.addCell(leftCell);

            PdfPCell centerCelll = new PdfPCell(new Paragraph("", normalFont));
            centerCelll.setBorder(Rectangle.NO_BORDER);
            dateTable.addCell(centerCelll);

            PdfPCell rightCell = new PdfPCell(new Paragraph("Date: " + formData.getCurrentDate(), normalFont));
            rightCell.setBorder(Rectangle.NO_BORDER);
            rightCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            dateTable.addCell(rightCell);

            document.add(dateTable);
            document.add(new Paragraph("\n"));

            // Title in Center
            Paragraph title = new Paragraph();
            Chunk underlineTitle = new Chunk("TO WHOM SO EVER IT MAY CONCERN", boldFont);
            underlineTitle.setUnderline(0.1f, -2f);
            title.add(underlineTitle);
            title.setAlignment(Element.ALIGN_CENTER);

            document.add(title);
            document.add(new Paragraph("\n"));

            // **Indented Paragraphs (Padding Left & Right)**
            String cond = "";
            if ("Male".equals(formData.getGender())) {
                cond += "He";
            } else {
                cond += "She";
            }
            Paragraph introText = new Paragraph(
                "           This is to certify "+ formData.getPatientName() +", aged "+ formData.getAge() +" years "+ formData.getGender() +", "+ formData.getFatherSonOf() +", R/o "+ formData.getAddress() +", District "+ formData.getDistName() +" Telangana State is a case of "+ formData.getPurpose() +". "+ cond +" has undergone "+ formData.getSpecialName() +" at "+ formData.getHospName() +" in "+ LocalDate.now().getYear() +".",
                normalFont
            );
            introText.setAlignment(Element.ALIGN_JUSTIFIED);
            introText.setIndentationLeft(50f);
            introText.setIndentationRight(50f);  // **Padding on both sides**
            document.add(introText);
            document.add(new Paragraph("\n"));
            
            float[] columnWidthsAmt = {4f, 2f}; // Column widths
            PdfPTable amtTable = new PdfPTable(columnWidthsAmt);
            amtTable.setWidthPercentage(70);
            amtTable.setHorizontalAlignment(Element.ALIGN_CENTER);
            amtTable.setSpacingBefore(10f);

            Paragraph title1 = new Paragraph();
            Chunk underlineTitle1 = new Chunk("Estimation for the Processor Unit", boldFont);
            underlineTitle1.setUnderline(0.1f, -2f);
            title1.add(underlineTitle1);
            PdfPCell leftAmtCell = new PdfPCell(title1);
            leftAmtCell.setBorder(Rectangle.NO_BORDER);
            amtTable.addCell(leftAmtCell);

            Paragraph title2 = new Paragraph();
            Chunk underlineTitle2 = new Chunk("-  Rs. " + formData.getAssuredAmt() + "/-", boldFont);
            underlineTitle2.setUnderline(0.1f, -2f);
            title2.add(underlineTitle2);
            PdfPCell rightAmtCell = new PdfPCell(title2);
            rightAmtCell.setBorder(Rectangle.NO_BORDER);
            rightAmtCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            amtTable.addCell(rightAmtCell);

            PdfPCell fullCell = new PdfPCell(new Paragraph("("+ formData.getAmountInWords() +")", normalAmtFont));
            fullCell.setBorder(Rectangle.NO_BORDER);
            fullCell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            fullCell.setColspan(2);
            amtTable.addCell(fullCell);

            document.add(amtTable);
            document.add(new Paragraph("\n"));

            Paragraph midText = new Paragraph(
                "           As the company is not having tie-up with the Government ENT Hospital, we can supply the processor only in Advance payment. This estimation is given on the request of patient.",
                normalFont
            );
            midText.setIndentationLeft(50f);
            midText.setIndentationRight(50f);
            document.add(midText);
            document.add(new Paragraph("\n"));

            Paragraph endText = new Paragraph(
                "           The Cheque or DD for the above amount should be drawn in the name of the Hospital Development Society, Government ENT Hospital, Hyderabad, or online transfer can be done as per below details.",
                normalFont
            );
            endText.setIndentationLeft(50f);
            endText.setIndentationRight(50f);
            document.add(endText);
            document.add(new Paragraph("\n"));

            // Create a wrapper table with 1 column
            PdfPTable wrapperTable = new PdfPTable(1);
            wrapperTable.setWidthPercentage(100);

            PdfPCell wrapperCell = new PdfPCell();
            wrapperCell.setBorder(Rectangle.NO_BORDER);
            wrapperCell.setPaddingLeft(80f); // Left indentation

            // Your actual content table
            PdfPTable table = new PdfPTable(2);
            table.setWidthPercentage(60);  // Smaller table
            table.setWidths(new float[]{2f, 2f});
            table.setHorizontalAlignment(Element.ALIGN_LEFT);

            // Add rows
            addInfoInnerTableCell(table, "HDS A/c. No.", normalFont);
            addInfoInnerTableCell(table, ": 37296786397", normalFont);
            addInfoInnerTableCell(table, "IFSC Code", normalFont);
            addInfoInnerTableCell(table, ": SBIN0020062", normalFont);
            addInfoInnerTableCell(table, "Branch", normalFont);
            addInfoInnerTableCell(table, ": SBI, Sultan Bazar Branch", normalFont);
            addInfoInnerTableCell(table, "MICR", normalFont);
            addInfoInnerTableCell(table, ": 500002358", normalFont);

            // Add the content table to the wrapper cell
            wrapperCell.addElement(table);
            wrapperTable.addCell(wrapperCell);

            // Add to document
            document.add(wrapperTable);
            document.add(new Paragraph("\n"));

            float[] columnWidthsForSignature = {2f, 2f, 2f}; // Column widths
            PdfPTable signatureTable = new PdfPTable(columnWidthsForSignature);
            signatureTable.setWidthPercentage(80);

            PdfPCell emptyCellLeft = new PdfPCell(new Phrase(""));
            emptyCellLeft.setBorder(Rectangle.NO_BORDER);
            emptyCellLeft.setHorizontalAlignment(Element.ALIGN_CENTER);

            PdfPCell emptyCellMiddle = new PdfPCell(new Phrase(""));
            emptyCellMiddle.setBorder(Rectangle.NO_BORDER);

            // Load image
            Image signatureImage = Image.getInstance(context_path + "/images/medicalSupdtBG.jpg"); 
            signatureImage.scaleToFit(100, 100); 
            signatureImage.setAlignment(Element.ALIGN_CENTER);

            // Create a cell for the image
            PdfPCell imageCell = new PdfPCell(signatureImage);
            imageCell.setBorder(Rectangle.NO_BORDER);
            imageCell.setHorizontalAlignment(Element.ALIGN_CENTER);

            // Create a cell for the text below the image
            PdfPCell textCell = new PdfPCell(new Phrase("SUPERINTENDENT", boldFont));
            textCell.setBorder(Rectangle.NO_BORDER);
            textCell.setHorizontalAlignment(Element.ALIGN_CENTER);

            // Create a nested table to combine image and text
            PdfPTable nestedTable = new PdfPTable(1);
            nestedTable.addCell(imageCell);
            nestedTable.addCell(textCell);

            // Create a cell to hold the nested table
            PdfPCell signatureCell = new PdfPCell();
            signatureCell.addElement(nestedTable);
            signatureCell.setBorder(Rectangle.NO_BORDER);
            signatureCell.setHorizontalAlignment(Element.ALIGN_CENTER);

            signatureTable.addCell(emptyCellLeft);
            signatureTable.addCell(emptyCellMiddle);
            signatureTable.addCell(signatureCell);

            document.add(Chunk.NEWLINE);
            document.add(Chunk.NEWLINE);

            document.add(signatureTable);
            
            document.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
   
    private void addInnerTableCell(PdfPTable table, String text, Font font, boolean isHeader, boolean isAmount) {
        PdfPCell cell = new PdfPCell(new Phrase(text, font));
        
        if (isHeader) {
            cell.setHorizontalAlignment(Element.ALIGN_CENTER); // Center align header text
            cell.setBackgroundColor(Color.LIGHT_GRAY); // Light Gray Background for Header
        } else if (isAmount) {
            cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        } else {
            cell.setHorizontalAlignment(Element.ALIGN_LEFT); // Left align normal cells
        }

        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setBorderWidth(0.5f); // Reduced border thickness
        
        table.addCell(cell);
    }
   
    private void addInfoInnerTableCell(PdfPTable table, String text, Font font) {
        PdfPCell cell = new PdfPCell(new Phrase(text, font));
        
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);

        cell.setBorderWidth(Rectangle.NO_BORDER);
        
        table.addCell(cell);
    }

}

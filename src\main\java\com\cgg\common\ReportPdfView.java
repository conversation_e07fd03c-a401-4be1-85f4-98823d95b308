// package com.cgg.common;

// import java.io.File;
// import java.io.FileOutputStream;
// import java.util.ArrayList;
// import java.util.List;
// import java.util.Map;
// import javax.servlet.ServletContext;
// import javax.servlet.http.HttpServletRequest;
// import javax.servlet.http.HttpServletResponse;
// import javax.servlet.http.HttpSession;

// import org.springframework.stereotype.Component;
// import org.springframework.web.servlet.view.AbstractView;

// import com.cgg.common.CommonUtils;
// import com.cgg.proceedings.model.LetterToHospitalsForm;
// import com.itextpdf.io.font.FontConstants;
// import com.itextpdf.kernel.font.PdfFontFactory;
// import com.itextpdf.kernel.pdf.PdfDocument;
// import com.itextpdf.kernel.pdf.PdfWriter;
// import com.itextpdf.layout.Document;
// import com.itextpdf.layout.element.Paragraph;
// import com.itextpdf.layout.element.Table;
// import com.itextpdf.layout.property.UnitValue;


// @Component("reportView")
// public class ReportPdfView extends AbstractView {
//   @Override
//   protected void renderMergedOutputModel(Map<String, Object> model,
//                                          HttpServletRequest request,
//                                          HttpServletResponse response) throws Exception {
// 	HttpSession sess = request.getSession();
//     ServletContext context = sess.getServletContext();       
//     String context_path=context.getRealPath("/");
//     LetterToHospitalsForm letterToHospForm=(LetterToHospitalsForm) model.get("letterToHospForm");
// 	java.util.Date d=new java.util.Date();
// 	String filename="hosp"+"PDF"+d.getTime()+".pdf";
// 	String file = context_path+"/pdffiles/"+filename;
// 	String temp="";

// 	File fileobj=new File(file);
//     // response.setHeader("Content-Disposition", new FileOutputStream(fileobj));
//       System.out.println("report---");
//       //IText API
//     PdfWriter pdfWriter = new PdfWriter(response.getOutputStream());
//     PdfDocument pdf = new PdfDocument(pdfWriter);
//     Document pdfDocument = new Document(pdf);
//     HttpSession session=request.getSession();
     
// 	  Paragraph title = new Paragraph("                    GOVERNMENT OF TELANGANA\n ");
// 	  title.add("                                        REVENUE (CMRF) DEPARTMENT \n");
// 	  title.add("                                        Letter No:"+
// 	letterToHospForm.getLetterNo()+"   Dated"+letterToHospForm.getLetterDate()+"");
// 	  title.setFont(PdfFontFactory.createFont(FontConstants.HELVETICA));
// 	  title.setFontSize(10f); title.setItalic(); 
// 	  pdfDocument.add(title);
	
//       //content
		
// 	  Paragraph content = new Paragraph("From                                                                           To\n");
// 	  content.add("The Principal Secretary to Government,                    The Director,\n" );
// 	  content.add(" Revenue (CMRF) Department,                                   "+ letterToHospForm.getHospName()+"\n"); 
// 	  content.add(" Secretariat,Hyderabad.\n");
// 	  content.setFontSize(8f);
// 	  pdfDocument.add(content);
		 
// 	  Paragraph content2 = new Paragraph("Sir, \n");
// 	  content2.add("    Sub:- CMRF-Sanction of Financial assistance under CMRF for medical treatment Reg.\n");
// 	  content2.add("    Ref: 1. Sanction No."+letterToHospForm.getLetterNo()+" Dated:"+letterToHospForm.getLetterDate()+"\n");
// 	  content2.add("    2.Letter and Bills from  "+ letterToHospForm.getHospName()+"\n");
// 	  content2.add("I am directed to state that the following person(s) as detailed below have requested for rendering financial assistance from " + 
// 	  		"Chief Minister's Relief Fund, so as to enable them to meet the medical expenses in connection with their treatment at your\r\n" + 
// 	  		"Hospital");
// 	  content2.setFontSize(8f);
// 	  pdfDocument.add(content2);
		  
// 	  Paragraph content3 = new Paragraph("I am to inform that the Government hereby sanction and release the amount as shown below against their names and the Bills " + 
// 	  		"submitted by you in the reference 2nd cited. \n");
// 	  content3.setFontSize(8f);
// 	  pdfDocument.add(content3);
		  
// 		  // table with 2 columns:
// 	   Table table = new Table(UnitValue.createPercentArray(5)).useAllAvailableWidth();
	 
// 	        // header row:
//         table.addHeaderCell("SNO");
//         table.addHeaderCell("Application S.No \n  (LOC no.)");
//         table.addHeaderCell("Patient Name");
//         table.addHeaderCell("Patient Address");
//         table.addHeaderCell("Amount Sanctioned");
	    
// 		  List<LetterToHospitalsForm> letterToHosp = new ArrayList<LetterToHospitalsForm>();
// 		  letterToHosp=(List<LetterToHospitalsForm>) model.get("letterToHosp");		  
// 		 // System.out.println("ii=letterToHosp=="+letterToHosp.size());

// 		  int i=1;
// 		  Long totAmt=(long) 0;
// 		  for(LetterToHospitalsForm dirExg : letterToHosp){  
			  
//             table.addCell(""+(i));
// 	        table.addCell(""+dirExg.getCmrfNo()+"");
// 	        table.addCell(""+dirExg.getPatName()+"");
// 	        table.addCell(""+dirExg.getPatAddr()+"");
// 	        table.addCell(""+dirExg.getSancAmt()+"");
// 	        totAmt=totAmt+(Long.parseLong(dirExg.getSancAmt()));
// 	        i=i+1;
// 					        }
// 		//  System.out.println("ii=ffff=="+i);
// 	        pdfDocument.add(table);
	       
          
// 		    Paragraph content4 = new Paragraph("A Cheque bearing No. "+letterToHospForm.getChequeNo()+", dt."+letterToHospForm.getLetterDate()+"for Rs."+CommonUtils.convert(totAmt.toString())+" ( "+CommonUtils.inWords(totAmt.toString())+") is enclosed " + 
// 		  		"accordingly.");
// 		  content4.setFontSize(8f);
// 		  pdfDocument.add(content4);

		 
// 		  Paragraph  content6= new Paragraph("I request you to acknowledge the same and send us a stamped receipt for record.");
// 		  content6.setFontSize(8f);
// 		  pdfDocument.add(content6);
		  
// 		  Paragraph  content7= new Paragraph( "Copy to:                                                               Yours faithfully,\nThe PS to Spl.Secy to C.M \n");
// 				  content7.add(" The Individual concerned.                                      for PRINCIPAL SECRETARY TO THE GOVERNMENT\nS.C/S.F.");
// 				  content7.setFontSize(8f);
// 				  pdfDocument.add(content7);

// 			      //Closing the document  
//       pdfDocument.close();
//       response.setHeader("Content-Disposition","attachment;filename="+file);

      
//   }
// }

package com.cgg.dataentry.dao;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.cgg.dataentry.model.UpdateChequeForm;

public interface UpdateChequeDao {
	public Map<String, Object> getTodaysCheckHistory(HttpServletRequest request);
	
	Map<String, Object> getDetails(UpdateChequeForm request) throws SQLException;

	boolean updateChequeDetails(UpdateChequeForm updateChequeForm,HttpServletRequest request) throws SQLException;

	boolean editChequeDetails(UpdateChequeForm updateChequeForm,HttpServletRequest request) throws SQLException;
	
	Map<String, Object> getChequeDetailsFromTo(UpdateChequeForm request) throws SQLException;
	
	Map<String, Object> getUpdChqDtlsForPrint(UpdateChequeForm updChqForm,boolean cmrfFlag, boolean dateFlag) throws SQLException;

	List<UpdateChequeForm> generateBeneficiaryLetters(String cmrfNo, String newChequeNo) throws Exception;

	Map<String, Object> updateProceedings(UpdateChequeForm updateChequeForm) throws Exception;
	
	boolean checkChequeNoIsExists(String newChequeNo) throws Exception;
}

package com.cgg.common;

import java.util.*;
import java.io.*;
import javax.servlet.ServletContext;
import javax.servlet.http.*;

public class GeneralReports {

    public String CreateXLS(String prefix, ArrayList data, String[] headings, String[][] columnnames, String[][] rowspan, String[][] colspan, String[] totals, String[] aligns, boolean wantSno, ArrayList extraFooter, HttpServletRequest req) {
        HttpSession sess = req.getSession();
        ServletContext context = sess.getServletContext();
        String context_path = context.getRealPath("/");;

        java.util.Date d = new java.util.Date();
        String filename = prefix + "XL" + d.getTime() + ".xls";
        String file = context_path + "/xlfiles/" + filename;

        String tableStr = getTableFormat(data, headings, columnnames, rowspan, colspan, totals, aligns, wantSno, extraFooter);
        GeneralOperations genop = new GeneralOperations();
        if (prefix.equals("RevenueDraftCopy") || prefix.equals("ExGratiaRevenueDraftCopy")) {
            tableStr = tableStr + "<tr><td colspan=8 rowspan=3 align=right>CHIEF MINISTER&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</td></tr>";

        }
        String finalStr = "";
        tableStr = tableStr.replaceAll("</a>", "");
        tableStr = tableStr.replaceAll("<br>", "\n");
        //finalStr = removeHtmlTags(tableStr);
        StringBuffer sb = new StringBuffer(tableStr);
        //data,headings,snos,totals,colheadings,spans	

        try {
            FileOutputStream fos = new FileOutputStream(file);
            char c;

            for (int i = 0; i < sb.length(); i++) {
                c = sb.charAt(i);
                fos.write(c);
            }
            fos.close();
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }

        return "../tgcmrelief/xlfiles/" + filename;

    }

    public static String removeHtmlTags(String ins) {
        StringBuffer finalStr = new StringBuffer();

        boolean intag = false;
        for (int i = 0; i < ins.length(); i++) {
            if (ins.charAt(i) == '<' && ins.charAt(i + 1) == 'a') {
                intag = true;
            } else if (ins.charAt(i) != '\t' && ins.charAt(i) != '\n' && !(intag)) {
                finalStr.append(ins.charAt(i));
            } else if (ins.charAt(i) == '>') {
                intag = false;
            }
        }
        return finalStr.toString();
    }

    public String getTableFormat(ArrayList data, String[] headings, String[][] columnnames, String[][] rowspan, String[][] colspan, String[] totals, String[] aligns, boolean wantSno, ArrayList extraFooter) {
        StringBuffer sb = new StringBuffer();
        ArrayList rowData = new ArrayList();
        boolean totFlag = false;
        String align = "";
        String tot[] = new String[totals.length];
        for (int i = 0; i < totals.length; i++) {
            if (!totals[i].equals("0")) {
                totFlag = true;
                break;
            }
        }
        sb.append("<table align=center border=1  class=reports>");

        for (int i = 0; i < headings.length; i++) {
            sb.append("<tr><th colspan=" + (columnnames[0].length + 1) + ">" + headings[i]);
        }

//		sb.append("<tr><th colspan="+(columnnames[0].length+1)+">"+headings[0]);
        //              sb.append("<tr><th colspan="+(columnnames[0].length+1)+">"+headings[1]);
        //			sb.append("<tr><th colspan="+(columnnames[0].length+1)+">"+headings[2]);

        for (int i = 0; i < columnnames.length; i++) {
            sb.append("<tr>");
            if (wantSno && i == 0) {
                sb.append("<th rowspan=" + rowspan.length + ">Sno");
            }
            for (int j = 0; j < columnnames[i].length; j++) {


                sb.append("<th colspan=" + colspan[i][j] + " rowspan=" + rowspan[i][j] + " >" + columnnames[i][j]);
            }
        }
        for (int i = 0; i < data.size(); i++) {

            rowData = new ArrayList();
            rowData = (ArrayList) data.get(i);
            sb.append("<tr>");
            if (wantSno) {
                sb.append("<td>" + (i + 1));
            }
            for (int j = 0; j < rowData.size(); j++) {
                if (aligns[j].equals("0")) {
                    align = "center";
                } else if (aligns[j].equals("1")) {
                    align = "left";
                } else {
                    align = "right";
                }
                sb.append("<td align=" + align + ">" + (String) rowData.get(j));
            }

        }
        if (totFlag) {
            sb.append("<tr>");
            for (int k = 0; k < tot.length; k++) {
                tot[k] = "0";
            }

            for (int i = 0; i < data.size(); i++) {
                rowData = new ArrayList();
                rowData = (ArrayList) data.get(i);


                for (int j = 0; j < rowData.size(); j++) {

                    if (!totals[j].equals("0")) {
                        System.out.println("rowData.get(j"+rowData.get(j));
                        System.out.println("rowData.get(j"+totals[j]);
                       tot[j] = "" + (Integer.parseInt((String) rowData.get(j)) + Integer.parseInt(tot[j])) + "";

                    } else {
                        tot[j] = "";
                    }
                }

            }
            if (wantSno) {
                sb.append("<th>Total");
            }
            for (int i = 0; i < tot.length; i++) {
                sb.append("<th align=right>" + tot[i]);
            }
        }

        if (null != extraFooter) {
            String prefix = "";
            for (int i = 0; i < extraFooter.size(); i++) {
                ArrayList tempFooter = new ArrayList();
                tempFooter = (ArrayList) extraFooter.get(i);
                for (int j = 0; j < tempFooter.size(); j++) {
                    if (j == 0) {
                        prefix = "Total No of Cases : ";
                    } else {
                        prefix = prefix + "      Total Amount in Rs : ";
                    }
                    prefix = prefix + (String) tempFooter.get(j) + " ";
                }
                sb.append("<tr><th colspan=8>" + prefix);
            }
        }


        return sb.toString();
    }
}

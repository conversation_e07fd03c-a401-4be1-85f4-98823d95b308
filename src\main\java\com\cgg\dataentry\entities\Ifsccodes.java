package com.cgg.dataentry.entities;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name="ifsccodes")
public class Ifsccodes {
	
	 @Id
	    @Column(name = "ifsccode")
	    private String ifscCode;  // Assuming IFSC code is the primary key or unique identifier

	    @Column(name = "bankname")
	    private String bankName;

	    @Column(name = "micrcode")
	    private String micrCode;

	    @Column(name = "branch")
	    private String branch;

	    @Column(name = "address")
	    private String address;

	    @Column(name = "contact")
	    private String contact;

	    @Column(name = "center")
	    private String center;

	    @Column(name = "district")
	    private String district;

	    @Column(name = "state")
	    private String state;

		public String getIfscCode() {
			return ifscCode;
		}

		public void setIfscCode(String ifscCode) {
			this.ifscCode = ifscCode;
		}

		public String getBankName() {
			return bankName;
		}

		public void setBankName(String bankName) {
			this.bankName = bankName;
		}

		public String getMicrCode() {
			return micrCode;
		}

		public void setMicrCode(String micrCode) {
			this.micrCode = micrCode;
		}

		public String getBranch() {
			return branch;
		}

		public void setBranch(String branch) {
			this.branch = branch;
		}

		public String getAddress() {
			return address;
		}

		public void setAddress(String address) {
			this.address = address;
		}

		public String getContact() {
			return contact;
		}

		public void setContact(String contact) {
			this.contact = contact;
		}

		public String getCenter() {
			return center;
		}

		public void setCenter(String center) {
			this.center = center;
		}

		public String getDistrict() {
			return district;
		}

		public void setDistrict(String district) {
			this.district = district;
		}

		public String getState() {
			return state;
		}

		public void setState(String state) {
			this.state = state;
		}
	    
	    

}

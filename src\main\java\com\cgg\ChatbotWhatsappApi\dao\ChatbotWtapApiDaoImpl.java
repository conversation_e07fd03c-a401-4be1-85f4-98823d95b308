package com.cgg.ChatbotWhatsappApi.dao;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.cgg.ChatbotWhatsappApi.model.ApiDetails;
import com.cgg.common.CommonFunctions;

@Repository("ChatbotWtapApiDaoImpl")
@Transactional
public class ChatbotWtapApiDaoImpl {

    @Autowired
	JdbcTemplate jdbcTemplate;

    public List<ApiDetails> getLOCApplications(String phoneNumber) throws Exception {
        List<ApiDetails> LOCdata=new ArrayList<ApiDetails>();
        String sql = "select loc_no ,patient_name, father_name, address, hospital_name, aadhaar_no, mobile_no,case when cno = '999' then mlamp else mlamp || ' (' || cname || ')' end as recommended_by "
                    + "from loc_cmrf lc "
                    + "left join constituency c on c.cno::int4 = lc.recommended_by::int4 "
                    + "where mobile_no = '" + phoneNumber + "' order by \"time_stamp\" desc limit 10;";
        
        try {
            System.out.println("Get all LOC Applications with phone number: "+ sql);

            LOCdata=jdbcTemplate.query(sql,new RowMapper<ApiDetails>(){ 
                    @Override 
                    public ApiDetails mapRow(ResultSet rs, int rowNum) throws SQLException { 
                        ApiDetails rowSqlData=new ApiDetails(); 
                        
                        //Row information
                        rowSqlData.setPhoneNo(phoneNumber.trim());
                        rowSqlData.setLocNo(rs.getString("loc_no"));
                        rowSqlData.setApplicantName(rs.getString("patient_name"));
                        
                        return rowSqlData; 
                    }
    
                });
            return (LOCdata != null && !LOCdata.isEmpty()) ? LOCdata : Collections.emptyList();
		}catch (Exception e) {
            System.err.println("Error fetching LOC Applications for phone number: " + phoneNumber);
            e.printStackTrace();
            throw new Exception("Error retrieving applications: " + e.getMessage(), e);
        }
	}

    public List<ApiDetails> getCMRFApplications(String phoneNumber) throws Exception {
        List<ApiDetails> CMRFdata=new ArrayList<ApiDetails>();
        String sql = "select mla_cmrf_no ,patient_name ,father_son_of ,pat_address ,aadhar_no ,mc.mobile_no , h.hospname , "
                    + " case when cn.cno in ('998') then 'PEOPLE REPRESENTATIVE' when cn.cno in ('999','997','194') then mlamp else coalesce(cn.mlamp || ' ' || cn.cname, 'NA') end  as recommendedby, "
                    + " case when mc.status in ('3','11') then sm.status_name ||' - '||coalesce(mc.cmrf_no,'NA-') else sm.status_name end as status "
                    + " from mla_cmrf mc "
                    + " left join hospital h on h.hospcode = mc.hosp_code "
                    + " left join constituency cn on cn.cno::int4 = mc.recommended_by::int4 "
                    + " left join status_mst sm on sm.status_id = mc.status::int4 "
                    + " where mc.mobile_no = '" + phoneNumber + "' order by mc.entered_on desc limit 10;";
        
        try {
            System.out.println("Get all CMRF Applications with phone number: "+ sql);

            CMRFdata=jdbcTemplate.query(sql,new RowMapper<ApiDetails>(){ 
                    @Override 
                    public ApiDetails mapRow(ResultSet rs, int rowNum) throws SQLException { 
                        ApiDetails rowSqlData=new ApiDetails(); 
                        
                        //Row information
                        rowSqlData.setPhoneNo(phoneNumber.trim());
                        rowSqlData.setTokenNo(rs.getString("mla_cmrf_no"));
                        rowSqlData.setApplicantName(rs.getString("patient_name"));
                        rowSqlData.setStatus(rs.getString("status"));
                        
                        return rowSqlData; 
                    }
    
                });
            return (CMRFdata != null && !CMRFdata.isEmpty()) ? CMRFdata : Collections.emptyList();
		}catch (Exception e) {
            System.err.println("Error fetching CMRF Applications for phone number: " + phoneNumber);
            e.printStackTrace();
            throw new Exception("Error retrieving applications: " + e.getMessage(), e);
        }
	}

    public List<ApiDetails> getApplicationByTokenNo(String tokenNumber) throws Exception {
        List<ApiDetails> tokenData=new ArrayList<ApiDetails>();
        String sql = "select mla_cmrf_no ,patient_name ,father_son_of ,pat_address ,aadhar_no ,mc.mobile_no , h.hospname , "
                    + " case when cn.cno in ('998') then 'PEOPLE REPRESENTATIVE' when cn.cno in ('999','997','194') then mlamp else coalesce(cn.mlamp || ' ' || cn.cname, 'NA') end  as recommendedby, "
                    + " case when mc.status in ('3','11') then sm.status_name ||' - '||coalesce(mc.cmrf_no,'NA-') else sm.status_name end as status "
                    + " from mla_cmrf mc "
                    + " left join hospital h on h.hospcode = mc.hosp_code "
                    + " left join constituency cn on cn.cno::int4 = mc.recommended_by::int4 "
                    + " left join status_mst sm on sm.status_id = mc.status::int4 "
                    + " where mc.mla_cmrf_no = '" + tokenNumber + "';";
        
        try {
            System.out.println("Get Application with token number: "+ sql);

            tokenData=jdbcTemplate.query(sql,new RowMapper<ApiDetails>(){ 
                    @Override 
                    public ApiDetails mapRow(ResultSet rs, int rowNum) throws SQLException { 
                        ApiDetails rowSqlData=new ApiDetails(); 
                        
                        //Row information
                        rowSqlData.setTokenNo(tokenNumber.trim());
                        rowSqlData.setPhoneNo(rs.getString("mobile_no"));
                        rowSqlData.setApplicantName(rs.getString("patient_name"));
                        rowSqlData.setStatus(rs.getString("status"));
                        
                        return rowSqlData; 
                    }
    
                });
            return (tokenData != null && !tokenData.isEmpty()) ? tokenData : Collections.emptyList();
		}catch (Exception e) {
            System.err.println("Error fetching Application for token number: " + tokenNumber);
            e.printStackTrace();
            throw new Exception("Error retrieving applications: " + e.getMessage(), e);
        }
	}

    public Boolean insertLogs(String phoneNumber, String ipAddr, String request, String response, String tokenNo) {
        // Map to hold column names and their corresponding values
        Map<String, Object> insertMap = new HashMap<>();
        String logSql = null;

        try {
            // Validate and add each parameter to the insert map
            if (CommonFunctions.validateData(phoneNumber)) {
                insertMap.put("phone_number", "'" + phoneNumber + "'");
            }

            insertMap.put("log_date", "current_timestamp");

            if (CommonFunctions.validateData(ipAddr)) {
                insertMap.put("ip_addr", "'" + ipAddr + "'");
            }

            if (CommonFunctions.validateData(request)) {
                insertMap.put("request", "'" + request + "'");
            }

            if (CommonFunctions.validateData(response)) {
                insertMap.put("response", "'" + response + "'");
            }

            if (CommonFunctions.validateData(tokenNo)) {
                insertMap.put("token_number", "'" + tokenNo + "'");
            }

            // Generate the query using a helper method from CommonFunctions
            Map<String, Object> queryMap = CommonFunctions.insertQuery(insertMap);

            // Ensure the query map is not null or empty before proceeding
            if (queryMap != null && !queryMap.isEmpty()) {
                logSql = "INSERT INTO cmrf_chatbot_whatsapp_logs (" + queryMap.get("colNames") + ") VALUES (" + queryMap.get("colValues") + ")";
            } else {
                System.err.println("No valid data to insert.");
                return false;
            }

            // Log the generated SQL
            System.out.println("LOG SQL: " + logSql);

            // Execute the SQL and return the result
            int count = jdbcTemplate.update(logSql);
            return count > 0;

        } catch (Exception e) {
            System.err.println("Error while inserting logs:");
            e.printStackTrace();
            return false;
        }
        // String log_sql="insert into cmrf_chatbot_whatsapp_logs (phone_number,log_date,ip_addr,request,response,token_number) "
				// + "	values('"+phone_number+"',current_timestamp,'"+ip_addr+"','"+request+"','"+response+"','"+tokenNo+"');";
    }

}

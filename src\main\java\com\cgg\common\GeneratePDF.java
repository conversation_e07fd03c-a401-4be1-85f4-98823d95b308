/*
 * Created on Dec 27, 2004
 *
 * TODO To change the template for this generated file go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
package com.cgg.common;

import java.awt.Color;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.function.Consumer;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.cgg.reports.model.VIPWiseChequeIssuesCases;
import com.cgg.reports.service.VIPWiseChequeIssuesCasesService;
import com.lowagie.text.Cell;
import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Element;
import com.lowagie.text.ExceptionConverter;
import com.lowagie.text.Font;
import com.lowagie.text.FontFactory;
import com.lowagie.text.Image;
import com.lowagie.text.PageSize;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Phrase;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfGState;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPageEventHelper;
import com.lowagie.text.pdf.PdfTemplate;
import com.lowagie.text.pdf.PdfWriter;

/**
 * <AUTHOR>
 *
 * TODO To change the template for this generated type comment go to
 * Window - Preferences - Java - Code Style - Code Templates
 */
public class GeneratePDF extends PdfPageEventHelper {

    public PdfPTable table;
    public PdfPTable aTable;
    public PdfGState gstate;
    public PdfTemplate tpl;
    public BaseFont helv;
    public String mprefix = "";
    public String signed = "";
    public String overlap = "";
    Document document;

    public String CreatePDF(String prefix, ArrayList data, String[] headings, String[][] columnnames, String[][] rowspans, String[][] colspans, String[] aligns, String[] totals, boolean wantSno, ArrayList extraFooter, HttpServletRequest req, int[] widths) {

        HttpSession sess = req.getSession();
        ServletContext context = sess.getServletContext();       
        String context_path=context.getRealPath("/");;
        System.out.println("context path is -------> "+context_path);
        java.util.Date d = new java.util.Date();
        String filename = prefix + "PDF" + d.getTime() + ".pdf";
        String file = context_path + "/pdffiles/" ;
        File folder = new File(file);    
        if(!folder.exists()){
            folder.mkdirs();
        } 
         file = context_path + "/pdffiles/" + filename;

       
        //String file = context_name+"/pdffiles/"+filename;
        mprefix = prefix;
        File fileobj = new File(file);

        signed = (String) req.getParameter("signed");
        if (signed == null) {
            signed = "N";
        }
        overlap = (String) req.getParameter("overlap");
        if (overlap == null) {
            overlap = "N";
        }

        try {
                    System.out.println("prefix-------> "+prefix);


            if (prefix.equals("CMRFEntryReport") || prefix.equals("DistWiseAbstractAllCases") || prefix.equals("SanctionStatement") || prefix.equals("SanctionStatement")|| prefix.equals("PREPSanctionStatement")) {
                document = new Document(PageSize.A4.rotate());
                mprefix = prefix;
            } else {
                document = new Document(PageSize.A4);
            }

            PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(fileobj));
            //PdfPageEvent pageEvent = new GeneratePDF();
            //writer.setPageEvent(pageEvent);
            document.setMargins(50, 10, 10, 20);
            document.open();
            Paragraph tDate = new Paragraph("Date :  " + new java.util.Date() + "", FontFactory.getFont(FontFactory.TIMES_ROMAN, 8, com.lowagie.text.Font.BOLD, new Color(0, 0, 0)));
            tDate.setAlignment(Element.ALIGN_RIGHT);

            Paragraph tSpace = new Paragraph("  ", FontFactory.getFont(FontFactory.TIMES_ROMAN, 8, com.lowagie.text.Font.BOLD, new Color(0, 0, 0)));
            tSpace.setAlignment(Element.ALIGN_RIGHT);

            //ol
            if (overlap.equals("Y")) {
                document.add(tSpace);
            }
            //document.add(tSpace);

            //document.add(tSpace);

            if (!prefix.equals("RecomendedConWise")) {
                document.add(tDate);
            }
            if (overlap.equals("Y")) {
                document.add(tSpace);
            }
            //document.add(tSpace);
//				document.add(tSpace);
//document.add(tSpace);

            for (int i = 0; i < headings.length; i++) {
                writeHeading(headings[i]);
            }
            //writeHeading(headings[0]);
            //writeHeading(headings[1]);
            //writeHeading(headings[2]);

            //document.add(tSpace);
            //document.add(tSpace);
            //document.add(tSpace);

            createTable(data, columnnames, rowspans, colspans, aligns, totals, wantSno, extraFooter, widths);

            if (prefix.equals("RevenueDraftCopy") || prefix.equals("ExGratiaRevenueDraftCopy")) {
                //document.add(tSpace);
                //document.add(tSpace);
                //document.add(tSpace);
                Paragraph cm = new Paragraph("CHIEF MINISTER                  ", FontFactory.getFont(FontFactory.TIMES_ROMAN, 12, com.lowagie.text.Font.BOLD, new Color(0, 0, 0)));
                cm.setAlignment(Element.ALIGN_RIGHT);
                Paragraph prl = new Paragraph("         Prl Secy Revenue", FontFactory.getFont(FontFactory.TIMES_ROMAN, 10, com.lowagie.text.Font.BOLD, new Color(0, 0, 0)));
                prl.setAlignment(Element.ALIGN_LEFT);
                document.add(cm);
                document.add(tSpace);
                document.add(prl);

            }

            // ArrayList recordData = (ArrayList) data.get(0);
            // String prName = (String) recordData.get(0);
            // int totalCheques = data.size();
            // String cmpNo = req.getParameter("cmp_no");
            // String cno = req.getParameter("cno");
            // String otherConst = req.getParameter("otherCont");
            // String esignedDate = req.getParameter("date");

            // acknowledgement(prName, totalCheques, cmpNo, cno, otherConst, esignedDate);

            document.close();
            writer.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return "../tgcmrf/pdffiles/" + filename;
    }

//	For Removing HTML tags
    public static String removeHtmlTags(String ins) {
        StringBuffer finalStr = new StringBuffer();

        boolean intag = false;
        for (int i = 0; i < ins.length(); i++) {
            if (ins.charAt(i) == '<') {
                intag = true;
            } else if (ins.charAt(i) != '>' && !(intag)) {
                finalStr.append(ins.charAt(i));
            } else if (ins.charAt(i) == '>') {
                intag = false;
            }
        }
        return finalStr.toString();
    }

//	For Writing Report Headings
    public void writeHeading(String heading) {
        /*	
        HeaderFooter header = new HeaderFooter(new Phrase("" + heading.replaceAll("<br>", "\n"), FontFactory.getFont(FontFactory.TIMES_ROMAN, 14, com.lowagie.text.Font.BOLD, new Color(255, 255, 255))), false);
        header.setAlignment(Element.ALIGN_CENTER);
        header.setBorderColor(new Color(0, 0, 0));
        header.setBackgroundColor(new Color(125,125,125));	// FOR THE MAIN HEADING
        header.setBackgroundColor(new Color(99,00,00));	
        
        document.setHeader(header);
         */
        Paragraph tHeading = new Paragraph(" " + heading.replaceAll("<br>", "\n"), FontFactory.getFont(FontFactory.TIMES_ROMAN, 15, com.lowagie.text.Font.BOLD, new Color(0, 0, 0)));
        tHeading.setAlignment(Element.ALIGN_CENTER);

        Paragraph tSpace = new Paragraph("  ", FontFactory.getFont(FontFactory.TIMES_ROMAN, 8, com.lowagie.text.Font.BOLD, new Color(0, 0, 0)));

        tSpace.setAlignment(Element.ALIGN_CENTER);

        try {
            document.add(tHeading);
            //document.add(tSpace);

        } catch (Exception e) {
            e.printStackTrace();
        }

    }
//	For Creating a table with columnnames,data and total

    public void createTable(ArrayList data, String[][] columnnames, String[][] rowspans, String[][] colspans, String[] aligns, String[] totals, boolean wantSno, ArrayList extraFooter, int[] widths) {
        try {
            PdfPTable aTable = new PdfPTable((columnnames[0].length + 1));
            String[] tokens = mprefix.split("ZZ");
            if (tokens.length > 1) {
                int widt = Integer.parseInt(tokens[1]);
                aTable.setWidthPercentage(widt);
                //System.out.println("PDF TAble Wisth is :"+widt);
            } else {
                aTable.setWidthPercentage(100);
//System.out.println("PDF TAble Wisth is :100");
            }
            aTable.getDefaultCell().setLeading(10f, 0f);
            //aTable.setAutoFillEmptyCells(true);
            //	aTable.setWidth(100);
            aTable.setWidths(widths);
            //aTable.setPadding(4);
            //	aTable.setSpacing(0);
            //	aTable.setBackgroundColor(new Color(255,255,255));	
            //	aTable.setCellsFitPage(true);	
            writeHeadings(columnnames, aTable, rowspans, colspans, wantSno);

//To generate Sanction Statement
            if (mprefix.equals("SanctionStatement") || mprefix.equals("PREPSanctionStatement")) {

                Paragraph tSpace = new Paragraph("  ", FontFactory.getFont(FontFactory.TIMES_ROMAN, 8, com.lowagie.text.Font.BOLD, new Color(0, 0, 0)));


                Calendar cal = Calendar.getInstance();
                String tdate = cal.get(cal.DATE) + "/" + (((cal.get(cal.MONTH)) % 12) + 1) + "/" + (cal.get(cal.YEAR) + (cal.get(cal.MONTH)) / 12);
                //Paragraph prl = new Paragraph("         Ratified above thirty cases processed and sactioned on "+tdate , FontFactory.getFont(FontFactory.TIMES_ROMAN, 10, com.lowagie.text.Font.BOLD, new Color(0,0,0)));
                Paragraph prl = new Paragraph("         The above thirty cases from 1 to 30 are sanctioned under C.M.R.F. on " + tdate, FontFactory.getFont(FontFactory.TIMES_ROMAN, 10, com.lowagie.text.Font.BOLD, new Color(0, 0, 0)));
                prl.setAlignment(Element.ALIGN_LEFT);

                Paragraph cm = new Paragraph("CHIEF MINISTER                  ", FontFactory.getFont(FontFactory.TIMES_ROMAN, 12, com.lowagie.text.Font.BOLD, new Color(0, 0, 0)));
                cm.setAlignment(Element.ALIGN_RIGHT);
                Paragraph sd = new Paragraph("Sd/-                                    ", FontFactory.getFont(FontFactory.TIMES_ROMAN, 10, com.lowagie.text.Font.BOLD, new Color(0, 0, 0)));
                sd.setAlignment(Element.ALIGN_RIGHT);

                ArrayList temp = new ArrayList();
                int k = 0;
                int samp = 0;
                for (int i = 0; i < data.size(); i++, k++) {

                    if (k == data.size()) {
                        writeData(temp, aTable, aligns, wantSno, totals);
                        document.add(aTable);
                        //document.add(tSpace);
                        //document.add(prl);				
                        //document.add(tSpace);
                        //document.add(tSpace);
                        //document.add(cm);
                        document.newPage();
                        temp = new ArrayList();
                        aTable = new PdfPTable((columnnames[0].length + 1));
                        if (tokens.length > 1) {
                            int widt = Integer.parseInt(tokens[1]);
                            aTable.setWidthPercentage(widt);
                        } else {
                            aTable.setWidthPercentage(100);
                        }
                        aTable.getDefaultCell().setLeading(10f, 0f);
                        aTable.setWidths(widths);
                        writeHeadings(columnnames, aTable, rowspans, colspans, wantSno);
                        //k=0;
                    }
                    temp.add(data.get(i));
                }//end of for
                //prl = new Paragraph("         Ratified above 1 to "+(k)+" cases processed and sanctioned on "+tdate , FontFactory.getFont(FontFactory.TIMES_ROMAN, 10, com.lowagie.text.Font.BOLD, new Color(0,0,0)));
                prl = new Paragraph("         The above  1 to " + (k) + " cases are sanctioned under C.M.R.F. on " + tdate, FontFactory.getFont(FontFactory.TIMES_ROMAN, 10, com.lowagie.text.Font.BOLD, new Color(0, 0, 0)));


                prl.setAlignment(Element.ALIGN_LEFT);

                writeData(temp, aTable, aligns, wantSno, totals);
                document.add(aTable);
                //document.add(tSpace);
                //document.add(tSpace);
                //document.add(tSpace);
                //document.add(tSpace);
                document.add(prl);
                //document.add(tSpace);
                //document.add(tSpace);
                if (signed.equals("Y")) {
                    document.add(sd);
                }
                document.add(cm);

            } else {
                writeData(data, aTable, aligns, wantSno, totals);
                writeTotals(data, aTable, totals, wantSno);
                Paragraph tSpace = new Paragraph("  ", FontFactory.getFont(FontFactory.TIMES_ROMAN, 8, com.lowagie.text.Font.BOLD, new Color(0, 0, 0)));
                document.add(tSpace);
                if (null != extraFooter) {
                    writeExtraFooter(extraFooter, aTable, (columnnames[0].length + 1));
                }
                document.add(aTable);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void writeExtraFooter(ArrayList extraFooter, PdfPTable aTable, int colspan) {
        try {


            PdfPCell hcell = null;
            String prefix = "";
            for (int i = 0; i < extraFooter.size(); i++) {
                ArrayList tempFooter = new ArrayList();
                tempFooter = (ArrayList) extraFooter.get(i);
                for (int j = 0; j < tempFooter.size(); j++) {
                    if (j == 0) {
                        prefix = "Total No of Cases : ";
                    } else {
                        prefix = prefix + "  Total Amount in Rs : ";
                    }
                    prefix = prefix + Converter.convert((String) tempFooter.get(j)) + " ";
                }

            }

            hcell = new PdfPCell(new Phrase(prefix, FontFactory.getFont(FontFactory.TIMES_ROMAN, 12, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));

            hcell.setColspan(colspan);
            hcell.setBorderColor(new Color(0, 0, 0));
            hcell.setBackgroundColor(new Color(255, 255, 255));
            hcell.setHorizontalAlignment(Element.ALIGN_CENTER);
            aTable.addCell(hcell);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

//	For writing columnnames	
    public void writeHeadings(String[][] columnnames, PdfPTable aTable, String[][] rowspans, String[][] colspans, boolean wantSno) {
        try {
            PdfPCell hcell = null;
            if (wantSno) {
                hcell = new PdfPCell(new Phrase("SNo", FontFactory.getFont(FontFactory.TIMES_ROMAN, 8, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                hcell.setColspan(1);
                //hcell.setRowspan(1);
                hcell.setBorderColor(new Color(0, 0, 0));
                hcell.setBackgroundColor(new Color(213, 214, 223));
                hcell.setMinimumHeight(15);
                hcell.setHorizontalAlignment(Element.ALIGN_CENTER);
                hcell.setVerticalAlignment(Element.ALIGN_MIDDLE);

                aTable.addCell(hcell);
            }
            for (int i = 0; i < columnnames.length; i++) {
                for (int j = 0; j < columnnames[i].length; j++) {
                    hcell = new PdfPCell(new Phrase(columnnames[i][j].replaceAll("<br>", "\n"), FontFactory.getFont(FontFactory.TIMES_ROMAN, 8, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                    hcell.setColspan(Integer.parseInt(colspans[i][j]));
                    //	hcell.setRowspan(Integer.parseInt(rowspans[i][j]));
                    hcell.setBorderColor(new Color(0, 0, 0));
                    hcell.setBackgroundColor(new Color(213, 214, 223));
                    hcell.setMinimumHeight(15);
                    hcell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    hcell.setVerticalAlignment(Element.ALIGN_MIDDLE);

                    aTable.addCell(hcell);
                }
            }
            if (mprefix.equals("DistWiseAbstractAllCases")) {
                for (int j = 0; j < 10; j++) {
                    hcell = new PdfPCell(new Phrase("(" + (j + 1) + ")", FontFactory.getFont(FontFactory.TIMES_ROMAN, 8, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                    hcell.setColspan(1);
                    //	hcell.setRowspan(Integer.parseInt(rowspans[i][j]));
                    hcell.setBorderColor(new Color(0, 0, 0));
                    hcell.setBackgroundColor(new Color(213, 214, 223));
                    hcell.setMinimumHeight(15);
                    hcell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    hcell.setVerticalAlignment(Element.ALIGN_MIDDLE);

                    aTable.addCell(hcell);
                }
                aTable.setHeaderRows(2);
            } else {
                aTable.setHeaderRows(1);
            }
            //aTable.endHeaders();

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

//	for writing table data
    public void writeData(ArrayList data, PdfPTable aTable, String[] aligns, boolean wantSno, String[] totals) {
        try {
            PdfPCell hcell = null;
            ArrayList templist;
            String celldata = "";



            for (int i = 0, k = 0; i < data.size(); i++, k++) {

                if (wantSno) {
                    hcell = new PdfPCell(new Phrase("" + (k + 1), FontFactory.getFont(FontFactory.TIMES_ROMAN, 10, com.lowagie.text.Font.NORMAL, new Color(0, 0, 0))));
                    hcell.setColspan(1);
                    hcell.setBorderColor(new Color(0, 0, 0));
                    hcell.setBackgroundColor(new Color(255, 255, 255));
                    hcell.setLeading(14f, 0f);
                    if (overlap.equals("Y")) {
                        hcell.setMinimumHeight(25);
                    } else {
                        hcell.setMinimumHeight(20);
                    }
                    hcell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    aTable.addCell(hcell);
                }
                templist = new ArrayList();
                templist = (ArrayList) data.get(i);
                String party = "";
                String economy = "";
                for (int j = 0; j < templist.size(); j++) {
                    if (totals[j].equals("0")) {
                        celldata = (String) templist.get(j);
                    } else {
                        celldata = Converter.convert((String) templist.get(j));
                    }

                    if (mprefix.equals("SanctionStatement") && j == 1) {
                        party = celldata.substring(celldata.indexOf("$") + 1);
                        economy = celldata.substring(celldata.lastIndexOf("$") + 1, celldata.length());
                        celldata = celldata.substring(0, celldata.indexOf("$"));
                    }

                    if (celldata == null) {
                        celldata = "";
                    }
                    celldata = celldata.replaceAll("<br>", "\n");
                    celldata = removeHtmlTags(celldata);
                    /*if(mprefix.equals("SanctionStatement")  && j==1){
                    if(party!=null && party.indexOf("T.R.S")>=0)
                    hcell=new PdfPCell(new Phrase(celldata , FontFactory.getFont(FontFactory.TIMES_ROMAN,9, com.lowagie.text.Font.BOLD, Color.PINK)));
                    
                    else if(party!=null && party.indexOf("T.D.P")>=0)
                    hcell=new PdfPCell(new Phrase(celldata , FontFactory.getFont(FontFactory.TIMES_ROMAN,9, com.lowagie.text.Font.BOLD, Color.YELLOW)));
                    
                    else if(party!=null && party.indexOf("CPI")>=0)
                    hcell=new PdfPCell(new Phrase(celldata , FontFactory.getFont(FontFactory.TIMES_ROMAN,9, com.lowagie.text.Font.BOLD, Color.RED)));
                    
                    else if(party!=null && party.indexOf("xxx")>=0)
                    hcell=new PdfPCell(new Phrase(celldata , FontFactory.getFont(FontFactory.TIMES_ROMAN,9, com.lowagie.text.Font.BOLD, Color.GRAY)));
                    
                    else if(party!=null && (party.indexOf("I.N.C")>=0 || party.indexOf("INC")>=0))
                    hcell=new PdfPCell(new Phrase(celldata , FontFactory.getFont(FontFactory.TIMES_ROMAN,9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                    
                    else
                    hcell=new PdfPCell(new Phrase(celldata , FontFactory.getFont(FontFactory.TIMES_ROMAN,9, com.lowagie.text.Font.BOLD, Color.GRAY)));
                    }*/
                    //if(mprefix.equals("SanctionStatement")  && (j==1 || (j==4 && economy.equals("Very Poor")))){
                    if (mprefix.equals("SanctionStatement") && (j == 1)) {
                        if (party != null && party.indexOf("TRS") >= 0) {
                            hcell = new PdfPCell(new Phrase(celldata, FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                            hcell.setBorderColor(new Color(0, 0, 0));
                            hcell.setBackgroundColor(Color.PINK);
                        } else if (party != null && (party.indexOf("TDP") >= 0 || party.indexOf("TDP") >= 0)) {
                            hcell = new PdfPCell(new Phrase(celldata, FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                            hcell.setBorderColor(new Color(0, 0, 0));
                            hcell.setBackgroundColor(Color.YELLOW);
                        } else if (party != null &&  (party.indexOf("CPI") >= 0 || party.indexOf("CPI(M)") >= 0)) {
                            hcell = new PdfPCell(new Phrase(celldata, FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                            hcell.setBorderColor(new Color(0, 0, 0));
                            hcell.setBackgroundColor(Color.RED);
                        } else if (party != null && party.indexOf("xxx") >= 0) {
                            hcell = new PdfPCell(new Phrase(celldata, FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                            hcell.setBorderColor(new Color(0, 0, 0));
                            hcell.setBackgroundColor(Color.GRAY);
                        } else if (party != null && party.indexOf("BJP") >= 0) {
                            hcell = new PdfPCell(new Phrase(celldata, FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                            hcell.setBorderColor(new Color(0, 0, 0));
                            hcell.setBackgroundColor(Color.ORANGE);
                        } else if (party != null && party.indexOf("COLL") >= 0) {
                            hcell = new PdfPCell(new Phrase(celldata, FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                            hcell.setBorderColor(new Color(0, 0, 0));
                            hcell.setBackgroundColor(new Color(189, 155, 87));
                        } else if (party != null && (party.indexOf("I.N.C") >= 0 || party.indexOf("INC") >= 0)) {
                            hcell = new PdfPCell(new Phrase(celldata, FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                            hcell.setBorderColor(new Color(0, 0, 0));
                            hcell.setBackgroundColor(new Color(255, 255, 255));
                        } else if (party != null && party.indexOf("AIMIM") >= 0) {
                            hcell = new PdfPCell(new Phrase(celldata, FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                            hcell.setBorderColor(new Color(0, 0, 0));
                            hcell.setBackgroundColor(Color.GREEN);
                        } else if (party != null && party.indexOf("COL") >= 0) {
                            hcell = new PdfPCell(new Phrase(celldata, FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                            hcell.setBorderColor(new Color(0, 0, 0));
                            hcell.setBackgroundColor(Color.CYAN);
                        } else if (party != null && party.indexOf("P.REP") >= 0) {
                            hcell = new PdfPCell(new Phrase(celldata, FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                            hcell.setBorderColor(new Color(0, 0, 0));
                            hcell.setBackgroundColor(new Color(198, 226, 255));
                        } else {
                            hcell = new PdfPCell(new Phrase(celldata, FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                            hcell.setBorderColor(new Color(0, 0, 0));
                            hcell.setBackgroundColor(Color.GRAY);
                        }

                    } else if (mprefix.equals("SanctionStatement") && ((j == 4 && economy.equals("2")))) {
                        hcell = new PdfPCell(new Phrase(celldata, FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                        hcell.setBorderColor(new Color(0, 0, 0));
                        hcell.setBackgroundColor(new Color(201, 219, 255));

                    } else if (mprefix.equals("SanctionStatement") && ((j == 4 && economy.equals("3")))) {
                        hcell = new PdfPCell(new Phrase(celldata, FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                        hcell.setBorderColor(new Color(0, 0, 0));
                        hcell.setBackgroundColor(new Color(183, 207, 255));

                    } /*else if (mprefix.equals("SanctionStatement") && ((j == 4 && economy.equals("4")))) {
                        hcell = new PdfPCell(new Phrase(celldata, FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                        hcell.setBorderColor(new Color(0, 0, 0));
                        hcell.setBackgroundColor(new Color(155, 188, 255));//blue

                    } */
                    else {
                        hcell = new PdfPCell(new Phrase(celldata, FontFactory.getFont(FontFactory.TIMES_ROMAN, 10, com.lowagie.text.Font.NORMAL, new Color(0, 0, 0))));
                        hcell.setBorderColor(new Color(0, 0, 0));
                        hcell.setBackgroundColor(new Color(255, 255, 255));
                    }
                    hcell.setColspan(1);
                    //					hcell.setColspan(Integer.parseInt(colspans[i][j]));
                    //                           hcell.setRowspan(Integer.parseInt(rowspans[i][j]));
                    hcell.setLeading(10f, 0f);
                    hcell.setMinimumHeight(20);
                    //hcell.setBorderColor(new Color(0,0,0));
                    //hcell.setBackgroundColor(new Color(255,255,255));	

                    if (aligns[j].equals("0")) {
                        hcell.setHorizontalAlignment(Element.ALIGN_CENTER);
                    } else if (aligns[j].equals("1")) {
                        hcell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    } else {
                        hcell.setHorizontalAlignment(Element.ALIGN_RIGHT);
                    }

                    aTable.addCell(hcell);
                }


            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void loop(com.lowagie.text.Table aTable) {
        try {
            Cell hcell = null;

            hcell = new Cell(new Phrase("", FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
            hcell.setColspan(1);
            // hcell.setRowspan(1);
            hcell.setBorderColor(new Color(255, 255, 255));
            hcell.setBackgroundColor(new Color(99, 00, 00));

            hcell.setHorizontalAlignment(Element.ALIGN_CENTER);

            aTable.addCell(hcell);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
//	for writing totals

    public void writeTotals(ArrayList data, PdfPTable aTable, String[] totals, boolean wantSno) {
        ArrayList rowData;
        wantSno = true;
        try {
            PdfPCell hcell = null;
            PdfPCell tcell = null;
            boolean totFlag = false;
            String[] tot = new String[totals.length];

            for (int i = 0; i < totals.length; i++) {
                tot[i] = "0";
                if (!totals[i].equals("0")) {
                    totFlag = true;

                }

            }

            if (totFlag) {

                for (int i = 0; i < data.size(); i++) {
                    rowData = new ArrayList();
                    rowData =  (ArrayList) data.get(i);


                    for (int j = 0; j < rowData.size(); j++) {
                        if (!totals[j].equals("0")) {
                            tot[j] = "" + (Long.parseLong((String) rowData.get(j)) + Long.parseLong(tot[j])) + "";

                        } else {
                            tot[j] = "";
                        }
                    }

                }


                //loop(aTable);
                //loop(aTable);
                //loop(aTable);
                //loop(aTable);
                if (wantSno) {
                    hcell = new PdfPCell(new Phrase("Total", FontFactory.getFont(FontFactory.TIMES_ROMAN, 11, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                    hcell.setColspan(1);
                    hcell.setBorderColor(new Color(0, 0, 0));
                    hcell.setBackgroundColor(new Color(213, 214, 223));
                    hcell.setMinimumHeight(30);
                    hcell.setHorizontalAlignment(Element.ALIGN_CENTER);

                    aTable.addCell(hcell);
                }

                for (int i = 0; i < tot.length; i++) {
                    try {
                        tcell = new PdfPCell(new Phrase("" + Converter.convert(tot[i]), FontFactory.getFont(FontFactory.TIMES_ROMAN, 11, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
                        tcell.setColspan(1);
                        tcell.setBorderColor(new Color(0, 0, 0));
                        tcell.setBackgroundColor(new Color(213, 214, 223));

                        tcell.setMinimumHeight(30);
                        tcell.setHorizontalAlignment(Element.ALIGN_RIGHT);

                        aTable.addCell(tcell);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    public void onOpenDocument(PdfWriter writer, Document document) {
        try {

            table = new PdfPTable(2);
            Phrase p = new Phrase();
            tpl = writer.getDirectContent().createTemplate(100, 100);
            tpl.setBoundingBox(new Rectangle(-10, -10, 100, 100));
            helv = BaseFont.createFont("Helvetica", BaseFont.WINANSI, false);
        } catch (Exception e) {
            throw new ExceptionConverter(e);
        }
    }

    public void onEndPage(PdfWriter writer, Document document) {

        PdfContentByte cb = writer.getDirectContent();
        cb.saveState();
        table.setTotalWidth(document.right() - document.left());

        String text = "Page " + writer.getPageNumber() + " of ";
        float textSize = helv.getWidthPoint(text, 9);
        float textBase = document.bottom() - 20;
        cb.beginText();
        cb.setFontAndSize(helv, 9);
        if ((writer.getPageNumber() & 1) == 1) {

            cb.setTextMatrix(document.left(), textBase);
            cb.showText(text);
            cb.endText();
            cb.addTemplate(tpl, document.left() + textSize, textBase);
        } else {
            float adjust = helv.getWidthPoint("0", 9);
            /*
            cb.setTextMatrix(document.left() - textSize - adjust, textBase);
            cb.showText(text);
            cb.endText();
            cb.addTemplate(tpl, document.left() - adjust, textBase);
             */
            cb.setTextMatrix(document.left(), textBase);
            cb.showText(text);
            cb.endText();
            cb.addTemplate(tpl, document.left() + textSize, textBase);
        }
        cb.saveState();
        cb.restoreState();

    }

    public void onStartPage(PdfWriter writer, Document document) {
    }

    public void onCloseDocument(PdfWriter writer, Document document) {
        tpl.beginText();
        tpl.setFontAndSize(helv, 9);
        tpl.setTextMatrix(0, 0);
        tpl.showText("" + (writer.getPageNumber() - 1));
        tpl.endText();
    }
    //below methods written on 18/12/2020 for vip check issued cases 
    public String CreateAllPDF(String prefix,  String[] headings, String[][] columnnames, String[][] rowspans, String[][] colspans, String[] aligns, String[] totals, boolean wantSno, ArrayList extraFooter, HttpServletRequest req, int[] widths,ArrayList cnos,String cmpno,ArrayList otherConst,VIPWiseChequeIssuesCasesService vipWiseSanctionedService,VIPWiseChequeIssuesCases vipWiseSanctionedReport) {

        HttpSession sess = req.getSession();
        ServletContext context = sess.getServletContext();       
        String context_path=context.getRealPath("/");;
        System.out.println("context path is -------> "+context_path);
        java.util.Date d = new java.util.Date();
        String filename = prefix + "PDF" + d.getTime() + ".pdf";
        String file = context_path + "/pdffiles/" ;
        File folder = new File(file);           
        if(!folder.exists()){
            folder.mkdirs();
        }  
         file = context_path + "/pdffiles/" + filename;
        //String file = context_name+"/pdffiles/"+filename;
        mprefix = prefix;
        File fileobj = new File(file);

        signed = (String) req.getParameter("signed");
        if (signed == null) {
            signed = "N";
        }
        overlap = (String) req.getParameter("overlap");
        if (overlap == null) {
            overlap = "N";
        }

        try {
                    System.out.println("prefix-------> "+prefix);


          
                document = new Document(PageSize.A4);
          

            PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(fileobj));
            //PdfPageEvent pageEvent = new GeneratePDF();
            //writer.setPageEvent(pageEvent);
            document.setMargins(50, 5, 10, 20);
            document.open();
            Paragraph tDate = new Paragraph("Date :  " + new java.util.Date() + "", FontFactory.getFont(FontFactory.TIMES_ROMAN, 8, com.lowagie.text.Font.BOLD, new Color(0, 0, 0)));
            tDate.setAlignment(Element.ALIGN_RIGHT);

            Paragraph tSpace = new Paragraph("  ", FontFactory.getFont(FontFactory.TIMES_ROMAN, 8, com.lowagie.text.Font.BOLD, new Color(0, 0, 0)));
            tSpace.setAlignment(Element.ALIGN_RIGHT);

            //ol
            if (overlap.equals("Y")) {
                document.add(tSpace);
            }
            //document.add(tSpace);

            //document.add(tSpace);

            if (!prefix.equals("RecomendedConWise")) {
                document.add(tDate);
            }
            if (overlap.equals("Y")) {
                document.add(tSpace);
            }
            //document.add(tSpace);
//				document.add(tSpace);
//document.add(tSpace);
            System.out.println("cno--"+cnos.size());
        	// VIPWiseChequeIssuesCasesService vipWiseSanctionedService = null;

        //    VIPWiseChequeIssuesCasesDao vipWiseCases=new VIPWiseChequeIssuesCasesDao();
            ArrayList   data=null;
 for(int k=0;k<cnos.size();k++){
                       String recommended_by=cnos.get(k).toString();
                         System.out.println("recommended_by"+recommended_by);
                          recommended_by = recommended_by.replace("[", "").replace("]", "");

                         System.out.println("recommended_by=="+recommended_by);

                     if(recommended_by.equals("998")){
                         System.out.println("otherConst--"+otherConst.size());
                         for(int l=0;l<otherConst.size();l++){
                             String otherconVal=otherConst.get(l).toString();
                             otherconVal = otherconVal.replace("[", "").replace("]", "");

                             System.out.println("otherconVal=="+otherconVal);

                    //  ArrayList data = vipWiseCases.getVIPSanctionedDetails(recommended_by,cmpno, null);
                         data = vipWiseSanctionedService.getVIPSanctionedDetails(recommended_by,cmpno,otherconVal, vipWiseSanctionedReport);
                         for (int i = 0; i < headings.length; i++) {
                             writeHeading(headings[i]);
                         }
                         //writeHeading(headings[0]);
                         //writeHeading(headings[1]);
                         //writeHeading(headings[2]);

                         //document.add(tSpace);
                         //document.add(tSpace);
                         //document.add(tSpace);
                         createTable(data, columnnames, rowspans, colspans, aligns, totals, wantSno, extraFooter, widths);

                        //  ArrayList recordData = (ArrayList) data.get(0);
                        //  String prName = (String) recordData.get(0);
                        //  int totalCheques = data.size();
                        //  String esignedDate = req.getParameter("date");
                        // //  System.out.println("esignedDate*****"+esignedDate);

                        //  acknowledgement(prName, totalCheques, cmpno, recommended_by, otherconVal, esignedDate);
                         document.newPage() ;
                         }
                         
                     }else {
                            data = vipWiseSanctionedService.getVIPSanctionedDetails(recommended_by,cmpno,"", vipWiseSanctionedReport);
                            for (int i = 0; i < headings.length; i++) {
                                writeHeading(headings[i]);
                            }
                            //writeHeading(headings[0]);
                            //writeHeading(headings[1]);
                            //writeHeading(headings[2]);

                            //document.add(tSpace);
                            //document.add(tSpace);
                            //document.add(tSpace);
                            createTable(data, columnnames, rowspans, colspans, aligns, totals, wantSno, extraFooter, widths);

                            // ArrayList recordData = (ArrayList) data.get(0);
                            // String prName = (String) recordData.get(0);
                            // int totalCheques = data.size();
                            // String esignedDate = req.getParameter("date");
                            // // System.out.println("esignedDate====="+esignedDate);

                            // acknowledgement(prName, totalCheques, cmpno, recommended_by, "-", esignedDate);
                            document.newPage();
 
                     }

                      System.out.println("data=="+data.size());

         /*   for (int i = 0; i < headings.length; i++) {
                writeHeading(headings[i]);
            }
            //writeHeading(headings[0]);
            //writeHeading(headings[1]);
            //writeHeading(headings[2]);

            //document.add(tSpace);
            //document.add(tSpace);
            //document.add(tSpace);
            createTable(data, columnnames, rowspans, colspans, aligns, totals, wantSno, extraFooter, widths);
            document.newPage() ;*/
 }
           
            document.close();
            writer.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
        return "../tgcmrf/pdffiles/" + filename;
    }

    public void acknowledgement(String prName, int totalCheques, String cmpNo, String cno, String otherConst, String esignedDate) {
        try {
            if (totalCheques > 3) {
                document.newPage();
                Paragraph space = new Paragraph("\n\n");
                document.add(space);
            } else {
                Paragraph space = new Paragraph("\n\n\n");
                document.add(space);
            }
            // Define a common font once
            Font regularFont = FontFactory.getFont(FontFactory.TIMES_ROMAN, 12, Font.BOLD, new Color(0, 0, 0));

            // Helper to create and add a left-aligned paragraph
            Consumer<String> addLeftAlignedPara = text -> {
                Paragraph p = new Paragraph(text, regularFont);
                p.setAlignment(Element.ALIGN_LEFT);
                try {
                    document.add(p);
                } catch (DocumentException e) {
                    e.printStackTrace();
                }
            };

            // Acknowledgment content
            addLeftAlignedPara.accept("\n\n");
            addLeftAlignedPara.accept("I acknowledge the receipt of above mentioned cheques.");
            addLeftAlignedPara.accept("Series : " + cmpNo);
            addLeftAlignedPara.accept("Total number of cheques received : " + totalCheques);
            addLeftAlignedPara.accept("Name : ");
            addLeftAlignedPara.accept("Phone number : ");
            addLeftAlignedPara.accept("People Representative name : " + prName);
            addLeftAlignedPara.accept("Date & Time : \n\n\n");

            Paragraph Signature = new Paragraph("Signature", regularFont);
            Signature.setIndentationLeft(400f);
            document.add(Signature);

            String tokenNumber = cmpNo.trim() + "#" + cno.trim() + "#" + otherConst.trim();

            //QR Payload Data
            String qrData = "{\"tokenNumber\" : \""+ tokenNumber +"\"," + 
                "\"cno\" : \""+cno.trim()+"\"," + 
                "\"otherConst\" : \""+otherConst.trim()+"\"," + 
                "\"esignedDate\" : \""+esignedDate.trim()+"\"," + 
                "\"totalCheques\" : \""+totalCheques+"\"}";
            
            // System.out.println("qrData=="+qrData);
            //Generate QR
            byte[] qrCodeImage = QRCodeUtil.generateQRCode(qrData, 80, 80);
            
            if (qrCodeImage != null) {
                Image qrImage = Image.getInstance(qrCodeImage); // Replace with actual path
                qrImage.scaleToFit(120, 120); // Scale the image if needed
                qrImage.setAlignment(Image.ALIGN_RIGHT);
                document.add(qrImage);
            } else {
                document.add(new Paragraph("QR Code Here", regularFont));
            }

            document.newPage() ;
        } catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
        }
    }
}

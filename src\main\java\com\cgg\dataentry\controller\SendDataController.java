package com.cgg.dataentry.controller;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.cgg.dataentry.model.SendData;
import com.cgg.dataentry.service.SendDataService;

@Controller
@RequestMapping(value = "SendData")
public class SendDataController {
	
	@Autowired
	private SendDataService sendDataService;
	
	
	@RequestMapping(method = RequestMethod.GET)
	public ModelAndView getPage(Model model) 
	{
		ModelAndView mav = new ModelAndView();

		SendData sendData = new SendData();
		mav.addObject("sendData", sendData);
		
		List<SendData> cmpNos = new ArrayList<SendData>();
		cmpNos = sendDataService.getCmpNos();
		mav.addObject("cmpNos",cmpNos);
		mav.setViewName("sendData");
		
		/*
		 * BindingResult result = (BindingResult) model.getAttribute("BindingResult");
		 * if (result != null && result.hasErrors()) {
		 * mav.addObject("org.springframework.validation.BindingResult", result);
		 * System.out.
		 * println("HAS ERRORS!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
		 * }
		 */
		 
		return mav;
	}
	
	
	@RequestMapping(value = "/Update" , method = RequestMethod.POST)
	public String Update(@Valid@ModelAttribute("sendData") SendData sendData,BindingResult result,RedirectAttributes redirectAttributes,Model model,HttpServletRequest request) throws Exception
	{
		String msg=null;
		
		if(result.hasErrors())
		{
			
			model.addAttribute("sendData", sendData);
			List<SendData> cmpNos = new ArrayList<SendData>();
			cmpNos = sendDataService.getCmpNos();
			model.addAttribute("cmpNos", cmpNos);
			return "sendData";
			 
		}
		int cnt = sendDataService.update(sendData.getCmpNumber(),request);
		if(cnt>0)		
			msg = "Data Successfully Sent to Revenue Department with Conrol No's : "+sendData.getCmpNumber();		
		else		
			msg = "Data Sending Failed!!!";
	
		
		redirectAttributes.addFlashAttribute("msg",msg);
		return "redirect:/SendData";
	}
	
}

package com.cgg.reports.repositories;

import java.util.List;

import javax.persistence.Tuple;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.cgg.dataentry.entities.MlaCmrf;

@Repository
public interface CMRFEnhancementReportRepository  extends JpaRepository<MlaCmrf, String>{
	
	@Query(value = " SELECT a.pat_name,father_son_of,pat_address,a.cmrf_no,case when con.cno = '998' then 'PEOPLE REPRESENTATIVE ' || coalesce('(' || other_const || ')', '')"
			+ " when con.cno in ('999', '997') then mlamp else mlamp || ' (' || coalesce(cname, '')||')'  end as mlamp, "
			+ " h.hospname,to_char(coalesce(a.req_amt,0),'FM99,FM99,99,999D') AS req_amt, "
			+ " to_char(coalesce(a.sanc_amt,0),'FM99,FM99,99,999D') AS sanc_amt "
			+ " FROM cmrelief a "
			+ " INNER JOIN constituency con ON cast(a.recommended_by AS INTEGER) = con.cno "
			+ " INNER JOIN hospital h ON a.hosp_code = h.hospcode "
			+ " WHERE a.cmrf_type='3' AND DATE(a.cmrf_dt) BETWEEN TO_DATE(:dateFrom, 'YYYY-MM-DD') AND TO_DATE(:dateTo, 'YYYY-MM-DD') ",  nativeQuery = true)
	List<Tuple> getCmrfEnhancementTotalCasesReport(@Param("dateFrom") String dateFrom, @Param("dateTo") String dateTo);
	
	
	@Query(value = " select coalesce(other_const,'-') other_const,case when con.cno = '998' then 'PEOPLE REPRESENTATIVE ' || coalesce('(' || other_const || ')', '')  "
			+ " when con.cno in ('999', '997') then mlamp else mlamp || ' (' || coalesce(cname, '')||')'  end as mlamp, "
			+ " to_char(COUNT(COALESCE(a.recommended_by ,'0')),'FM99,FM99,99,999D') cases,cno "
			+ " FROM cmrelief a "
			+ " INNER JOIN constituency con ON cast(a.recommended_by AS INTEGER) = con.cno "			
			+ " WHERE a.cmrf_type='3' "
			+ " AND DATE(cmrf_dt) BETWEEN TO_DATE(:dateFrom, 'YYYY-MM-DD') AND TO_DATE(:dateTo, 'YYYY-MM-DD') "
			+ " group by cno,mlamp,cname,other_const order by mlamp", 
	        nativeQuery = true)
	List<Tuple> getCmrfEnhancementPRWiseReportCnt(@Param("dateFrom") String dateFrom, @Param("dateTo") String dateTo);
	
	
	@Query(value = " SELECT  a.pat_name,father_son_of,pat_address,a.cmrf_no,case when con.cno = '998' then 'PEOPLE REPRESENTATIVE ' || coalesce('(' || other_const || ')', '') "
			+ " when con.cno in ('999', '997') then mlamp else mlamp || ' (' || coalesce(cname, '')||')'  end as mlamp, "
			+ " h.hospname,to_char(coalesce(a.req_amt,0),'FM99,FM99,99,999D') AS req_amt, "
			+ " to_char(coalesce(a.sanc_amt,0),'FM99,FM99,99,999D') AS sanc_amt "
			+ " FROM cmrelief a "
			+ " INNER JOIN constituency con ON cast(a.recommended_by AS INTEGER) = con.cno "
			+ " INNER JOIN hospital h ON a.hosp_code = h.hospcode "
			+ " WHERE a.cmrf_type='3' AND cast(a.recommended_by as TEXT) = :constNo AND a.other_const = :otherConst "
			+ " AND DATE(a.cmrf_dt) BETWEEN TO_DATE(:dateFrom, 'YYYY-MM-DD') AND TO_DATE(:dateTo, 'YYYY-MM-DD')", 
	        nativeQuery = true)
	List<Tuple> getPRWiseEnhancementDetailsWithOtherConst(@Param("constNo") String constNo, @Param("otherConst") String otherConst, @Param("dateFrom") String dateFrom, @Param("dateTo") String dateTo);
	 
	
	@Query(value = " SELECT a.pat_name,father_son_of,pat_address,a.cmrf_no,case when con.cno = '998' then 'PEOPLE REPRESENTATIVE ' || coalesce('(' || other_const || ')', '') "
			+ "	when con.cno in ('999', '997') then mlamp else mlamp || ' (' || coalesce(cname, '')||')'  end as mlamp, "
			+ "	h.hospname,to_char(coalesce(a.req_amt,0),'FM99,FM99,99,999D') AS req_amt, "
			+ "	to_char(coalesce(a.sanc_amt,0),'FM99,FM99,99,999D') AS sanc_amt "
	        + " FROM cmrelief a "
	        + " INNER JOIN constituency con ON cast(a.recommended_by AS INTEGER) = con.cno "	   	        
	        + " LEFT JOIN hospital h ON a.hosp_code = h.hospcode "
	        + " WHERE a.cmrf_type='3' AND cast(a.recommended_by as VARCHAR) = :constNo "
	        + " AND DATE(a.cmrf_dt) BETWEEN TO_DATE(:dateFrom, 'YYYY-MM-DD') AND TO_DATE(:dateTo, 'YYYY-MM-DD')", 
	        nativeQuery = true)
	List<Tuple> getPRWiseEnhancementDetails(@Param("constNo") String constNo, @Param("dateFrom") String dateFrom, @Param("dateTo") String dateTo);

}

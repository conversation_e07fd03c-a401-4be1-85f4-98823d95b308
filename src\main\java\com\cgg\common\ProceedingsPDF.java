package com.cgg.common;

import java.awt.Color;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;

import com.lowagie.text.Cell;
import com.lowagie.text.Document;
import com.lowagie.text.Element;
import com.lowagie.text.ExceptionConverter;
import com.lowagie.text.FontFactory;
import com.lowagie.text.PageSize;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Phrase;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfGState;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPageEventHelper;
import com.lowagie.text.pdf.PdfTemplate;
import com.lowagie.text.pdf.PdfWriter;
import com.lowagie.text.pdf.PdfPageEvent;
import javax.servlet.*;
import javax.servlet.http.*;
import com.lowagie.text.HeaderFooter;

public class ProceedingsPDF extends PdfPageEventHelper
{
	Document document ;
	public PdfPTable table;
	public PdfPTable aTable;
    public PdfGState gstate;
    public PdfTemplate tpl;
    public BaseFont helv;
	
	public String CreatePDF(String prefix,ArrayList data,String[]  headings,String[][] columnnames,String[][] rowspans,String[][] colspans,String[] aligns,String[] totals,boolean wantSno,String[] matter,HttpServletRequest req)
	{
		HttpSession sess = req.getSession();
                 ServletContext context = sess.getServletContext();       
        String context_path=context.getRealPath("/");;
        
		java.util.Date d=new java.util.Date();
		String filename=prefix+"PDF"+d.getTime()+".pdf";
		String file = context_path+"/pdffiles/"+filename;
		String temp="";
		System.out.println("file----");
		File fileobj=new File(file);
		
		try
		{
			document =  new Document(PageSize.A4);
			//document = new iTextSharp.text.Document(iTextSharp.text.PageSize.A4.Rotate(),20,20,20,40);

			PdfWriter writer=PdfWriter.getInstance(document, new FileOutputStream(fileobj));
			//PdfPageEvent pageEvent = new GeneratePDF();
		//	writer.setPageEvent(pageEvent);
		document.setMargins(10,10,0,0);
			document.open();
			Paragraph tDate = new Paragraph("Date :  " + new java.util.Date() + "", FontFactory.getFont(FontFactory.TIMES_ROMAN, 6, com.lowagie.text.Font.NORMAL, new Color(0,0,0)));
			tDate.setAlignment(Element.ALIGN_RIGHT);

			Paragraph tSpace = new Paragraph("  " , FontFactory.getFont(FontFactory.TIMES_ROMAN, 5, com.lowagie.text.Font.NORMAL, new Color(0,0,0)));
			tSpace.setAlignment(Element.ALIGN_RIGHT);

			//document.add(tDate);
			//document.add(tSpace);
			Paragraph tHeading1 = new Paragraph(headings[0] , FontFactory.getFont(FontFactory.TIMES_ROMAN, 12, com.lowagie.text.Font.NORMAL, new Color(0,0,0)));
			tHeading1.setAlignment(Element.ALIGN_CENTER);
			Paragraph tHeading2 = new Paragraph(headings[1] , FontFactory.getFont(FontFactory.TIMES_ROMAN, 12, com.lowagie.text.Font.NORMAL, new Color(0,0,0)));
			tHeading2.setAlignment(Element.ALIGN_CENTER);
			Paragraph tHeading3 = new Paragraph(headings[2] , FontFactory.getFont(FontFactory.TIMES_ROMAN, 12, com.lowagie.text.Font.NORMAL, new Color(0,0,0)));
			tHeading3.setAlignment(Element.ALIGN_CENTER);
			

		/*	writeHeading(headings[0]);
			writeHeading(headings[1]);
			writeHeading(headings[2]);
		*/	
			HeaderFooter header = new HeaderFooter(new Phrase(" ", FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.NORMAL, new Color(0,0,0))), false);
                        header.setAlignment(Element.ALIGN_CENTER);
                        header.setBorderColor(new Color(255, 255, 255));
                        header.setBackgroundColor(new Color(255,255,255));
             //   document.setHeader(header);
               // document.setFooter(header);
			for(int i=0;i<matter.length;i++)
			{
			     if(i==0)
				 temp=matter[0];
				matter[i]=matter[i].replaceAll("<br>","\n");
				matter[i]=matter[i].replaceAll("&nbsp;"," ");
			}

			Paragraph matter6= new Paragraph(matter[6] , FontFactory.getFont(FontFactory.TIMES_ROMAN, 11, com.lowagie.text.Font.BOLD, new Color(0,0,0)));
			matter6.setAlignment(Element.ALIGN_LEFT);

			Paragraph matter0= new Paragraph(matter[0] , FontFactory.getFont(FontFactory.TIMES_ROMAN, 11, com.lowagie.text.Font.NORMAL, new Color(0,0,0)));
			matter0.setAlignment(Element.ALIGN_LEFT);

			Paragraph matter1= new Paragraph(matter[1] , FontFactory.getFont(FontFactory.TIMES_ROMAN, 11, com.lowagie.text.Font.NORMAL, new Color(0,0,0)));
			matter1.setAlignment(Element.ALIGN_LEFT);

			Paragraph matter2= new Paragraph(matter[2] , FontFactory.getFont(FontFactory.TIMES_ROMAN, 11, com.lowagie.text.Font.NORMAL, new Color(0,0,0)));
			matter1.setAlignment(Element.ALIGN_LEFT);

			Paragraph matter3= new Paragraph(matter[3] , FontFactory.getFont(FontFactory.TIMES_ROMAN, 11, com.lowagie.text.Font.NORMAL, new Color(0,0,0)));
			matter1.setAlignment(Element.ALIGN_LEFT);

			
			PdfPTable fTable = new PdfPTable((1));
			//fTable.setAutoFillEmptyCells(true);
			 fTable.setWidthPercentage(100);
                        fTable.getDefaultCell().setLeading(14f,0f);
                        //fTable.setWidth(100);
                        //fTable.setPadding(4);
                        //fTable.setSpacing(0);
                        //fTable.setBorderColor(new Color(255, 255, 255));
                        //fTable.setBackgroundColor(new Color(255,255,255));
                        //fTable.setCellsFitPage(true);
                       // fTable.setTableFitsPage(true);
			
			 PdfPCell fcell=null;
			 fcell=new PdfPCell(new Phrase("     "+matter[4].replaceAll("<br>","\n") , FontFactory.getFont(FontFactory.TIMES_ROMAN,11, com.lowagie.text.Font.NORMAL, new Color(0, 0, 0))));
                                        fcell.setColspan(1);
                                        fcell.setBorderColor(new Color(255,255,255));
                                        fcell.setBackgroundColor(new Color(255,255,255));
				fTable.addCell(fcell);
			 fcell=new PdfPCell(new Phrase(matter[5].replaceAll("<br>","\n") , FontFactory.getFont(FontFactory.TIMES_ROMAN,10, com.lowagie.text.Font.NORMAL, new Color(0, 0, 0))));
                                        fcell.setColspan(1);
                                        fcell.setBorderColor(new Color(255,255,255));
                                        fcell.setBackgroundColor(new Color(255,255,255));
				fTable.addCell(fcell);

//To avoid hospital loa in previous proceedings
if(!prefix.equals("PreviousHospital")){
			document.add(tHeading1);
			document.add(tHeading2);
			document.add(tHeading3);
			
			/*document.add(matter0);
			document.add(tSpace);

			document.add(matter6);*/

			document.add(tSpace);
			ArrayList addressdata=new ArrayList();
					addressdata.add(matter[0]);
					addressdata.add(matter[6]);
					createTable(new String("addressinfo"),addressdata,columnnames,rowspans,colspans,aligns,totals,wantSno,null,writer);
			document.add(tSpace);
			document.add(matter1);
			document.add(tSpace);

			document.add(matter2);
			document.add(tSpace);
			document.add(tSpace);

			createTable(prefix,data,columnnames,rowspans,colspans,aligns,totals,wantSno,null,writer);

			document.add(matter3);
			document.add(tSpace);

			document.add(fTable);
}

	Paragraph matter4= new Paragraph(matter[4] , FontFactory.getFont(FontFactory.TIMES_ROMAN, 11, com.lowagie.text.Font.NORMAL, new Color(0,0,0)));

								
//For generating Letters to Patients
			ArrayList tempdata=new ArrayList(),data1=new ArrayList();
	if(prefix.equals("PreviousHospital")){
			for(int i=0;i<data.size();i++){
					tempdata=new ArrayList();
					data1=new ArrayList();
					
					String address=(String)((ArrayList)data.get(i)).get(1);
					String purpose=(String)((ArrayList)data.get(i)).get(2);
					String amount=(String)((ArrayList)data.get(i)).get(3);
					String cmrfid=(String)((ArrayList)data.get(i)).get(0);
					String pat_mat2="Copy To :\n	"+address;

					
					tempdata.add(cmrfid);
					tempdata.add(address);
					tempdata.add(purpose);
					tempdata.add(amount);

					//cb.saveState();
					document.newPage();
				//	cb.restoreState();

					document.add(tHeading1);
					document.add(tHeading2);
					document.add(tHeading3);
//					document.add(tSpace);
					//document.add(matter0);
					//document.add(matter6);
					ArrayList addressdata=new ArrayList();
					addressdata.add(matter[0]);
					addressdata.add(matter[6]);
					document.add(tSpace);
					createTable(new String("addressinfo"),addressdata,columnnames,rowspans,colspans,aligns,totals,wantSno,null,writer);
					document.add(tSpace);




//					document.add(tSpace);
					document.add(matter1);
//					document.add(tSpace);

					String matter33= "       I am directed to state that the following person has requested for rendering financial assitance from Chief Minister's Relief Fund, so as to enable his/her to meet the medical expenses in connection with his/her treatment, as he/she is poor patient.<br>       I am to inform you that the Government have decided to sanction the amount Rs."+amount+"/- against his/her name to meet the medical expenses.<br>       The institution mentioned above has to submit the medical bills upto the value of sanctioned amount as shown against his/her name for reimbursement from Chief Minister's Relief Fund.<br>";

					Paragraph p_matter33= new Paragraph(matter33.replaceAll("<br>","\n") , 
					FontFactory.getFont(FontFactory.TIMES_ROMAN, 11, com.lowagie.text.Font.NORMAL, new Color(0,0,0)));

					p_matter33.setAlignment(Element.ALIGN_LEFT);
					document.add(p_matter33);
//					document.add(tSpace);

					data1.add(tempdata);
			createTable(prefix,data1,columnnames,rowspans,colspans,aligns,totals,wantSno,null,writer);

			document.add(matter3);
//			document.add(tSpace);
			document.add(matter4);
//			document.add(tSpace);

			Paragraph pat_mat22= new Paragraph(pat_mat2.replaceAll("<br>","\n") , 
					FontFactory.getFont(FontFactory.TIMES_ROMAN, 11, com.lowagie.text.Font.NORMAL, new Color(0,0,0)));

					pat_mat22.setAlignment(Element.ALIGN_LEFT);
					document.add(pat_mat22);

			}
	}

			document.close();
			writer.close();
			
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return "../tgcmrf/pdffiles/" + filename;	}


//For Removing HTML tags

	public static String removeHtmlTags(String ins)
  	{
		StringBuffer finalStr = new StringBuffer();

		boolean intag = false;
		for (int i=0; i<ins.length();i++)
		{
		 if (ins.charAt(i) == '<'){
			  intag = true;
			}
		 else if (ins.charAt(i) != '\t' && ins.charAt(i)!= '\n' && ins.charAt(i)!= '>' && !(intag)){
			   finalStr.append(ins.charAt(i));
			}
		  else if (ins.charAt(i) == '>')
		  	{
				intag=false;
	  		}
		}
	return finalStr.toString();
  	}	

//For Writing Report Headings

	public void writeHeading(String heading)
	{
	/*
		 HeaderFooter header = new HeaderFooter(new Phrase(" ", FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.NORMAL, new Color(0,0,0))), true);
                        header.setAlignment(Element.ALIGN_CENTER);
                        header.setBorderColor(new Color(255, 255, 255));
                        header.setBackgroundColor(new Color(255,255,255));
		document.setFooter(header);
	*/
		Paragraph tHeading = new Paragraph(" " + heading.replaceAll("<br>","\n") , FontFactory.getFont(FontFactory.TIMES_ROMAN,12, com.lowagie.text.Font.NORMAL, new Color(0, 0,0 )));
		tHeading.setAlignment(Element.ALIGN_CENTER);

		Paragraph tSpace = new Paragraph("  " , FontFactory.getFont(FontFactory.TIMES_ROMAN, 2, com.lowagie.text.Font.NORMAL, new Color(0,0,0)));

		tSpace.setAlignment(Element.ALIGN_CENTER);

		try
		{
			document.add(tHeading);
			document.add(tSpace);
			
		}
		catch(Exception e)
		{
				e.printStackTrace();
		}

	}
//For Creating a table with columnnames,data and total
	public void createTable(String prefix,ArrayList data,String[][] columnnames,String[][] rowspans,String[][] colspans,String[] aligns,String[] totals,boolean wantSno,ArrayList extraFooter,PdfWriter writer)
	{
		try
		{
			if(prefix.equals("addressinfo"))
			 aTable = new PdfPTable(2);
			else
			 aTable = new PdfPTable((columnnames[0].length+1));


			aTable.setWidthPercentage(100);
			aTable.setTotalWidth(525);
                        aTable.getDefaultCell().setLeading(14f,0f);
			//aTable.setSplitRows(true);
			//aTable.setSplitLate(true);
			int[]  widths={};
			if(prefix.equals("RevisedLOA_CH"))
			{
				widths=new int[6];
				widths[0]=4;
				widths[1]=18;
				widths[2]=27;
				widths[3]=20;
				widths[4]=20;
				widths[5]=12;
			}else
				if(prefix.equals("Hospital"))
			{
				widths=new int[5];
				widths[0]=6;
				widths[1]=14;
				widths[2]=27;
				widths[3]=25;
				widths[4]=15;
			}
			else if(prefix.equals("CoveringLetterDirectCheques"))
			{
				widths=new int[12];
				widths[0]=5;
				widths[1]=18;
				widths[2]=7;
				widths[3]=5;
				widths[4]=17;
				widths[5]=6;				widths[6]=9;
				widths[7]=10;				widths[8]=18;
				widths[9]=10;				widths[10]=8;
				widths[11]=11;				
			}
			else if(prefix.equals("addressinfo")){
				//System.out.println("inside seting of widths");
				widths =new int[2];
				widths[0]=35;
				widths[1]=60;
			}
			else
			{
				widths=new int[5];
				widths[0]=6;
				widths[1]=14;
				widths[2]=38;
                widths[3]=33;
                widths[4]=20;
			}
			
			aTable.setWidths(widths);
			//System.out.println("after set widths");
		//	aTable.setAutoFillEmptyCells(true);
		//	aTable.setWidth(100);
		//	aTable.setPadding(4);
		//	aTable.setSpacing(0);
		//	aTable.setBackgroundColor(new Color(255,255,255));	
		//	aTable.setCellsFitPage(true);	
		//System.out.println("before heading*********");
		if(prefix.equals("addressinfo")){
			//System.out.println("In create table");
			//System.out.println("data is "+data);
		writeAddress(data,aTable,aligns,wantSno,writer,0,totals);
			//System.out.println("after create address");
		}
		else{
			//System.out.println("in else block");
			writeHeadings(columnnames,aTable,rowspans,colspans,wantSno);
    	//System.out.println("after heading*********");
			writeData(data,aTable,aligns,wantSno,writer,0,totals);

			writeTotals(data,aTable,totals,wantSno);
		}	
			Paragraph tSpace = new Paragraph("  " , FontFactory.getFont(FontFactory.TIMES_ROMAN, 8, com.lowagie.text.Font.NORMAL, new Color(0,0,0)));
			document.add(tSpace);
			
			if(null!=extraFooter)
			{
				writeExtraFooter(extraFooter,aTable,(columnnames[0].length+1));
			}
			
                        
			
			document.add(aTable);
		}
		catch(Exception e)
		{
				e.printStackTrace();
		}
	}

	public void writeExtraFooter(ArrayList extraFooter,PdfPTable aTable,int  colspan)
	{
		try
		{


		PdfPCell hcell = null;
		String prefix ="";
		for(int i=0;i<extraFooter.size();i++)
		{
			ArrayList tempFooter = new ArrayList();
			tempFooter = (ArrayList)extraFooter.get(i);
			for(int j=0;j<tempFooter.size();j++)
			{
				if(j==0)
				{
					prefix="Total No of Cases : ";
				}
				else
				{
					prefix= prefix+"Total Amount in Rs : ";
				}	
			 prefix = prefix + Converter.convert((String)tempFooter.get(j))+" ";
			}
	
		}

			 hcell=new PdfPCell(new Phrase(prefix , FontFactory.getFont(FontFactory.COURIER, 12, com.lowagie.text.Font.NORMAL, new Color(0, 0, 0))));

                                        hcell.setColspan(colspan);
                                        hcell.setBorderColor(new Color(0,0,0));
                                        hcell.setBackgroundColor(new Color(255,255,255));
					hcell.setHorizontalAlignment(Element.ALIGN_CENTER);
                                        aTable.addCell(hcell);
		
		}
		catch(Exception e)
		{
			e.printStackTrace();
		}
	}


//For writing columnnames	
	
	public void writeHeadings(String[][] columnnames,PdfPTable aTable,String[][] rowspans,String[][] colspans,boolean wantSno)
	{
		try{
			PdfPCell hcell=null;
			if(wantSno)
			{
				hcell=new PdfPCell(new Phrase("SNo" , FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.NORMAL, new Color(0,0, 0))));
				hcell.setColspan(1);
			//	hcell.setRowspan(1);
				 hcell.setMinimumHeight(30);
				hcell.setBorderColor(new Color(0,0,0));
				hcell.setBackgroundColor(new Color(213,214,223));	

				hcell.setHorizontalAlignment(Element.ALIGN_CENTER);
				hcell.setVerticalAlignment(Element.ALIGN_MIDDLE);

				aTable.addCell(hcell);
			}
			for(int i=0;i<columnnames.length;i++)
			{
				for(int j=0;j<columnnames[i].length;j++)
				{
					hcell=new PdfPCell(new Phrase(columnnames[i][j].replaceAll("<br>","\n") , FontFactory.getFont(FontFactory.TIMES_ROMAN, 9, com.lowagie.text.Font.NORMAL, new Color(0,0,0))));
					hcell.setColspan(Integer.parseInt(colspans[i][j]));
			//		hcell.setRowspan(Integer.parseInt(rowspans[i][j]));
					 hcell.setMinimumHeight(30);
					hcell.setBorderColor(new Color(0,0,0));
					hcell.setBackgroundColor(new Color(213,214,223));	

					hcell.setHorizontalAlignment(Element.ALIGN_CENTER);
					hcell.setVerticalAlignment(Element.ALIGN_MIDDLE);

					aTable.addCell(hcell);
				}
			}
			
			//aTable.endHeaders();
			aTable.setHeaderRows(1);
		}
		catch(Exception e)
		{
				e.printStackTrace();
		}

	}

//for writing addresses	
public void writeAddress(ArrayList data,PdfPTable aTable,String[] aligns,boolean wantSno,PdfWriter writer,int a,String[] totals)
	{
		
		try{
			PdfPCell hcell=null;
			ArrayList templist;
			
				templist = new ArrayList();
				templist = data;
				//System.out.println("total list********"+templist);
				for(int j=0;j<templist.size();j++)
				{
					//System.out.println("Individual list********"+templist.get(j));
					if(j==1)
					hcell=new PdfPCell(new Phrase(((String)templist.get(j)).replaceAll("<br>","\n") , FontFactory.getFont(FontFactory.TIMES_ROMAN,10, com.lowagie.text.Font.BOLD, new Color(0, 0, 0))));
					else
					hcell=new PdfPCell(new Phrase(((String)templist.get(j)).replaceAll("<br>","\n") , FontFactory.getFont(FontFactory.TIMES_ROMAN,10, com.lowagie.text.Font.NORMAL, new Color(0, 0, 0))));

					hcell.setColspan(1);
					hcell.setBorderColor(new Color(255,255,255));
					hcell.setBackgroundColor(new Color(255,255,255));	
					 hcell.setLeading(14f,0f);
					 hcell.setMinimumHeight(50);
				     hcell.setHorizontalAlignment(Element.ALIGN_LEFT);
                	 aTable.addCell(hcell);
				}
			
					
					

			
		}
		catch(Exception e)
		{
				e.printStackTrace();
		}

	}



















//for writing table data
public void writeData(ArrayList data,PdfPTable aTable,String[] aligns,boolean wantSno,PdfWriter writer,int a,String[] totals)
	{
		
		try{
			PdfPCell hcell=null;
			ArrayList templist;
			
			for(int i=a;i<data.size();i++)
			{
				if(wantSno)
				{
					hcell=new PdfPCell(new Phrase(""+(i+1) , FontFactory.getFont(FontFactory.TIMES_ROMAN,10, com.lowagie.text.Font.NORMAL, new Color(0, 0, 0))));
					hcell.setColspan(1);
					hcell.setBorderColor(new Color(0,0,0));
					hcell.setBackgroundColor(new Color(255,255,255));	
					 hcell.setMinimumHeight(50);
					hcell.setHorizontalAlignment(Element.ALIGN_CENTER);
					 hcell.setLeading(14f,0f);
					aTable.addCell(hcell);
				}
				templist = new ArrayList();
				templist = (ArrayList)data.get(i);
				//System.out.println("total list********"+templist);
				for(int j=0;j<templist.size();j++)
				{
									//System.out.println("Individual list********"+templist.get(j));
					if(totals[j].equals("1"))
						hcell=new PdfPCell(new Phrase(Converter.convert(((String)templist.get(j))).replaceAll("<br>","\n") , FontFactory.getFont(FontFactory.TIMES_ROMAN,10, com.lowagie.text.Font.NORMAL, new Color(0, 0, 0))));
					else
						hcell=new PdfPCell(new Phrase(((String)templist.get(j)).replaceAll("<br>","\n") , FontFactory.getFont(FontFactory.TIMES_ROMAN,10, com.lowagie.text.Font.NORMAL, new Color(0, 0, 0))));		
					hcell.setColspan(1);
	//					hcell.setColspan(Integer.parseInt(colspans[i][j]));
          //                              hcell.setRowspan(Integer.parseInt(rowspans[i][j]));
					hcell.setBorderColor(new Color(0,0,0));
					hcell.setBackgroundColor(new Color(255,255,255));	
					 hcell.setLeading(14f,0f);
					 hcell.setMinimumHeight(50);
					if(aligns[j].equals("0")) 
					hcell.setHorizontalAlignment(Element.ALIGN_CENTER);
	       	                        else if(aligns[j].equals("1"))
					hcell.setHorizontalAlignment(Element.ALIGN_LEFT);
                	                else 
					hcell.setHorizontalAlignment(Element.ALIGN_RIGHT);
					
					aTable.addCell(hcell);
				}
			}

					
					

			
		}
		catch(Exception e)
		{
				e.printStackTrace();
		}

	}
	
	public void loop(com.lowagie.text.Table aTable)
	{
		try
		{
			Cell hcell = null;

					hcell=new Cell(new Phrase("" , FontFactory.getFont(FontFactory.COURIER,9, com.lowagie.text.Font.NORMAL, new Color(0, 0, 0))));
                                        hcell.setColspan(1);
                                      //  hcell.setRowspan(1);
                                        hcell.setBorderColor(new Color(255,255,255));
                                        hcell.setBackgroundColor(new Color(99,00,00));

                                        hcell.setHorizontalAlignment(Element.ALIGN_CENTER);

                                        aTable.addCell(hcell);
		}
		
		
		catch(Exception e)
		{
			e.printStackTrace();
		}
	}

//for writing totals
	public void writeTotals(ArrayList data,PdfPTable  aTable,String[] totals,boolean wantSno)
	{
		ArrayList rowData;
		wantSno = true;
		try
		{
			PdfPCell hcell =null; 
			PdfPCell tcell =null; 
			boolean totFlag=false;
			String[] tot = new String[totals.length];
			
			for(int i=0;i<totals.length;i++)
			{
				tot[i]="0";
				if(!totals[i].equals("0"))
				{
					totFlag=true;
                                
				}

			}
			
			if(totFlag)
			{

				for(int i=0;i<data.size();i++)
				{
					rowData = new ArrayList();
					rowData=(ArrayList)data.get(i);


					for(int j=0;j<rowData.size();j++)
					{
						if(!totals[j].equals("0"))
						{
							tot[j]= ""+(Integer.parseInt((String)rowData.get(j)) + Integer.parseInt(tot[j]))+"";

						}
						else
						{
							tot[j]="";
						}
					}

				}

				
				
				if(wantSno)
				{
					hcell=new PdfPCell(new Phrase("Total" , FontFactory.getFont(FontFactory.TIMES_ROMAN,10, com.lowagie.text.Font.NORMAL, new Color(0,0,0))));
					hcell.setColspan(1);
					hcell.setBorderColor(new Color(0,0,0));
					hcell.setBackgroundColor(new Color(213,214,223));	
					 hcell.setMinimumHeight(20);
					hcell.setHorizontalAlignment(Element.ALIGN_CENTER);

					aTable.addCell(hcell);
				}

				for(int i=0;i<tot.length;i++)
				{
					try
					{
						tcell=new PdfPCell(new Phrase(""+Converter.convert(tot[i]) , FontFactory.getFont(FontFactory.TIMES_ROMAN, 10, com.lowagie.text.Font.NORMAL, new Color(0,0,0))));
						tcell.setColspan(1);
						tcell.setBorderColor(new Color(0,0,0));
						tcell.setBackgroundColor(new Color(213,214,223));	
						 hcell.setMinimumHeight(30);
						tcell.setHorizontalAlignment(Element.ALIGN_CENTER);

						aTable.addCell(tcell);
					}
					catch(Exception e)
					{
						e.printStackTrace();
					}
				}

			}
		}
		catch(Exception e)
		{
				e.printStackTrace();
		}
                

	}
	
	
	public void onOpenDocument(PdfWriter writer, Document document) {
        try {
            
            table = new PdfPTable(2);
            Phrase p = new Phrase();
            tpl = writer.getDirectContent().createTemplate(100, 100);
            tpl.setBoundingBox(new Rectangle(-20, -20, 100, 100));
            helv = BaseFont.createFont("Helvetica", BaseFont.WINANSI, false);
        }
        catch(Exception e) {
            throw new ExceptionConverter(e);
        }
    }    
    
    public void onEndPage(PdfWriter writer, Document document) {
        PdfContentByte cb = writer.getDirectContent();
        cb.saveState();
        table.setTotalWidth(document.right() - document.left());
       
        String text = "Page " + writer.getPageNumber() + " of ";
        float textSize = helv.getWidthPoint(text, 9);
        float textBase = document.bottom() - 20;
        cb.beginText();
        cb.setFontAndSize(helv, 9);
        if ((writer.getPageNumber() & 1) == 1) {

		  cb.setTextMatrix(document.left(), textBase);
	          cb.showText(text);
       		  cb.endText();
        		  cb.addTemplate(tpl, document.left() + textSize, textBase);
        }
        else {
            float adjust = helv.getWidthPoint("0", 9);
	/*
            cb.setTextMatrix(document.left() - textSize - adjust, textBase);
            cb.showText(text);
            cb.endText();
            cb.addTemplate(tpl, document.left() - adjust, textBase);
	*/
            cb.setTextMatrix(document.left(), textBase);
            cb.showText(text);
    	    cb.endText();
            cb.addTemplate(tpl, document.left() + textSize, textBase);
        }
        cb.saveState();
        cb.restoreState();
        
    }
    
    public void onStartPage(PdfWriter writer, Document document) {
        
    }
    
    public void onCloseDocument(PdfWriter writer, Document document) {
       tpl.beginText();
       tpl.setFontAndSize(helv, 9);
       tpl.setTextMatrix(0, 0);
       tpl.showText("" + (writer.getPageNumber() - 1));
       tpl.endText();
    }

	

}

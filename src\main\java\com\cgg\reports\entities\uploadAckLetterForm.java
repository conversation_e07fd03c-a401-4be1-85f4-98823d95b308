package com.cgg.reports.entities;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.Pattern;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

import lombok.Data;

@Data
@Entity
@Table(name = "pr_acknowledgement_letters")
public class uploadAckLetterForm implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    @Column(name = "cmp_no")
    private String cmpNo;
    
    @Column(name = "cno")
    private Integer cno;
    
    @Column(name = "other_const")
    private String otherConst;
    
    @Column(name = "pa_name")
    private String paName;
    
    @Column(name = "pa_mobile_no")
    @Pattern(regexp = "\\d{10}", message = "Mobile number must be exactly 10 digits")
    private String paMobileNo;
    
    @Column(name = "total_cheques")
    private Integer totalCheques;
    
    @Column(name = "ack_letter")
    private String ackLetterPath;

    @Transient
    private MultipartFile ackLetter;
    
    @Column(name = "entered_on", updatable = false)
    private LocalDateTime enteredOn;
    
    @Column(name = "entered_by")
    private String enteredBy;
    
    @Column(name = "entered_ip")
    private String enteredIp;
    
    @DateTimeFormat(pattern = "dd-MM-yyyy")
    @Column(name = "esigned_date")
    private LocalDate esignedDate;

    @Column(name = "is_qr_scanned", columnDefinition = "boolean default false")
    private Boolean isQrScanned;

    @Column(name = "qr_scanned_by")
    private String qrScannedBy;

    @Column(name = "qr_scanned_timestamp")
    private LocalDateTime qrScannedTimestamp;

    @Column(name = "qr_scanned_ip_address")
    private String qrScannedIpAddress;

    @Transient
    @DateTimeFormat(pattern = "dd-MM-yyyy")
    private LocalDate esignedFromDate;

    @Transient
    @DateTimeFormat(pattern = "dd-MM-yyyy")
    private LocalDate esignedToDate;
}

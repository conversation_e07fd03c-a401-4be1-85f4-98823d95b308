package com.cgg.dataentry.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cgg.dataentry.dao.EnhancementLocDetailsEntryDao;
import com.cgg.dataentry.model.EnhancementLocDetails;

@Service
public class EnhancementLocDetailsEntryServiceImpl implements EnhancementLocDetailsEntryService{
	@Autowired
	private EnhancementLocDetailsEntryDao EnhanceLocEntryDao;
	
	public List<EnhancementLocDetails> getRecommendedDetails()throws Exception{
		
		return EnhanceLocEntryDao.getRecommendedDetails();
	}
public List<EnhancementLocDetails> getHospitalList()throws Exception{
		
		return EnhanceLocEntryDao.getHospitalList();
	}
public String saveCmrfDetails(EnhancementLocDetails locEntryForm,
        Map<String, Object> model,HttpServletRequest request) throws Exception{
	System.out.println("service");
	return EnhanceLocEntryDao.saveLocDetails(locEntryForm,model,request);
}
public EnhancementLocDetails getLocLetterData(String locNo,String prevLocNo) throws Exception{
	System.out.println("service");
	return EnhanceLocEntryDao.getLocLetterData(locNo,prevLocNo);
}

public String getLocData(String locTokenNo) throws Exception{
	System.out.println("service");
	return EnhanceLocEntryDao.getLocData(locTokenNo);
}
public String getPreviousLocAmount(String prevLocNo) throws Exception{
	System.out.println("service");
	return EnhanceLocEntryDao.getPreviousLocAmount(prevLocNo);
}
}

package com.cgg.dataentry.service;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import javax.persistence.Tuple;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.cgg.common.CommonUtils;
import com.cgg.common.Response;
import com.cgg.dataentry.dao.UpdateChequeDao;
import com.cgg.dataentry.model.UpdateChequeForm;
import com.cgg.reports.repositories.CMRFAccountReportRepository;

@Service
public class UpdateChequeServiceImpl implements UpdateChequeService {

     @Autowired
	UpdateChequeDao updateChequeDao;
     
    @Autowired
    CMRFAccountReportRepository cMRFAccountReportRepository;
	
     public Map<String, Object> getTodaysCheckHistory(HttpServletRequest request){
    	 Map<String, Object> todaysCheckHistory = updateChequeDao.getTodaysCheckHistory(request);
    	 return todaysCheckHistory;
     }
     
	@Override
	public Response getDetails(UpdateChequeForm request) {
		Response response = new Response();
		try {
		Map<String, Object> details = updateChequeDao.getDetails(request);
		List<Object> chequeHistoryList = (List<Object>) details.get("chequeHistory");
		if(!details.containsKey("chequeDetails") && chequeHistoryList.size()==0) {
			response.setStatus(HttpStatus.NO_CONTENT);
			response.setMessage("No Records Found");
			return response;	
		}
		response.setStatus(HttpStatus.OK);
		response.setData(details);
	}catch (Exception e) {
		e.printStackTrace();
		response.setStatus(HttpStatus.EXPECTATION_FAILED);
		response.setMessage("Something went wrong");
	}
		return response;
	}

	@Override
	public boolean updateChequeDetails(UpdateChequeForm updateChequeForm,HttpServletRequest request) {
		boolean updateChequeDetails = false;
		try {
			updateChequeDetails = updateChequeDao.updateChequeDetails(updateChequeForm,request);
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return updateChequeDetails;
	}

	@Override
	public boolean editChequeDetails(UpdateChequeForm updateChequeForm,HttpServletRequest request) {
		boolean updateChequeDetails = false;
		try {
			updateChequeDetails = updateChequeDao.editChequeDetails(updateChequeForm,request);
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return updateChequeDetails;
	}

	@Override
	public Response getChequeDetailsFromTo(UpdateChequeForm request) {
		Response response = new Response();
		try {
		Map<String, Object> details = updateChequeDao.getChequeDetailsFromTo(request);
		List<Object> chequeHistoryList = (List<Object>) details.get("chequeHistory");
		if(chequeHistoryList.size()==0) {
			response.setStatus(HttpStatus.NO_CONTENT);
			response.setMessage("No Records Found");
			return response;	
		}
		response.setStatus(HttpStatus.OK);
		response.setData(details);
	}catch (Exception e) {
		e.printStackTrace();
		response.setStatus(HttpStatus.EXPECTATION_FAILED);
		response.setMessage("Something went wrong");
	}
		return response;
	}

	@Override
	public Response getUpdChqDtlsForPrint(UpdateChequeForm request, boolean cmrfFlag, boolean dateFlag) {
		Response response = new Response();
		try {
			Map<String, Object> details = updateChequeDao.getUpdChqDtlsForPrint(request,cmrfFlag,dateFlag);
			List<Object> chequeHistoryList = (List<Object>) details.get("chequeHistory");
			if(chequeHistoryList.size()==0) {
				response.setStatus(HttpStatus.NO_CONTENT);
				response.setMessage("No Records Found");
				return response;	
			}
			response.setStatus(HttpStatus.OK);
			response.setData(details);
		}catch (Exception e) {
			e.printStackTrace();
			response.setStatus(HttpStatus.EXPECTATION_FAILED);
			response.setMessage("Something went wrong");
		}
			return response;
	}

	@Override
	public List<UpdateChequeForm> generateBeneficiaryLetters(String cmrfNo, String newChequeNo) throws Exception {
		return updateChequeDao.generateBeneficiaryLetters(cmrfNo, newChequeNo);
	}

	@Override
	public Map<String, Object> updateProceedings(UpdateChequeForm updateChequeForm) throws Exception {
		return updateChequeDao.updateProceedings(updateChequeForm);
	}
	
	
	@Override
	public List getChequeReprintDetails(UpdateChequeForm updateChequeForm) {
		List<Tuple> chequeReprintDetails = cMRFAccountReportRepository.getChequeReprintDetails(updateChequeForm.getDateFrom(), updateChequeForm.getDateTo());
		return CommonUtils.convertTuplesToListMap(chequeReprintDetails);
	}

	@Override
	public boolean checkChequeNoIsExists(String newChequeNo) throws Exception {
		return updateChequeDao.checkChequeNoIsExists(newChequeNo);
	}

}

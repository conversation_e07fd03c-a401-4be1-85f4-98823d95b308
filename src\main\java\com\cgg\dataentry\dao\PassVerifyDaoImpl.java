package com.cgg.dataentry.dao;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Map;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.cgg.common.CommonUtils;
import com.cgg.dataentry.model.PassVerifyForm;

@Repository
public class PassVerifyDaoImpl implements PassVerifyDao{
	@Autowired
	private DataSource dataSource;
	
	@Autowired
	JdbcTemplate jdbcTemlate;
	@Override
	
	
	public String validUser(PassVerifyForm passVerifyForm, Map<String, Object> model) {
		Connection con=null;
		Statement st=null;
		ResultSet rs=null;
		String sql=null;
		String pass=null;
		String msg=null;
		try {
			con = dataSource.getConnection();
			st=con.createStatement();
			
			String SQL = "select password from users where userid='verify'";
			System.out.println("sql---"+SQL);
			rs=st.executeQuery(SQL);
			while(rs.next()) {
				pass=rs.getString(1);
				
			}
			System.out.println("pass--"+pass+"hid---"+passVerifyForm.getHidpassword());
			if(pass.equals(passVerifyForm.getHidpassword())) {
				msg="authorised";
			}else {
				msg="Unauthorised";
			}
			
		}catch(Exception e) {
			e.printStackTrace();
		}
		finally {
			try {
				CommonUtils.closeCon(con, st, rs);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return msg;
		
	}

}

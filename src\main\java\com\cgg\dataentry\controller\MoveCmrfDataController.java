package com.cgg.dataentry.controller;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.cgg.dataentry.model.MoveDataModel;
import com.cgg.dataentry.service.MoveCmrfDataService;

@Controller
@RequestMapping(value="/moveData")
public class MoveCmrfDataController {
	
	@Autowired
	MoveCmrfDataService moveDataService;
	
	@RequestMapping(method=RequestMethod.GET)
	public String moveData(Map<String, Object> model) throws Exception {
		
		MoveDataModel moveData=new MoveDataModel();
		model.put("moveData",moveData);
		return "moveCmrfData";
		
	}
	@RequestMapping(method=RequestMethod.POST)
	public String updateData(@ModelAttribute("moveData") MoveDataModel moveData,Map<String, Object> model,HttpServletRequest request) throws Exception {
		boolean flag=false;
		flag=moveDataService.updateData(moveData,request);
		if(flag) {
			model.put("msg", "Operation Successful");
		}else {
			model.put("msg", "Error While Updating (or) No CMRF Data Found on Specified Date.");
		}
		moveData.setCMRFFrom("");moveData.setCMRFTo("");
		return "moveCmrfData";
		
	}
	
}
